# DeepSeek API 配置指南

## 🚀 为什么选择DeepSeek？

DeepSeek是一个高性能的大语言模型，相比OpenAI具有以下优势：

### ✅ 优势对比
- **💰 成本更低**: DeepSeek的API调用费用比OpenAI便宜很多
- **🚀 性能优秀**: 在中文理解和创意生成方面表现出色
- **🎯 专业优化**: 对图像描述和创意文本生成有很好的优化
- **🔒 数据安全**: 提供更好的数据隐私保护
- **⚡ 响应快速**: API响应速度快，稳定性好

## 📋 配置步骤

### 1. 获取DeepSeek API密钥

1. 访问 [DeepSeek官网](https://platform.deepseek.com/)
2. 注册账号并登录
3. 进入API管理页面
4. 创建新的API密钥
5. 复制生成的API密钥

### 2. 配置环境变量

在项目根目录的 `.env.local` 文件中添加：

```bash
# DeepSeek API Key (推荐使用)
DEEPSEEK_API_KEY=your_actual_deepseek_api_key_here

# OpenAI API Key (备用，可选)
OPENAI_API_KEY=your_openai_api_key_here
```

### 3. 重启应用

```bash
npm run dev
```

## 🔧 技术实现

### API切换逻辑
应用会自动检测可用的API密钥：
1. **优先使用DeepSeek**: 如果配置了有效的DeepSeek API密钥
2. **备用OpenAI**: 如果DeepSeek不可用，使用OpenAI
3. **演示模式**: 如果都没有配置，使用本地演示模式

### 模型配置
```typescript
// 自动选择模型
const model = getModel(); // 'deepseek-chat' 或 'gpt-4'
const provider = getProvider(); // 'deepseek' 或 'openai'
```

### DeepSeek特定优化
```typescript
// DeepSeek专用参数
{
  model: 'deepseek-chat',
  temperature: 0.7,
  max_tokens: 800,
  top_p: 0.9,
  frequency_penalty: 0.1,
  presence_penalty: 0.1
}
```

## 🎨 DeepSeek优化特色

### 1. 中文优化
- 更好的中文语言理解
- 自然的中文表达方式
- 符合中文用户习惯的对话风格

### 2. 创意增强
- 更丰富的艺术描述词汇
- 专业的摄影和绘画术语
- 创意性的视觉元素组合

### 3. 提示词专业化
- 针对AI绘画优化的提示词结构
- 更精确的技术参数建议
- 更高的优化信心度

## 📊 性能对比

| 特性 | DeepSeek | OpenAI GPT-4 |
|------|----------|--------------|
| 中文理解 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| 创意生成 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| 成本效益 | ⭐⭐⭐⭐⭐ | ⭐⭐ |
| 响应速度 | ⭐⭐⭐⭐ | ⭐⭐⭐ |
| 稳定性 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |

## 🔍 使用示例

### 输入示例
```
用户: "我想要一只可爱的小猫坐在花园里"
```

### DeepSeek优化结果
```json
{
  "originalPrompt": "我想要一只可爱的小猫坐在花园里",
  "optimizedPrompt": "adorable cat, expressive eyes, fluffy fur texture, sitting pose in blooming garden, natural sunlight filtering through leaves, warm color palette, cozy outdoor environment, professional pet photography, high resolution, detailed whiskers, soft focus background, 8k quality, award-winning composition",
  "improvements": [
    "DeepSeek优化：增强了宠物摄影专业术语",
    "细化了毛发质感和眼神描述", 
    "优化了花园环境氛围和光照设置",
    "添加了构图和质量增强词汇"
  ],
  "confidence": 0.92,
  "suggestedParameters": {
    "aspectRatio": "16:9",
    "style": "photorealistic", 
    "quality": "ultra-high",
    "model": "deepseek-optimized"
  }
}
```

## 🛠️ 故障排除

### 常见问题

**Q: DeepSeek API调用失败？**
A: 检查API密钥是否正确，确保账户有足够余额

**Q: 如何验证DeepSeek是否正在使用？**
A: 查看浏览器控制台，会显示 "Using deepseek with model: deepseek-chat"

**Q: 可以同时配置两个API吗？**
A: 可以，应用会优先使用DeepSeek，OpenAI作为备用

**Q: DeepSeek的费用如何计算？**
A: 按token计费，具体费用请查看DeepSeek官网定价

### 调试信息
应用会在控制台输出当前使用的AI提供商：
```
Using deepseek with model: deepseek-chat
Optimizing prompt using deepseek with model: deepseek-chat
```

## 📝 注意事项

1. **API密钥安全**: 不要将API密钥提交到代码仓库
2. **费用控制**: 建议设置API使用限额
3. **备用方案**: 保留OpenAI配置作为备用
4. **测试验证**: 配置后测试确保功能正常

## 🎉 开始使用

配置完成后，您就可以享受DeepSeek带来的：
- 更低的使用成本
- 更好的中文体验  
- 更专业的提示词优化
- 更快的响应速度

立即配置您的DeepSeek API密钥，体验升级后的AI图像生成助手！
