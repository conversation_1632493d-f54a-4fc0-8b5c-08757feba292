# AI图像生成对话助手

这是一个基于Next.js构建的对话式AI图像生成助手，可以帮助用户将模糊的图像需求转换为专业的提示词，并优化图像生成效果。

## 功能特性

### 🎯 核心功能
- **智能对话交互**: 通过自然语言对话理解用户的图像生成需求
- **需求澄清**: 自动识别缺失信息并通过友好对话获取更多细节
- **提示词优化**: 将用户描述转换为专业的图像生成提示词
- **实时反馈**: 提供即时的对话响应和需求分析

### 🧠 AI能力
- **意图识别**: 识别用户的图像生成意图（风格、主题、元素等）
- **关键信息提取**: 从对话中提取关键的视觉元素
- **专业优化**: 根据最佳实践优化提示词结构和内容
- **多轮对话管理**: 维护对话上下文和历史记录

### 🎨 支持的图像元素
- **艺术风格**: 写实、卡通、油画、水彩、数字艺术等
- **情绪氛围**: 温暖、冷酷、神秘、浪漫、史诗等
- **光照效果**: 自然光、金色时光、戏剧性光照等
- **构图方式**: 特写、全身、鸟瞰、对称构图等
- **色彩方案**: 智能识别和建议色彩搭配

## 技术架构

- **前端**: React + Next.js 15 + TypeScript
- **UI框架**: Tailwind CSS + Headless UI
- **AI集成**: OpenAI GPT-4 API
- **状态管理**: React Context + useReducer
- **图标**: Heroicons

## 快速开始

### 1. 环境准备

确保您的系统已安装：
- Node.js 18+
- npm 或 yarn

### 2. 安装依赖

```bash
npm install
```

### 3. 配置环境变量

复制 `.env.local` 文件并添加您的API密钥：

```bash
# .env.local

# 推荐使用DeepSeek (成本更低，中文优化更好)
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# 或者使用OpenAI (备用选项)
OPENAI_API_KEY=your_openai_api_key_here
```

**🚀 推荐使用DeepSeek**：
- 💰 成本更低
- 🇨🇳 中文优化更好
- 🎨 创意生成能力强
- ⚡ 响应速度快

详细配置说明请查看 [DeepSeek配置指南](./DEEPSEEK_SETUP.md)

### 4. 启动开发服务器

```bash
npm run dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

## 使用指南

### 基本使用流程

1. **描述需求**: 在输入框中描述您想要生成的图像
   - 例如："我想要一只可爱的小猫坐在花园里"

2. **对话澄清**: AI会询问更多细节来完善需求
   - 风格偏好、色彩方案、构图方式等

3. **获得优化提示词**: 系统会生成专业的提示词
   - 查看优化说明和建议参数
   - 可以手动编辑提示词

4. **复制使用**: 复制优化后的提示词用于图像生成

### 快捷提示

应用提供了常用的快捷提示词，点击即可快速开始对话：
- "一只可爱的小猫"
- "未来城市风景"
- "油画风格的花朵"
- "卡通人物设计"
- "抽象艺术作品"

## 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API路由
│   │   ├── chat/          # 对话处理API
│   │   └── optimize-prompt/ # 提示词优化API
│   └── page.tsx           # 主页面
├── components/            # React组件
│   └── Chat/             # 聊天相关组件
│       ├── ChatInterface.tsx    # 主聊天界面
│       ├── MessageList.tsx      # 消息列表
│       ├── MessageInput.tsx     # 消息输入
│       └── PromptPreview.tsx    # 提示词预览
├── contexts/             # React Context
│   └── ChatContext.tsx   # 聊天状态管理
└── lib/                  # 工具库
    ├── types.ts          # TypeScript类型定义
    ├── openai.ts         # OpenAI API集成
    └── promptOptimizer.ts # 提示词优化逻辑
```

## API说明

### POST /api/chat
处理用户消息并返回AI回复

**请求体**:
```json
{
  "message": "用户输入的消息",
  "conversationHistory": ["历史对话数组"]
}
```

**响应**:
```json
{
  "response": "AI回复",
  "extractedRequirement": "提取的需求信息",
  "keywords": "关键词分析",
  "needsClarification": "是否需要进一步澄清"
}
```

### POST /api/optimize-prompt
优化提示词

**请求体**:
```json
{
  "requirement": "用户需求描述",
  "conversationHistory": ["对话历史"],
  "useAI": true
}
```

## 开发说明

### 添加新的艺术风格

在 `src/lib/promptOptimizer.ts` 中的 `STYLE_MAPPINGS` 对象添加新风格：

```typescript
const STYLE_MAPPINGS = {
  '新风格': 'professional description for the style',
  // ...
};
```

### 自定义优化规则

修改 `generateOptimizedPrompt` 函数来添加自定义的提示词优化逻辑。

## 部署

### Vercel部署（推荐）

1. 将代码推送到GitHub
2. 在Vercel中导入项目
3. 设置环境变量 `OPENAI_API_KEY`
4. 部署完成

### 其他平台

确保设置正确的环境变量并支持Node.js运行时。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 许可证

MIT License
