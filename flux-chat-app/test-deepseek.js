#!/usr/bin/env node

// DeepSeek集成测试脚本
const testDeepSeekIntegration = async () => {
  console.log('🧪 DeepSeek集成测试');
  console.log('='.repeat(50));
  
  const baseUrl = 'http://localhost:3000';
  
  // 测试1: 对话API
  console.log('\n📝 测试1: 对话API (DeepSeek演示模式)');
  try {
    const chatResponse = await fetch(`${baseUrl}/api/chat`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: '我想要一只可爱的小猫坐在花园里',
        conversationHistory: []
      })
    });
    
    if (chatResponse.ok) {
      const chatData = await chatResponse.json();
      console.log('✅ 对话API测试成功');
      console.log('📄 AI回复:', chatData.response.substring(0, 100) + '...');
      console.log('🔍 需求分析:', chatData.needsClarification ? '需要澄清' : '信息充足');
    } else {
      console.log('❌ 对话API测试失败:', chatResponse.status);
    }
  } catch (error) {
    console.log('❌ 对话API测试出错:', error.message);
  }
  
  // 测试2: 提示词优化API (演示模式)
  console.log('\n🎨 测试2: 提示词优化API (DeepSeek演示模式)');
  try {
    const optimizeResponse = await fetch(`${baseUrl}/api/optimize-prompt`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        requirement: '一只可爱的小猫坐在花园里，写实风格，黄昏时分',
        conversationHistory: [
          '我想要一只可爱的小猫',
          '什么风格？',
          '写实风格，坐在花园里，黄昏时分'
        ],
        useAI: false // 使用演示模式
      })
    });
    
    if (optimizeResponse.ok) {
      const optimizeData = await optimizeResponse.json();
      console.log('✅ 提示词优化测试成功');
      console.log('📝 原始提示:', optimizeData.optimization.originalPrompt);
      console.log('🚀 优化提示:', optimizeData.optimization.optimizedPrompt.substring(0, 100) + '...');
      console.log('📊 信心度:', optimizeData.optimization.confidence);
      console.log('💡 改进点:', optimizeData.optimization.improvements.length + '个');
    } else {
      console.log('❌ 提示词优化测试失败:', optimizeResponse.status);
    }
  } catch (error) {
    console.log('❌ 提示词优化测试出错:', error.message);
  }
  
  // 测试3: 多轮对话
  console.log('\n💬 测试3: 多轮对话测试');
  try {
    // 第一轮
    const round1 = await fetch(`${baseUrl}/api/chat`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: '我想要一幅风景画',
        conversationHistory: []
      })
    });
    
    if (round1.ok) {
      const round1Data = await round1.json();
      console.log('✅ 第一轮对话成功');
      
      // 第二轮
      const round2 = await fetch(`${baseUrl}/api/chat`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: '山水画，中国风，水墨画风格',
          conversationHistory: [
            '我想要一幅风景画',
            round1Data.response
          ]
        })
      });
      
      if (round2.ok) {
        const round2Data = await round2.json();
        console.log('✅ 第二轮对话成功');
        console.log('📄 AI回复:', round2Data.response.substring(0, 80) + '...');
      }
    }
  } catch (error) {
    console.log('❌ 多轮对话测试出错:', error.message);
  }
  
  // 测试4: DeepSeek风格优化
  console.log('\n🎯 测试4: DeepSeek风格优化测试');
  try {
    const deepseekOptimize = await fetch(`${baseUrl}/api/optimize-prompt`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        requirement: '未来科幻城市，赛博朋克风格，夜晚，霓虹灯',
        conversationHistory: [
          '我想要未来城市',
          '什么风格？',
          '赛博朋克风格，夜晚，霓虹灯'
        ],
        useAI: false
      })
    });
    
    if (deepseekOptimize.ok) {
      const data = await deepseekOptimize.json();
      console.log('✅ DeepSeek风格优化成功');
      console.log('🎨 优化特色:', data.optimization.improvements.filter(imp => 
        imp.includes('DeepSeek')).length + '个DeepSeek特色优化');
      console.log('📊 信心度:', data.optimization.confidence);
      console.log('⚙️ 建议参数:', JSON.stringify(data.optimization.suggestedParameters));
    }
  } catch (error) {
    console.log('❌ DeepSeek风格优化测试出错:', error.message);
  }
  
  // 环境检查
  console.log('\n🔧 环境配置检查');
  console.log('📍 API端点:', baseUrl);
  console.log('🔑 DeepSeek密钥:', process.env.DEEPSEEK_API_KEY ? '已配置' : '未配置');
  console.log('🔑 OpenAI密钥:', process.env.OPENAI_API_KEY ? '已配置' : '未配置');
  
  if (!process.env.DEEPSEEK_API_KEY && !process.env.OPENAI_API_KEY) {
    console.log('ℹ️  当前运行在演示模式');
  } else if (process.env.DEEPSEEK_API_KEY && process.env.DEEPSEEK_API_KEY !== 'your_deepseek_api_key_here') {
    console.log('🚀 DeepSeek API已配置，可使用完整功能');
  } else if (process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'your_openai_api_key_here') {
    console.log('🔄 OpenAI API已配置，作为备用选项');
  }
  
  console.log('\n✨ 测试完成！');
  console.log('\n📖 使用说明:');
  console.log('1. 访问 http://localhost:3000 查看主页');
  console.log('2. 访问 http://localhost:3000/chat 开始对话');
  console.log('3. 配置DeepSeek API密钥以获得最佳体验');
  console.log('4. 查看 DEEPSEEK_SETUP.md 了解详细配置');
};

// 运行测试
testDeepSeekIntegration().catch(console.error);
