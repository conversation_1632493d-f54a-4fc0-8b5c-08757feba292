#!/usr/bin/env node

// 简单的DeepSeek验证脚本
const verifyDeepSeek = async () => {
  console.log('🔍 DeepSeek配置验证');
  console.log('='.repeat(30));
  
  // 检查环境变量
  const fs = require('fs');
  const path = require('path');
  
  try {
    const envPath = path.join(__dirname, '.env.local');
    const envContent = fs.readFileSync(envPath, 'utf8');
    
    if (envContent.includes('sk-2c934eb6e8794edfb2b959b56cb0f343')) {
      console.log('✅ DeepSeek API密钥已正确配置');
    } else {
      console.log('❌ DeepSeek API密钥未找到');
    }
    
    console.log('\n📋 环境变量内容:');
    console.log(envContent);
    
  } catch (error) {
    console.log('❌ 无法读取.env.local文件:', error.message);
  }
  
  // 测试API调用
  console.log('\n🧪 测试API调用...');
  
  try {
    const response = await fetch('http://localhost:3000/api/chat', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: '测试DeepSeek',
        conversationHistory: []
      })
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ API调用成功');
      console.log('📝 回复长度:', data.response.length, '字符');
    } else {
      console.log('❌ API调用失败:', response.status);
    }
  } catch (error) {
    console.log('❌ API调用出错:', error.message);
  }
  
  console.log('\n🎉 验证完成！');
  console.log('💡 提示: 查看开发服务器日志确认是否显示 "Using deepseek"');
};

verifyDeepSeek().catch(console.error);
