#!/usr/bin/env node

// 图像生成功能测试脚本
const testImageGeneration = async () => {
  console.log('🎨 图像生成模块测试');
  console.log('='.repeat(50));
  
  const baseUrl = 'http://localhost:3000';
  
  // 测试1: 获取可用模型和配置
  console.log('\n📋 测试1: 获取可用模型和配置');
  try {
    const configResponse = await fetch(`${baseUrl}/api/generate-image`);
    
    if (configResponse.ok) {
      const config = await configResponse.json();
      console.log('✅ 配置获取成功');
      console.log('🤖 可用模型:', Object.keys(config.models).join(', '));
      console.log('📐 支持尺寸:', Object.keys(config.sizes).join(', '));
      console.log('🎨 风格预设:', config.style_presets.join(', '));
      console.log('⚙️ 限制:', JSON.stringify(config.limits));
    } else {
      console.log('❌ 配置获取失败:', configResponse.status);
    }
  } catch (error) {
    console.log('❌ 配置获取出错:', error.message);
  }
  
  // 测试2: 图像生成API (演示模式)
  console.log('\n🎨 测试2: 图像生成API (演示模式)');
  try {
    const generateResponse = await fetch(`${baseUrl}/api/generate-image`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        prompt: 'a cute cat sitting in a garden',
        model: 'flux-schnell',
        size: 'landscape',
        num_images: 1
      })
    });
    
    const generateData = await generateResponse.json();
    
    if (generateResponse.ok && generateData.success) {
      console.log('✅ 图像生成成功');
      console.log('🖼️ 生成图像数量:', generateData.images.length);
      console.log('🤖 使用模型:', generateData.metadata.model_used);
      console.log('⏱️ 生成时间:', generateData.metadata.generation_time + 'ms');
    } else {
      console.log('ℹ️ 图像生成需要API密钥');
      console.log('📝 错误信息:', generateData.error);
      console.log('🎭 演示模式:', generateData.demo_mode ? '是' : '否');
    }
  } catch (error) {
    console.log('❌ 图像生成测试出错:', error.message);
  }
  
  // 测试3: 完整工作流测试
  console.log('\n🔄 测试3: 完整工作流测试');
  try {
    // 步骤1: 对话
    console.log('📝 步骤1: 发起对话');
    const chatResponse = await fetch(`${baseUrl}/api/chat`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: '我想要一只可爱的小猫坐在花园里',
        conversationHistory: []
      })
    });
    
    if (chatResponse.ok) {
      const chatData = await chatResponse.json();
      console.log('✅ 对话成功');
      console.log('💬 AI回复长度:', chatData.response.length, '字符');
      
      // 步骤2: 提示词优化
      console.log('\n🚀 步骤2: 提示词优化');
      const optimizeResponse = await fetch(`${baseUrl}/api/optimize-prompt`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          requirement: '一只可爱的小猫坐在花园里，写实风格，黄昏时分',
          conversationHistory: [
            '我想要一只可爱的小猫坐在花园里',
            chatData.response
          ],
          useAI: true
        })
      });
      
      if (optimizeResponse.ok) {
        const optimizeData = await optimizeResponse.json();
        console.log('✅ 提示词优化成功');
        console.log('📝 优化提示词长度:', optimizeData.optimization.optimizedPrompt.length, '字符');
        console.log('📊 信心度:', optimizeData.optimization.confidence);
        console.log('💡 改进数量:', optimizeData.optimization.improvements.length, '个');
        
        // 步骤3: 图像生成 (演示)
        console.log('\n🎨 步骤3: 图像生成 (演示)');
        const imageResponse = await fetch(`${baseUrl}/api/generate-image`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            prompt: optimizeData.optimization.optimizedPrompt,
            model: 'flux-schnell',
            size: 'landscape',
            num_images: 1,
            style_preset: 'photorealistic'
          })
        });
        
        const imageData = await imageResponse.json();
        
        if (imageData.demo_mode) {
          console.log('ℹ️ 演示模式: 需要配置FAL API密钥才能实际生成图像');
          console.log('📋 配置说明: 查看 FAL_SETUP.md');
        } else if (imageData.success) {
          console.log('✅ 图像生成成功');
          console.log('🖼️ 图像URL:', imageData.images[0].url);
        } else {
          console.log('❌ 图像生成失败:', imageData.error);
        }
      }
    }
  } catch (error) {
    console.log('❌ 完整工作流测试出错:', error.message);
  }
  
  // 测试4: 参数验证
  console.log('\n🔍 测试4: 参数验证');
  try {
    // 测试无效提示词
    const invalidResponse = await fetch(`${baseUrl}/api/generate-image`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        // 缺少prompt
        model: 'flux-schnell'
      })
    });
    
    if (invalidResponse.status === 400) {
      console.log('✅ 参数验证正常工作');
    } else {
      console.log('❌ 参数验证可能有问题');
    }
  } catch (error) {
    console.log('❌ 参数验证测试出错:', error.message);
  }
  
  // 环境检查
  console.log('\n🔧 环境配置检查');
  console.log('📍 API端点:', baseUrl);
  console.log('🔑 FAL API密钥:', process.env.FAL_API_KEY ? '已配置' : '未配置');
  console.log('🔑 DeepSeek密钥:', process.env.DEEPSEEK_API_KEY ? '已配置' : '未配置');
  
  if (!process.env.FAL_API_KEY) {
    console.log('\n💡 配置建议:');
    console.log('1. 获取FAL.AI API密钥: https://fal.ai/dashboard/keys');
    console.log('2. 添加到 .env.local: FAL_API_KEY=your_key_here');
    console.log('3. 重启开发服务器');
    console.log('4. 查看详细配置: FAL_SETUP.md');
  } else {
    console.log('🚀 FAL API已配置，可使用完整图像生成功能');
  }
  
  console.log('\n✨ 测试完成！');
  console.log('\n📖 使用说明:');
  console.log('1. 访问 http://localhost:3000/chat 开始对话');
  console.log('2. 描述想要的图像，AI会优化提示词');
  console.log('3. 点击"生成图像"按钮创建图像');
  console.log('4. 配置FAL API密钥以获得完整功能');
};

// 运行测试
testImageGeneration().catch(console.error);
