"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@msgpack";
exports.ids = ["vendor-chunks/@msgpack"];
exports.modules = {

/***/ "(rsc)/./node_modules/@msgpack/msgpack/dist.esm/CachedKeyDecoder.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/CachedKeyDecoder.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CachedKeyDecoder: () => (/* binding */ CachedKeyDecoder)\n/* harmony export */ });\n/* harmony import */ var _utils_utf8_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/utf8.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/utils/utf8.mjs\");\n\nconst DEFAULT_MAX_KEY_LENGTH = 16;\nconst DEFAULT_MAX_LENGTH_PER_KEY = 16;\nclass CachedKeyDecoder {\n    constructor(maxKeyLength = DEFAULT_MAX_KEY_LENGTH, maxLengthPerKey = DEFAULT_MAX_LENGTH_PER_KEY) {\n        this.hit = 0;\n        this.miss = 0;\n        this.maxKeyLength = maxKeyLength;\n        this.maxLengthPerKey = maxLengthPerKey;\n        // avoid `new Array(N)`, which makes a sparse array,\n        // because a sparse array is typically slower than a non-sparse array.\n        this.caches = [];\n        for (let i = 0; i < this.maxKeyLength; i++) {\n            this.caches.push([]);\n        }\n    }\n    canBeCached(byteLength) {\n        return byteLength > 0 && byteLength <= this.maxKeyLength;\n    }\n    find(bytes, inputOffset, byteLength) {\n        const records = this.caches[byteLength - 1];\n        FIND_CHUNK: for (const record of records) {\n            const recordBytes = record.bytes;\n            for (let j = 0; j < byteLength; j++) {\n                if (recordBytes[j] !== bytes[inputOffset + j]) {\n                    continue FIND_CHUNK;\n                }\n            }\n            return record.str;\n        }\n        return null;\n    }\n    store(bytes, value) {\n        const records = this.caches[bytes.length - 1];\n        const record = { bytes, str: value };\n        if (records.length >= this.maxLengthPerKey) {\n            // `records` are full!\n            // Set `record` to an arbitrary position.\n            records[(Math.random() * records.length) | 0] = record;\n        }\n        else {\n            records.push(record);\n        }\n    }\n    decode(bytes, inputOffset, byteLength) {\n        const cachedValue = this.find(bytes, inputOffset, byteLength);\n        if (cachedValue != null) {\n            this.hit++;\n            return cachedValue;\n        }\n        this.miss++;\n        const str = (0,_utils_utf8_mjs__WEBPACK_IMPORTED_MODULE_0__.utf8DecodeJs)(bytes, inputOffset, byteLength);\n        // Ensure to copy a slice of bytes because the bytes may be a NodeJS Buffer and Buffer#slice() returns a reference to its internal ArrayBuffer.\n        const slicedCopyOfBytes = Uint8Array.prototype.slice.call(bytes, inputOffset, inputOffset + byteLength);\n        this.store(slicedCopyOfBytes, str);\n        return str;\n    }\n}\n//# sourceMappingURL=CachedKeyDecoder.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@msgpack/msgpack/dist.esm/CachedKeyDecoder.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/@msgpack/msgpack/dist.esm/DecodeError.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/DecodeError.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DecodeError: () => (/* binding */ DecodeError)\n/* harmony export */ });\nclass DecodeError extends Error {\n    constructor(message) {\n        super(message);\n        // fix the prototype chain in a cross-platform way\n        const proto = Object.create(DecodeError.prototype);\n        Object.setPrototypeOf(this, proto);\n        Object.defineProperty(this, \"name\", {\n            configurable: true,\n            enumerable: false,\n            value: DecodeError.name,\n        });\n    }\n}\n//# sourceMappingURL=DecodeError.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQG1zZ3BhY2svbXNncGFjay9kaXN0LmVzbS9EZWNvZGVFcnJvci5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2xpdXBlbmcvY3Vyc29yL+i/m+mYti9GbHV45a+56K+d5byP5bqU55SoL2ZsdXgtY2hhdC1hcHAvbm9kZV9tb2R1bGVzL0Btc2dwYWNrL21zZ3BhY2svZGlzdC5lc20vRGVjb2RlRXJyb3IubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBjbGFzcyBEZWNvZGVFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgICBjb25zdHJ1Y3RvcihtZXNzYWdlKSB7XG4gICAgICAgIHN1cGVyKG1lc3NhZ2UpO1xuICAgICAgICAvLyBmaXggdGhlIHByb3RvdHlwZSBjaGFpbiBpbiBhIGNyb3NzLXBsYXRmb3JtIHdheVxuICAgICAgICBjb25zdCBwcm90byA9IE9iamVjdC5jcmVhdGUoRGVjb2RlRXJyb3IucHJvdG90eXBlKTtcbiAgICAgICAgT2JqZWN0LnNldFByb3RvdHlwZU9mKHRoaXMsIHByb3RvKTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRoaXMsIFwibmFtZVwiLCB7XG4gICAgICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgICAgICBlbnVtZXJhYmxlOiBmYWxzZSxcbiAgICAgICAgICAgIHZhbHVlOiBEZWNvZGVFcnJvci5uYW1lLFxuICAgICAgICB9KTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1EZWNvZGVFcnJvci5tanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@msgpack/msgpack/dist.esm/DecodeError.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/@msgpack/msgpack/dist.esm/Decoder.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/Decoder.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Decoder: () => (/* binding */ Decoder)\n/* harmony export */ });\n/* harmony import */ var _utils_prettyByte_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/prettyByte.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/utils/prettyByte.mjs\");\n/* harmony import */ var _ExtensionCodec_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ExtensionCodec.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/ExtensionCodec.mjs\");\n/* harmony import */ var _utils_int_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/int.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/utils/int.mjs\");\n/* harmony import */ var _utils_utf8_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils/utf8.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/utils/utf8.mjs\");\n/* harmony import */ var _utils_typedArrays_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/typedArrays.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/utils/typedArrays.mjs\");\n/* harmony import */ var _CachedKeyDecoder_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CachedKeyDecoder.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/CachedKeyDecoder.mjs\");\n/* harmony import */ var _DecodeError_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./DecodeError.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/DecodeError.mjs\");\n\n\n\n\n\n\n\nconst STATE_ARRAY = \"array\";\nconst STATE_MAP_KEY = \"map_key\";\nconst STATE_MAP_VALUE = \"map_value\";\nconst mapKeyConverter = (key) => {\n    if (typeof key === \"string\" || typeof key === \"number\") {\n        return key;\n    }\n    throw new _DecodeError_mjs__WEBPACK_IMPORTED_MODULE_0__.DecodeError(\"The type of key must be string or number but \" + typeof key);\n};\nclass StackPool {\n    constructor() {\n        this.stack = [];\n        this.stackHeadPosition = -1;\n    }\n    get length() {\n        return this.stackHeadPosition + 1;\n    }\n    top() {\n        return this.stack[this.stackHeadPosition];\n    }\n    pushArrayState(size) {\n        const state = this.getUninitializedStateFromPool();\n        state.type = STATE_ARRAY;\n        state.position = 0;\n        state.size = size;\n        state.array = new Array(size);\n    }\n    pushMapState(size) {\n        const state = this.getUninitializedStateFromPool();\n        state.type = STATE_MAP_KEY;\n        state.readCount = 0;\n        state.size = size;\n        state.map = {};\n    }\n    getUninitializedStateFromPool() {\n        this.stackHeadPosition++;\n        if (this.stackHeadPosition === this.stack.length) {\n            const partialState = {\n                type: undefined,\n                size: 0,\n                array: undefined,\n                position: 0,\n                readCount: 0,\n                map: undefined,\n                key: null,\n            };\n            this.stack.push(partialState);\n        }\n        return this.stack[this.stackHeadPosition];\n    }\n    release(state) {\n        const topStackState = this.stack[this.stackHeadPosition];\n        if (topStackState !== state) {\n            throw new Error(\"Invalid stack state. Released state is not on top of the stack.\");\n        }\n        if (state.type === STATE_ARRAY) {\n            const partialState = state;\n            partialState.size = 0;\n            partialState.array = undefined;\n            partialState.position = 0;\n            partialState.type = undefined;\n        }\n        if (state.type === STATE_MAP_KEY || state.type === STATE_MAP_VALUE) {\n            const partialState = state;\n            partialState.size = 0;\n            partialState.map = undefined;\n            partialState.readCount = 0;\n            partialState.type = undefined;\n        }\n        this.stackHeadPosition--;\n    }\n    reset() {\n        this.stack.length = 0;\n        this.stackHeadPosition = -1;\n    }\n}\nconst HEAD_BYTE_REQUIRED = -1;\nconst EMPTY_VIEW = new DataView(new ArrayBuffer(0));\nconst EMPTY_BYTES = new Uint8Array(EMPTY_VIEW.buffer);\ntry {\n    // IE11: The spec says it should throw RangeError,\n    // IE11: but in IE11 it throws TypeError.\n    EMPTY_VIEW.getInt8(0);\n}\ncatch (e) {\n    if (!(e instanceof RangeError)) {\n        throw new Error(\"This module is not supported in the current JavaScript engine because DataView does not throw RangeError on out-of-bounds access\");\n    }\n}\nconst MORE_DATA = new RangeError(\"Insufficient data\");\nconst sharedCachedKeyDecoder = new _CachedKeyDecoder_mjs__WEBPACK_IMPORTED_MODULE_1__.CachedKeyDecoder();\nclass Decoder {\n    constructor(options) {\n        this.totalPos = 0;\n        this.pos = 0;\n        this.view = EMPTY_VIEW;\n        this.bytes = EMPTY_BYTES;\n        this.headByte = HEAD_BYTE_REQUIRED;\n        this.stack = new StackPool();\n        this.entered = false;\n        this.extensionCodec = options?.extensionCodec ?? _ExtensionCodec_mjs__WEBPACK_IMPORTED_MODULE_2__.ExtensionCodec.defaultCodec;\n        this.context = options?.context; // needs a type assertion because EncoderOptions has no context property when ContextType is undefined\n        this.useBigInt64 = options?.useBigInt64 ?? false;\n        this.rawStrings = options?.rawStrings ?? false;\n        this.maxStrLength = options?.maxStrLength ?? _utils_int_mjs__WEBPACK_IMPORTED_MODULE_3__.UINT32_MAX;\n        this.maxBinLength = options?.maxBinLength ?? _utils_int_mjs__WEBPACK_IMPORTED_MODULE_3__.UINT32_MAX;\n        this.maxArrayLength = options?.maxArrayLength ?? _utils_int_mjs__WEBPACK_IMPORTED_MODULE_3__.UINT32_MAX;\n        this.maxMapLength = options?.maxMapLength ?? _utils_int_mjs__WEBPACK_IMPORTED_MODULE_3__.UINT32_MAX;\n        this.maxExtLength = options?.maxExtLength ?? _utils_int_mjs__WEBPACK_IMPORTED_MODULE_3__.UINT32_MAX;\n        this.keyDecoder = options?.keyDecoder !== undefined ? options.keyDecoder : sharedCachedKeyDecoder;\n        this.mapKeyConverter = options?.mapKeyConverter ?? mapKeyConverter;\n    }\n    clone() {\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n        return new Decoder({\n            extensionCodec: this.extensionCodec,\n            context: this.context,\n            useBigInt64: this.useBigInt64,\n            rawStrings: this.rawStrings,\n            maxStrLength: this.maxStrLength,\n            maxBinLength: this.maxBinLength,\n            maxArrayLength: this.maxArrayLength,\n            maxMapLength: this.maxMapLength,\n            maxExtLength: this.maxExtLength,\n            keyDecoder: this.keyDecoder,\n        });\n    }\n    reinitializeState() {\n        this.totalPos = 0;\n        this.headByte = HEAD_BYTE_REQUIRED;\n        this.stack.reset();\n        // view, bytes, and pos will be re-initialized in setBuffer()\n    }\n    setBuffer(buffer) {\n        const bytes = (0,_utils_typedArrays_mjs__WEBPACK_IMPORTED_MODULE_4__.ensureUint8Array)(buffer);\n        this.bytes = bytes;\n        this.view = new DataView(bytes.buffer, bytes.byteOffset, bytes.byteLength);\n        this.pos = 0;\n    }\n    appendBuffer(buffer) {\n        if (this.headByte === HEAD_BYTE_REQUIRED && !this.hasRemaining(1)) {\n            this.setBuffer(buffer);\n        }\n        else {\n            const remainingData = this.bytes.subarray(this.pos);\n            const newData = (0,_utils_typedArrays_mjs__WEBPACK_IMPORTED_MODULE_4__.ensureUint8Array)(buffer);\n            // concat remainingData + newData\n            const newBuffer = new Uint8Array(remainingData.length + newData.length);\n            newBuffer.set(remainingData);\n            newBuffer.set(newData, remainingData.length);\n            this.setBuffer(newBuffer);\n        }\n    }\n    hasRemaining(size) {\n        return this.view.byteLength - this.pos >= size;\n    }\n    createExtraByteError(posToShow) {\n        const { view, pos } = this;\n        return new RangeError(`Extra ${view.byteLength - pos} of ${view.byteLength} byte(s) found at buffer[${posToShow}]`);\n    }\n    /**\n     * @throws {@link DecodeError}\n     * @throws {@link RangeError}\n     */\n    decode(buffer) {\n        if (this.entered) {\n            const instance = this.clone();\n            return instance.decode(buffer);\n        }\n        try {\n            this.entered = true;\n            this.reinitializeState();\n            this.setBuffer(buffer);\n            const object = this.doDecodeSync();\n            if (this.hasRemaining(1)) {\n                throw this.createExtraByteError(this.pos);\n            }\n            return object;\n        }\n        finally {\n            this.entered = false;\n        }\n    }\n    *decodeMulti(buffer) {\n        if (this.entered) {\n            const instance = this.clone();\n            yield* instance.decodeMulti(buffer);\n            return;\n        }\n        try {\n            this.entered = true;\n            this.reinitializeState();\n            this.setBuffer(buffer);\n            while (this.hasRemaining(1)) {\n                yield this.doDecodeSync();\n            }\n        }\n        finally {\n            this.entered = false;\n        }\n    }\n    async decodeAsync(stream) {\n        if (this.entered) {\n            const instance = this.clone();\n            return instance.decodeAsync(stream);\n        }\n        try {\n            this.entered = true;\n            let decoded = false;\n            let object;\n            for await (const buffer of stream) {\n                if (decoded) {\n                    this.entered = false;\n                    throw this.createExtraByteError(this.totalPos);\n                }\n                this.appendBuffer(buffer);\n                try {\n                    object = this.doDecodeSync();\n                    decoded = true;\n                }\n                catch (e) {\n                    if (!(e instanceof RangeError)) {\n                        throw e; // rethrow\n                    }\n                    // fallthrough\n                }\n                this.totalPos += this.pos;\n            }\n            if (decoded) {\n                if (this.hasRemaining(1)) {\n                    throw this.createExtraByteError(this.totalPos);\n                }\n                return object;\n            }\n            const { headByte, pos, totalPos } = this;\n            throw new RangeError(`Insufficient data in parsing ${(0,_utils_prettyByte_mjs__WEBPACK_IMPORTED_MODULE_5__.prettyByte)(headByte)} at ${totalPos} (${pos} in the current buffer)`);\n        }\n        finally {\n            this.entered = false;\n        }\n    }\n    decodeArrayStream(stream) {\n        return this.decodeMultiAsync(stream, true);\n    }\n    decodeStream(stream) {\n        return this.decodeMultiAsync(stream, false);\n    }\n    async *decodeMultiAsync(stream, isArray) {\n        if (this.entered) {\n            const instance = this.clone();\n            yield* instance.decodeMultiAsync(stream, isArray);\n            return;\n        }\n        try {\n            this.entered = true;\n            let isArrayHeaderRequired = isArray;\n            let arrayItemsLeft = -1;\n            for await (const buffer of stream) {\n                if (isArray && arrayItemsLeft === 0) {\n                    throw this.createExtraByteError(this.totalPos);\n                }\n                this.appendBuffer(buffer);\n                if (isArrayHeaderRequired) {\n                    arrayItemsLeft = this.readArraySize();\n                    isArrayHeaderRequired = false;\n                    this.complete();\n                }\n                try {\n                    while (true) {\n                        yield this.doDecodeSync();\n                        if (--arrayItemsLeft === 0) {\n                            break;\n                        }\n                    }\n                }\n                catch (e) {\n                    if (!(e instanceof RangeError)) {\n                        throw e; // rethrow\n                    }\n                    // fallthrough\n                }\n                this.totalPos += this.pos;\n            }\n        }\n        finally {\n            this.entered = false;\n        }\n    }\n    doDecodeSync() {\n        DECODE: while (true) {\n            const headByte = this.readHeadByte();\n            let object;\n            if (headByte >= 0xe0) {\n                // negative fixint (111x xxxx) 0xe0 - 0xff\n                object = headByte - 0x100;\n            }\n            else if (headByte < 0xc0) {\n                if (headByte < 0x80) {\n                    // positive fixint (0xxx xxxx) 0x00 - 0x7f\n                    object = headByte;\n                }\n                else if (headByte < 0x90) {\n                    // fixmap (1000 xxxx) 0x80 - 0x8f\n                    const size = headByte - 0x80;\n                    if (size !== 0) {\n                        this.pushMapState(size);\n                        this.complete();\n                        continue DECODE;\n                    }\n                    else {\n                        object = {};\n                    }\n                }\n                else if (headByte < 0xa0) {\n                    // fixarray (1001 xxxx) 0x90 - 0x9f\n                    const size = headByte - 0x90;\n                    if (size !== 0) {\n                        this.pushArrayState(size);\n                        this.complete();\n                        continue DECODE;\n                    }\n                    else {\n                        object = [];\n                    }\n                }\n                else {\n                    // fixstr (101x xxxx) 0xa0 - 0xbf\n                    const byteLength = headByte - 0xa0;\n                    object = this.decodeString(byteLength, 0);\n                }\n            }\n            else if (headByte === 0xc0) {\n                // nil\n                object = null;\n            }\n            else if (headByte === 0xc2) {\n                // false\n                object = false;\n            }\n            else if (headByte === 0xc3) {\n                // true\n                object = true;\n            }\n            else if (headByte === 0xca) {\n                // float 32\n                object = this.readF32();\n            }\n            else if (headByte === 0xcb) {\n                // float 64\n                object = this.readF64();\n            }\n            else if (headByte === 0xcc) {\n                // uint 8\n                object = this.readU8();\n            }\n            else if (headByte === 0xcd) {\n                // uint 16\n                object = this.readU16();\n            }\n            else if (headByte === 0xce) {\n                // uint 32\n                object = this.readU32();\n            }\n            else if (headByte === 0xcf) {\n                // uint 64\n                if (this.useBigInt64) {\n                    object = this.readU64AsBigInt();\n                }\n                else {\n                    object = this.readU64();\n                }\n            }\n            else if (headByte === 0xd0) {\n                // int 8\n                object = this.readI8();\n            }\n            else if (headByte === 0xd1) {\n                // int 16\n                object = this.readI16();\n            }\n            else if (headByte === 0xd2) {\n                // int 32\n                object = this.readI32();\n            }\n            else if (headByte === 0xd3) {\n                // int 64\n                if (this.useBigInt64) {\n                    object = this.readI64AsBigInt();\n                }\n                else {\n                    object = this.readI64();\n                }\n            }\n            else if (headByte === 0xd9) {\n                // str 8\n                const byteLength = this.lookU8();\n                object = this.decodeString(byteLength, 1);\n            }\n            else if (headByte === 0xda) {\n                // str 16\n                const byteLength = this.lookU16();\n                object = this.decodeString(byteLength, 2);\n            }\n            else if (headByte === 0xdb) {\n                // str 32\n                const byteLength = this.lookU32();\n                object = this.decodeString(byteLength, 4);\n            }\n            else if (headByte === 0xdc) {\n                // array 16\n                const size = this.readU16();\n                if (size !== 0) {\n                    this.pushArrayState(size);\n                    this.complete();\n                    continue DECODE;\n                }\n                else {\n                    object = [];\n                }\n            }\n            else if (headByte === 0xdd) {\n                // array 32\n                const size = this.readU32();\n                if (size !== 0) {\n                    this.pushArrayState(size);\n                    this.complete();\n                    continue DECODE;\n                }\n                else {\n                    object = [];\n                }\n            }\n            else if (headByte === 0xde) {\n                // map 16\n                const size = this.readU16();\n                if (size !== 0) {\n                    this.pushMapState(size);\n                    this.complete();\n                    continue DECODE;\n                }\n                else {\n                    object = {};\n                }\n            }\n            else if (headByte === 0xdf) {\n                // map 32\n                const size = this.readU32();\n                if (size !== 0) {\n                    this.pushMapState(size);\n                    this.complete();\n                    continue DECODE;\n                }\n                else {\n                    object = {};\n                }\n            }\n            else if (headByte === 0xc4) {\n                // bin 8\n                const size = this.lookU8();\n                object = this.decodeBinary(size, 1);\n            }\n            else if (headByte === 0xc5) {\n                // bin 16\n                const size = this.lookU16();\n                object = this.decodeBinary(size, 2);\n            }\n            else if (headByte === 0xc6) {\n                // bin 32\n                const size = this.lookU32();\n                object = this.decodeBinary(size, 4);\n            }\n            else if (headByte === 0xd4) {\n                // fixext 1\n                object = this.decodeExtension(1, 0);\n            }\n            else if (headByte === 0xd5) {\n                // fixext 2\n                object = this.decodeExtension(2, 0);\n            }\n            else if (headByte === 0xd6) {\n                // fixext 4\n                object = this.decodeExtension(4, 0);\n            }\n            else if (headByte === 0xd7) {\n                // fixext 8\n                object = this.decodeExtension(8, 0);\n            }\n            else if (headByte === 0xd8) {\n                // fixext 16\n                object = this.decodeExtension(16, 0);\n            }\n            else if (headByte === 0xc7) {\n                // ext 8\n                const size = this.lookU8();\n                object = this.decodeExtension(size, 1);\n            }\n            else if (headByte === 0xc8) {\n                // ext 16\n                const size = this.lookU16();\n                object = this.decodeExtension(size, 2);\n            }\n            else if (headByte === 0xc9) {\n                // ext 32\n                const size = this.lookU32();\n                object = this.decodeExtension(size, 4);\n            }\n            else {\n                throw new _DecodeError_mjs__WEBPACK_IMPORTED_MODULE_0__.DecodeError(`Unrecognized type byte: ${(0,_utils_prettyByte_mjs__WEBPACK_IMPORTED_MODULE_5__.prettyByte)(headByte)}`);\n            }\n            this.complete();\n            const stack = this.stack;\n            while (stack.length > 0) {\n                // arrays and maps\n                const state = stack.top();\n                if (state.type === STATE_ARRAY) {\n                    state.array[state.position] = object;\n                    state.position++;\n                    if (state.position === state.size) {\n                        object = state.array;\n                        stack.release(state);\n                    }\n                    else {\n                        continue DECODE;\n                    }\n                }\n                else if (state.type === STATE_MAP_KEY) {\n                    if (object === \"__proto__\") {\n                        throw new _DecodeError_mjs__WEBPACK_IMPORTED_MODULE_0__.DecodeError(\"The key __proto__ is not allowed\");\n                    }\n                    state.key = this.mapKeyConverter(object);\n                    state.type = STATE_MAP_VALUE;\n                    continue DECODE;\n                }\n                else {\n                    // it must be `state.type === State.MAP_VALUE` here\n                    state.map[state.key] = object;\n                    state.readCount++;\n                    if (state.readCount === state.size) {\n                        object = state.map;\n                        stack.release(state);\n                    }\n                    else {\n                        state.key = null;\n                        state.type = STATE_MAP_KEY;\n                        continue DECODE;\n                    }\n                }\n            }\n            return object;\n        }\n    }\n    readHeadByte() {\n        if (this.headByte === HEAD_BYTE_REQUIRED) {\n            this.headByte = this.readU8();\n            // console.log(\"headByte\", prettyByte(this.headByte));\n        }\n        return this.headByte;\n    }\n    complete() {\n        this.headByte = HEAD_BYTE_REQUIRED;\n    }\n    readArraySize() {\n        const headByte = this.readHeadByte();\n        switch (headByte) {\n            case 0xdc:\n                return this.readU16();\n            case 0xdd:\n                return this.readU32();\n            default: {\n                if (headByte < 0xa0) {\n                    return headByte - 0x90;\n                }\n                else {\n                    throw new _DecodeError_mjs__WEBPACK_IMPORTED_MODULE_0__.DecodeError(`Unrecognized array type byte: ${(0,_utils_prettyByte_mjs__WEBPACK_IMPORTED_MODULE_5__.prettyByte)(headByte)}`);\n                }\n            }\n        }\n    }\n    pushMapState(size) {\n        if (size > this.maxMapLength) {\n            throw new _DecodeError_mjs__WEBPACK_IMPORTED_MODULE_0__.DecodeError(`Max length exceeded: map length (${size}) > maxMapLengthLength (${this.maxMapLength})`);\n        }\n        this.stack.pushMapState(size);\n    }\n    pushArrayState(size) {\n        if (size > this.maxArrayLength) {\n            throw new _DecodeError_mjs__WEBPACK_IMPORTED_MODULE_0__.DecodeError(`Max length exceeded: array length (${size}) > maxArrayLength (${this.maxArrayLength})`);\n        }\n        this.stack.pushArrayState(size);\n    }\n    decodeString(byteLength, headerOffset) {\n        if (!this.rawStrings || this.stateIsMapKey()) {\n            return this.decodeUtf8String(byteLength, headerOffset);\n        }\n        return this.decodeBinary(byteLength, headerOffset);\n    }\n    /**\n     * @throws {@link RangeError}\n     */\n    decodeUtf8String(byteLength, headerOffset) {\n        if (byteLength > this.maxStrLength) {\n            throw new _DecodeError_mjs__WEBPACK_IMPORTED_MODULE_0__.DecodeError(`Max length exceeded: UTF-8 byte length (${byteLength}) > maxStrLength (${this.maxStrLength})`);\n        }\n        if (this.bytes.byteLength < this.pos + headerOffset + byteLength) {\n            throw MORE_DATA;\n        }\n        const offset = this.pos + headerOffset;\n        let object;\n        if (this.stateIsMapKey() && this.keyDecoder?.canBeCached(byteLength)) {\n            object = this.keyDecoder.decode(this.bytes, offset, byteLength);\n        }\n        else {\n            object = (0,_utils_utf8_mjs__WEBPACK_IMPORTED_MODULE_6__.utf8Decode)(this.bytes, offset, byteLength);\n        }\n        this.pos += headerOffset + byteLength;\n        return object;\n    }\n    stateIsMapKey() {\n        if (this.stack.length > 0) {\n            const state = this.stack.top();\n            return state.type === STATE_MAP_KEY;\n        }\n        return false;\n    }\n    /**\n     * @throws {@link RangeError}\n     */\n    decodeBinary(byteLength, headOffset) {\n        if (byteLength > this.maxBinLength) {\n            throw new _DecodeError_mjs__WEBPACK_IMPORTED_MODULE_0__.DecodeError(`Max length exceeded: bin length (${byteLength}) > maxBinLength (${this.maxBinLength})`);\n        }\n        if (!this.hasRemaining(byteLength + headOffset)) {\n            throw MORE_DATA;\n        }\n        const offset = this.pos + headOffset;\n        const object = this.bytes.subarray(offset, offset + byteLength);\n        this.pos += headOffset + byteLength;\n        return object;\n    }\n    decodeExtension(size, headOffset) {\n        if (size > this.maxExtLength) {\n            throw new _DecodeError_mjs__WEBPACK_IMPORTED_MODULE_0__.DecodeError(`Max length exceeded: ext length (${size}) > maxExtLength (${this.maxExtLength})`);\n        }\n        const extType = this.view.getInt8(this.pos + headOffset);\n        const data = this.decodeBinary(size, headOffset + 1 /* extType */);\n        return this.extensionCodec.decode(data, extType, this.context);\n    }\n    lookU8() {\n        return this.view.getUint8(this.pos);\n    }\n    lookU16() {\n        return this.view.getUint16(this.pos);\n    }\n    lookU32() {\n        return this.view.getUint32(this.pos);\n    }\n    readU8() {\n        const value = this.view.getUint8(this.pos);\n        this.pos++;\n        return value;\n    }\n    readI8() {\n        const value = this.view.getInt8(this.pos);\n        this.pos++;\n        return value;\n    }\n    readU16() {\n        const value = this.view.getUint16(this.pos);\n        this.pos += 2;\n        return value;\n    }\n    readI16() {\n        const value = this.view.getInt16(this.pos);\n        this.pos += 2;\n        return value;\n    }\n    readU32() {\n        const value = this.view.getUint32(this.pos);\n        this.pos += 4;\n        return value;\n    }\n    readI32() {\n        const value = this.view.getInt32(this.pos);\n        this.pos += 4;\n        return value;\n    }\n    readU64() {\n        const value = (0,_utils_int_mjs__WEBPACK_IMPORTED_MODULE_3__.getUint64)(this.view, this.pos);\n        this.pos += 8;\n        return value;\n    }\n    readI64() {\n        const value = (0,_utils_int_mjs__WEBPACK_IMPORTED_MODULE_3__.getInt64)(this.view, this.pos);\n        this.pos += 8;\n        return value;\n    }\n    readU64AsBigInt() {\n        const value = this.view.getBigUint64(this.pos);\n        this.pos += 8;\n        return value;\n    }\n    readI64AsBigInt() {\n        const value = this.view.getBigInt64(this.pos);\n        this.pos += 8;\n        return value;\n    }\n    readF32() {\n        const value = this.view.getFloat32(this.pos);\n        this.pos += 4;\n        return value;\n    }\n    readF64() {\n        const value = this.view.getFloat64(this.pos);\n        this.pos += 8;\n        return value;\n    }\n}\n//# sourceMappingURL=Decoder.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@msgpack/msgpack/dist.esm/Decoder.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/@msgpack/msgpack/dist.esm/Encoder.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/Encoder.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_INITIAL_BUFFER_SIZE: () => (/* binding */ DEFAULT_INITIAL_BUFFER_SIZE),\n/* harmony export */   DEFAULT_MAX_DEPTH: () => (/* binding */ DEFAULT_MAX_DEPTH),\n/* harmony export */   Encoder: () => (/* binding */ Encoder)\n/* harmony export */ });\n/* harmony import */ var _utils_utf8_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/utf8.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/utils/utf8.mjs\");\n/* harmony import */ var _ExtensionCodec_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ExtensionCodec.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/ExtensionCodec.mjs\");\n/* harmony import */ var _utils_int_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/int.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/utils/int.mjs\");\n/* harmony import */ var _utils_typedArrays_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/typedArrays.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/utils/typedArrays.mjs\");\n\n\n\n\nconst DEFAULT_MAX_DEPTH = 100;\nconst DEFAULT_INITIAL_BUFFER_SIZE = 2048;\nclass Encoder {\n    constructor(options) {\n        this.entered = false;\n        this.extensionCodec = options?.extensionCodec ?? _ExtensionCodec_mjs__WEBPACK_IMPORTED_MODULE_0__.ExtensionCodec.defaultCodec;\n        this.context = options?.context; // needs a type assertion because EncoderOptions has no context property when ContextType is undefined\n        this.useBigInt64 = options?.useBigInt64 ?? false;\n        this.maxDepth = options?.maxDepth ?? DEFAULT_MAX_DEPTH;\n        this.initialBufferSize = options?.initialBufferSize ?? DEFAULT_INITIAL_BUFFER_SIZE;\n        this.sortKeys = options?.sortKeys ?? false;\n        this.forceFloat32 = options?.forceFloat32 ?? false;\n        this.ignoreUndefined = options?.ignoreUndefined ?? false;\n        this.forceIntegerToFloat = options?.forceIntegerToFloat ?? false;\n        this.pos = 0;\n        this.view = new DataView(new ArrayBuffer(this.initialBufferSize));\n        this.bytes = new Uint8Array(this.view.buffer);\n    }\n    clone() {\n        // Because of slightly special argument `context`,\n        // type assertion is needed.\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n        return new Encoder({\n            extensionCodec: this.extensionCodec,\n            context: this.context,\n            useBigInt64: this.useBigInt64,\n            maxDepth: this.maxDepth,\n            initialBufferSize: this.initialBufferSize,\n            sortKeys: this.sortKeys,\n            forceFloat32: this.forceFloat32,\n            ignoreUndefined: this.ignoreUndefined,\n            forceIntegerToFloat: this.forceIntegerToFloat,\n        });\n    }\n    reinitializeState() {\n        this.pos = 0;\n    }\n    /**\n     * This is almost equivalent to {@link Encoder#encode}, but it returns an reference of the encoder's internal buffer and thus much faster than {@link Encoder#encode}.\n     *\n     * @returns Encodes the object and returns a shared reference the encoder's internal buffer.\n     */\n    encodeSharedRef(object) {\n        if (this.entered) {\n            const instance = this.clone();\n            return instance.encodeSharedRef(object);\n        }\n        try {\n            this.entered = true;\n            this.reinitializeState();\n            this.doEncode(object, 1);\n            return this.bytes.subarray(0, this.pos);\n        }\n        finally {\n            this.entered = false;\n        }\n    }\n    /**\n     * @returns Encodes the object and returns a copy of the encoder's internal buffer.\n     */\n    encode(object) {\n        if (this.entered) {\n            const instance = this.clone();\n            return instance.encode(object);\n        }\n        try {\n            this.entered = true;\n            this.reinitializeState();\n            this.doEncode(object, 1);\n            return this.bytes.slice(0, this.pos);\n        }\n        finally {\n            this.entered = false;\n        }\n    }\n    doEncode(object, depth) {\n        if (depth > this.maxDepth) {\n            throw new Error(`Too deep objects in depth ${depth}`);\n        }\n        if (object == null) {\n            this.encodeNil();\n        }\n        else if (typeof object === \"boolean\") {\n            this.encodeBoolean(object);\n        }\n        else if (typeof object === \"number\") {\n            if (!this.forceIntegerToFloat) {\n                this.encodeNumber(object);\n            }\n            else {\n                this.encodeNumberAsFloat(object);\n            }\n        }\n        else if (typeof object === \"string\") {\n            this.encodeString(object);\n        }\n        else if (this.useBigInt64 && typeof object === \"bigint\") {\n            this.encodeBigInt64(object);\n        }\n        else {\n            this.encodeObject(object, depth);\n        }\n    }\n    ensureBufferSizeToWrite(sizeToWrite) {\n        const requiredSize = this.pos + sizeToWrite;\n        if (this.view.byteLength < requiredSize) {\n            this.resizeBuffer(requiredSize * 2);\n        }\n    }\n    resizeBuffer(newSize) {\n        const newBuffer = new ArrayBuffer(newSize);\n        const newBytes = new Uint8Array(newBuffer);\n        const newView = new DataView(newBuffer);\n        newBytes.set(this.bytes);\n        this.view = newView;\n        this.bytes = newBytes;\n    }\n    encodeNil() {\n        this.writeU8(0xc0);\n    }\n    encodeBoolean(object) {\n        if (object === false) {\n            this.writeU8(0xc2);\n        }\n        else {\n            this.writeU8(0xc3);\n        }\n    }\n    encodeNumber(object) {\n        if (!this.forceIntegerToFloat && Number.isSafeInteger(object)) {\n            if (object >= 0) {\n                if (object < 0x80) {\n                    // positive fixint\n                    this.writeU8(object);\n                }\n                else if (object < 0x100) {\n                    // uint 8\n                    this.writeU8(0xcc);\n                    this.writeU8(object);\n                }\n                else if (object < 0x10000) {\n                    // uint 16\n                    this.writeU8(0xcd);\n                    this.writeU16(object);\n                }\n                else if (object < 0x100000000) {\n                    // uint 32\n                    this.writeU8(0xce);\n                    this.writeU32(object);\n                }\n                else if (!this.useBigInt64) {\n                    // uint 64\n                    this.writeU8(0xcf);\n                    this.writeU64(object);\n                }\n                else {\n                    this.encodeNumberAsFloat(object);\n                }\n            }\n            else {\n                if (object >= -0x20) {\n                    // negative fixint\n                    this.writeU8(0xe0 | (object + 0x20));\n                }\n                else if (object >= -0x80) {\n                    // int 8\n                    this.writeU8(0xd0);\n                    this.writeI8(object);\n                }\n                else if (object >= -0x8000) {\n                    // int 16\n                    this.writeU8(0xd1);\n                    this.writeI16(object);\n                }\n                else if (object >= -0x80000000) {\n                    // int 32\n                    this.writeU8(0xd2);\n                    this.writeI32(object);\n                }\n                else if (!this.useBigInt64) {\n                    // int 64\n                    this.writeU8(0xd3);\n                    this.writeI64(object);\n                }\n                else {\n                    this.encodeNumberAsFloat(object);\n                }\n            }\n        }\n        else {\n            this.encodeNumberAsFloat(object);\n        }\n    }\n    encodeNumberAsFloat(object) {\n        if (this.forceFloat32) {\n            // float 32\n            this.writeU8(0xca);\n            this.writeF32(object);\n        }\n        else {\n            // float 64\n            this.writeU8(0xcb);\n            this.writeF64(object);\n        }\n    }\n    encodeBigInt64(object) {\n        if (object >= BigInt(0)) {\n            // uint 64\n            this.writeU8(0xcf);\n            this.writeBigUint64(object);\n        }\n        else {\n            // int 64\n            this.writeU8(0xd3);\n            this.writeBigInt64(object);\n        }\n    }\n    writeStringHeader(byteLength) {\n        if (byteLength < 32) {\n            // fixstr\n            this.writeU8(0xa0 + byteLength);\n        }\n        else if (byteLength < 0x100) {\n            // str 8\n            this.writeU8(0xd9);\n            this.writeU8(byteLength);\n        }\n        else if (byteLength < 0x10000) {\n            // str 16\n            this.writeU8(0xda);\n            this.writeU16(byteLength);\n        }\n        else if (byteLength < 0x100000000) {\n            // str 32\n            this.writeU8(0xdb);\n            this.writeU32(byteLength);\n        }\n        else {\n            throw new Error(`Too long string: ${byteLength} bytes in UTF-8`);\n        }\n    }\n    encodeString(object) {\n        const maxHeaderSize = 1 + 4;\n        const byteLength = (0,_utils_utf8_mjs__WEBPACK_IMPORTED_MODULE_1__.utf8Count)(object);\n        this.ensureBufferSizeToWrite(maxHeaderSize + byteLength);\n        this.writeStringHeader(byteLength);\n        (0,_utils_utf8_mjs__WEBPACK_IMPORTED_MODULE_1__.utf8Encode)(object, this.bytes, this.pos);\n        this.pos += byteLength;\n    }\n    encodeObject(object, depth) {\n        // try to encode objects with custom codec first of non-primitives\n        const ext = this.extensionCodec.tryToEncode(object, this.context);\n        if (ext != null) {\n            this.encodeExtension(ext);\n        }\n        else if (Array.isArray(object)) {\n            this.encodeArray(object, depth);\n        }\n        else if (ArrayBuffer.isView(object)) {\n            this.encodeBinary(object);\n        }\n        else if (typeof object === \"object\") {\n            this.encodeMap(object, depth);\n        }\n        else {\n            // symbol, function and other special object come here unless extensionCodec handles them.\n            throw new Error(`Unrecognized object: ${Object.prototype.toString.apply(object)}`);\n        }\n    }\n    encodeBinary(object) {\n        const size = object.byteLength;\n        if (size < 0x100) {\n            // bin 8\n            this.writeU8(0xc4);\n            this.writeU8(size);\n        }\n        else if (size < 0x10000) {\n            // bin 16\n            this.writeU8(0xc5);\n            this.writeU16(size);\n        }\n        else if (size < 0x100000000) {\n            // bin 32\n            this.writeU8(0xc6);\n            this.writeU32(size);\n        }\n        else {\n            throw new Error(`Too large binary: ${size}`);\n        }\n        const bytes = (0,_utils_typedArrays_mjs__WEBPACK_IMPORTED_MODULE_2__.ensureUint8Array)(object);\n        this.writeU8a(bytes);\n    }\n    encodeArray(object, depth) {\n        const size = object.length;\n        if (size < 16) {\n            // fixarray\n            this.writeU8(0x90 + size);\n        }\n        else if (size < 0x10000) {\n            // array 16\n            this.writeU8(0xdc);\n            this.writeU16(size);\n        }\n        else if (size < 0x100000000) {\n            // array 32\n            this.writeU8(0xdd);\n            this.writeU32(size);\n        }\n        else {\n            throw new Error(`Too large array: ${size}`);\n        }\n        for (const item of object) {\n            this.doEncode(item, depth + 1);\n        }\n    }\n    countWithoutUndefined(object, keys) {\n        let count = 0;\n        for (const key of keys) {\n            if (object[key] !== undefined) {\n                count++;\n            }\n        }\n        return count;\n    }\n    encodeMap(object, depth) {\n        const keys = Object.keys(object);\n        if (this.sortKeys) {\n            keys.sort();\n        }\n        const size = this.ignoreUndefined ? this.countWithoutUndefined(object, keys) : keys.length;\n        if (size < 16) {\n            // fixmap\n            this.writeU8(0x80 + size);\n        }\n        else if (size < 0x10000) {\n            // map 16\n            this.writeU8(0xde);\n            this.writeU16(size);\n        }\n        else if (size < 0x100000000) {\n            // map 32\n            this.writeU8(0xdf);\n            this.writeU32(size);\n        }\n        else {\n            throw new Error(`Too large map object: ${size}`);\n        }\n        for (const key of keys) {\n            const value = object[key];\n            if (!(this.ignoreUndefined && value === undefined)) {\n                this.encodeString(key);\n                this.doEncode(value, depth + 1);\n            }\n        }\n    }\n    encodeExtension(ext) {\n        if (typeof ext.data === \"function\") {\n            const data = ext.data(this.pos + 6);\n            const size = data.length;\n            if (size >= 0x100000000) {\n                throw new Error(`Too large extension object: ${size}`);\n            }\n            this.writeU8(0xc9);\n            this.writeU32(size);\n            this.writeI8(ext.type);\n            this.writeU8a(data);\n            return;\n        }\n        const size = ext.data.length;\n        if (size === 1) {\n            // fixext 1\n            this.writeU8(0xd4);\n        }\n        else if (size === 2) {\n            // fixext 2\n            this.writeU8(0xd5);\n        }\n        else if (size === 4) {\n            // fixext 4\n            this.writeU8(0xd6);\n        }\n        else if (size === 8) {\n            // fixext 8\n            this.writeU8(0xd7);\n        }\n        else if (size === 16) {\n            // fixext 16\n            this.writeU8(0xd8);\n        }\n        else if (size < 0x100) {\n            // ext 8\n            this.writeU8(0xc7);\n            this.writeU8(size);\n        }\n        else if (size < 0x10000) {\n            // ext 16\n            this.writeU8(0xc8);\n            this.writeU16(size);\n        }\n        else if (size < 0x100000000) {\n            // ext 32\n            this.writeU8(0xc9);\n            this.writeU32(size);\n        }\n        else {\n            throw new Error(`Too large extension object: ${size}`);\n        }\n        this.writeI8(ext.type);\n        this.writeU8a(ext.data);\n    }\n    writeU8(value) {\n        this.ensureBufferSizeToWrite(1);\n        this.view.setUint8(this.pos, value);\n        this.pos++;\n    }\n    writeU8a(values) {\n        const size = values.length;\n        this.ensureBufferSizeToWrite(size);\n        this.bytes.set(values, this.pos);\n        this.pos += size;\n    }\n    writeI8(value) {\n        this.ensureBufferSizeToWrite(1);\n        this.view.setInt8(this.pos, value);\n        this.pos++;\n    }\n    writeU16(value) {\n        this.ensureBufferSizeToWrite(2);\n        this.view.setUint16(this.pos, value);\n        this.pos += 2;\n    }\n    writeI16(value) {\n        this.ensureBufferSizeToWrite(2);\n        this.view.setInt16(this.pos, value);\n        this.pos += 2;\n    }\n    writeU32(value) {\n        this.ensureBufferSizeToWrite(4);\n        this.view.setUint32(this.pos, value);\n        this.pos += 4;\n    }\n    writeI32(value) {\n        this.ensureBufferSizeToWrite(4);\n        this.view.setInt32(this.pos, value);\n        this.pos += 4;\n    }\n    writeF32(value) {\n        this.ensureBufferSizeToWrite(4);\n        this.view.setFloat32(this.pos, value);\n        this.pos += 4;\n    }\n    writeF64(value) {\n        this.ensureBufferSizeToWrite(8);\n        this.view.setFloat64(this.pos, value);\n        this.pos += 8;\n    }\n    writeU64(value) {\n        this.ensureBufferSizeToWrite(8);\n        (0,_utils_int_mjs__WEBPACK_IMPORTED_MODULE_3__.setUint64)(this.view, this.pos, value);\n        this.pos += 8;\n    }\n    writeI64(value) {\n        this.ensureBufferSizeToWrite(8);\n        (0,_utils_int_mjs__WEBPACK_IMPORTED_MODULE_3__.setInt64)(this.view, this.pos, value);\n        this.pos += 8;\n    }\n    writeBigUint64(value) {\n        this.ensureBufferSizeToWrite(8);\n        this.view.setBigUint64(this.pos, value);\n        this.pos += 8;\n    }\n    writeBigInt64(value) {\n        this.ensureBufferSizeToWrite(8);\n        this.view.setBigInt64(this.pos, value);\n        this.pos += 8;\n    }\n}\n//# sourceMappingURL=Encoder.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@msgpack/msgpack/dist.esm/Encoder.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/@msgpack/msgpack/dist.esm/ExtData.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/ExtData.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExtData: () => (/* binding */ ExtData)\n/* harmony export */ });\n/**\n * ExtData is used to handle Extension Types that are not registered to ExtensionCodec.\n */\nclass ExtData {\n    constructor(type, data) {\n        this.type = type;\n        this.data = data;\n    }\n}\n//# sourceMappingURL=ExtData.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQG1zZ3BhY2svbXNncGFjay9kaXN0LmVzbS9FeHREYXRhLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9saXVwZW5nL2N1cnNvci/ov5vpmLYvRmx1eOWvueivneW8j+W6lOeUqC9mbHV4LWNoYXQtYXBwL25vZGVfbW9kdWxlcy9AbXNncGFjay9tc2dwYWNrL2Rpc3QuZXNtL0V4dERhdGEubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRXh0RGF0YSBpcyB1c2VkIHRvIGhhbmRsZSBFeHRlbnNpb24gVHlwZXMgdGhhdCBhcmUgbm90IHJlZ2lzdGVyZWQgdG8gRXh0ZW5zaW9uQ29kZWMuXG4gKi9cbmV4cG9ydCBjbGFzcyBFeHREYXRhIHtcbiAgICBjb25zdHJ1Y3Rvcih0eXBlLCBkYXRhKSB7XG4gICAgICAgIHRoaXMudHlwZSA9IHR5cGU7XG4gICAgICAgIHRoaXMuZGF0YSA9IGRhdGE7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9RXh0RGF0YS5tanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@msgpack/msgpack/dist.esm/ExtData.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/@msgpack/msgpack/dist.esm/ExtensionCodec.mjs":
/*!*******************************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/ExtensionCodec.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExtensionCodec: () => (/* binding */ ExtensionCodec)\n/* harmony export */ });\n/* harmony import */ var _ExtData_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ExtData.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/ExtData.mjs\");\n/* harmony import */ var _timestamp_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./timestamp.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/timestamp.mjs\");\n// ExtensionCodec to handle MessagePack extensions\n\n\nclass ExtensionCodec {\n    constructor() {\n        // built-in extensions\n        this.builtInEncoders = [];\n        this.builtInDecoders = [];\n        // custom extensions\n        this.encoders = [];\n        this.decoders = [];\n        this.register(_timestamp_mjs__WEBPACK_IMPORTED_MODULE_0__.timestampExtension);\n    }\n    register({ type, encode, decode, }) {\n        if (type >= 0) {\n            // custom extensions\n            this.encoders[type] = encode;\n            this.decoders[type] = decode;\n        }\n        else {\n            // built-in extensions\n            const index = -1 - type;\n            this.builtInEncoders[index] = encode;\n            this.builtInDecoders[index] = decode;\n        }\n    }\n    tryToEncode(object, context) {\n        // built-in extensions\n        for (let i = 0; i < this.builtInEncoders.length; i++) {\n            const encodeExt = this.builtInEncoders[i];\n            if (encodeExt != null) {\n                const data = encodeExt(object, context);\n                if (data != null) {\n                    const type = -1 - i;\n                    return new _ExtData_mjs__WEBPACK_IMPORTED_MODULE_1__.ExtData(type, data);\n                }\n            }\n        }\n        // custom extensions\n        for (let i = 0; i < this.encoders.length; i++) {\n            const encodeExt = this.encoders[i];\n            if (encodeExt != null) {\n                const data = encodeExt(object, context);\n                if (data != null) {\n                    const type = i;\n                    return new _ExtData_mjs__WEBPACK_IMPORTED_MODULE_1__.ExtData(type, data);\n                }\n            }\n        }\n        if (object instanceof _ExtData_mjs__WEBPACK_IMPORTED_MODULE_1__.ExtData) {\n            // to keep ExtData as is\n            return object;\n        }\n        return null;\n    }\n    decode(data, type, context) {\n        const decodeExt = type < 0 ? this.builtInDecoders[-1 - type] : this.decoders[type];\n        if (decodeExt) {\n            return decodeExt(data, type, context);\n        }\n        else {\n            // decode() does not fail, returns ExtData instead.\n            return new _ExtData_mjs__WEBPACK_IMPORTED_MODULE_1__.ExtData(type, data);\n        }\n    }\n}\nExtensionCodec.defaultCodec = new ExtensionCodec();\n//# sourceMappingURL=ExtensionCodec.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@msgpack/msgpack/dist.esm/ExtensionCodec.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/@msgpack/msgpack/dist.esm/decode.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/decode.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decode: () => (/* binding */ decode),\n/* harmony export */   decodeMulti: () => (/* binding */ decodeMulti)\n/* harmony export */ });\n/* harmony import */ var _Decoder_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Decoder.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/Decoder.mjs\");\n\n/**\n * It decodes a single MessagePack object in a buffer.\n *\n * This is a synchronous decoding function.\n * See other variants for asynchronous decoding: {@link decodeAsync}, {@link decodeMultiStream}, or {@link decodeArrayStream}.\n *\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\nfunction decode(buffer, options) {\n    const decoder = new _Decoder_mjs__WEBPACK_IMPORTED_MODULE_0__.Decoder(options);\n    return decoder.decode(buffer);\n}\n/**\n * It decodes multiple MessagePack objects in a buffer.\n * This is corresponding to {@link decodeMultiStream}.\n *\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\nfunction decodeMulti(buffer, options) {\n    const decoder = new _Decoder_mjs__WEBPACK_IMPORTED_MODULE_0__.Decoder(options);\n    return decoder.decodeMulti(buffer);\n}\n//# sourceMappingURL=decode.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@msgpack/msgpack/dist.esm/decode.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/@msgpack/msgpack/dist.esm/decodeAsync.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/decodeAsync.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decodeArrayStream: () => (/* binding */ decodeArrayStream),\n/* harmony export */   decodeAsync: () => (/* binding */ decodeAsync),\n/* harmony export */   decodeMultiStream: () => (/* binding */ decodeMultiStream)\n/* harmony export */ });\n/* harmony import */ var _Decoder_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Decoder.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/Decoder.mjs\");\n/* harmony import */ var _utils_stream_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/stream.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/utils/stream.mjs\");\n\n\n/**\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\nasync function decodeAsync(streamLike, options) {\n    const stream = (0,_utils_stream_mjs__WEBPACK_IMPORTED_MODULE_0__.ensureAsyncIterable)(streamLike);\n    const decoder = new _Decoder_mjs__WEBPACK_IMPORTED_MODULE_1__.Decoder(options);\n    return decoder.decodeAsync(stream);\n}\n/**\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\nfunction decodeArrayStream(streamLike, options) {\n    const stream = (0,_utils_stream_mjs__WEBPACK_IMPORTED_MODULE_0__.ensureAsyncIterable)(streamLike);\n    const decoder = new _Decoder_mjs__WEBPACK_IMPORTED_MODULE_1__.Decoder(options);\n    return decoder.decodeArrayStream(stream);\n}\n/**\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\nfunction decodeMultiStream(streamLike, options) {\n    const stream = (0,_utils_stream_mjs__WEBPACK_IMPORTED_MODULE_0__.ensureAsyncIterable)(streamLike);\n    const decoder = new _Decoder_mjs__WEBPACK_IMPORTED_MODULE_1__.Decoder(options);\n    return decoder.decodeStream(stream);\n}\n//# sourceMappingURL=decodeAsync.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@msgpack/msgpack/dist.esm/decodeAsync.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/@msgpack/msgpack/dist.esm/encode.mjs":
/*!***********************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/encode.mjs ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encode: () => (/* binding */ encode)\n/* harmony export */ });\n/* harmony import */ var _Encoder_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Encoder.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/Encoder.mjs\");\n\n/**\n * It encodes `value` in the MessagePack format and\n * returns a byte buffer.\n *\n * The returned buffer is a slice of a larger `ArrayBuffer`, so you have to use its `#byteOffset` and `#byteLength` in order to convert it to another typed arrays including NodeJS `Buffer`.\n */\nfunction encode(value, options) {\n    const encoder = new _Encoder_mjs__WEBPACK_IMPORTED_MODULE_0__.Encoder(options);\n    return encoder.encodeSharedRef(value);\n}\n//# sourceMappingURL=encode.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQG1zZ3BhY2svbXNncGFjay9kaXN0LmVzbS9lbmNvZGUubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXdDO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1Asd0JBQXdCLGlEQUFPO0FBQy9CO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2xpdXBlbmcvY3Vyc29yL+i/m+mYti9GbHV45a+56K+d5byP5bqU55SoL2ZsdXgtY2hhdC1hcHAvbm9kZV9tb2R1bGVzL0Btc2dwYWNrL21zZ3BhY2svZGlzdC5lc20vZW5jb2RlLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBFbmNvZGVyIH0gZnJvbSBcIi4vRW5jb2Rlci5tanNcIjtcbi8qKlxuICogSXQgZW5jb2RlcyBgdmFsdWVgIGluIHRoZSBNZXNzYWdlUGFjayBmb3JtYXQgYW5kXG4gKiByZXR1cm5zIGEgYnl0ZSBidWZmZXIuXG4gKlxuICogVGhlIHJldHVybmVkIGJ1ZmZlciBpcyBhIHNsaWNlIG9mIGEgbGFyZ2VyIGBBcnJheUJ1ZmZlcmAsIHNvIHlvdSBoYXZlIHRvIHVzZSBpdHMgYCNieXRlT2Zmc2V0YCBhbmQgYCNieXRlTGVuZ3RoYCBpbiBvcmRlciB0byBjb252ZXJ0IGl0IHRvIGFub3RoZXIgdHlwZWQgYXJyYXlzIGluY2x1ZGluZyBOb2RlSlMgYEJ1ZmZlcmAuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBlbmNvZGUodmFsdWUsIG9wdGlvbnMpIHtcbiAgICBjb25zdCBlbmNvZGVyID0gbmV3IEVuY29kZXIob3B0aW9ucyk7XG4gICAgcmV0dXJuIGVuY29kZXIuZW5jb2RlU2hhcmVkUmVmKHZhbHVlKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWVuY29kZS5tanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@msgpack/msgpack/dist.esm/encode.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/@msgpack/msgpack/dist.esm/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DecodeError: () => (/* reexport safe */ _DecodeError_mjs__WEBPACK_IMPORTED_MODULE_4__.DecodeError),\n/* harmony export */   Decoder: () => (/* reexport safe */ _Decoder_mjs__WEBPACK_IMPORTED_MODULE_3__.Decoder),\n/* harmony export */   EXT_TIMESTAMP: () => (/* reexport safe */ _timestamp_mjs__WEBPACK_IMPORTED_MODULE_8__.EXT_TIMESTAMP),\n/* harmony export */   Encoder: () => (/* reexport safe */ _Encoder_mjs__WEBPACK_IMPORTED_MODULE_5__.Encoder),\n/* harmony export */   ExtData: () => (/* reexport safe */ _ExtData_mjs__WEBPACK_IMPORTED_MODULE_7__.ExtData),\n/* harmony export */   ExtensionCodec: () => (/* reexport safe */ _ExtensionCodec_mjs__WEBPACK_IMPORTED_MODULE_6__.ExtensionCodec),\n/* harmony export */   decode: () => (/* reexport safe */ _decode_mjs__WEBPACK_IMPORTED_MODULE_1__.decode),\n/* harmony export */   decodeArrayStream: () => (/* reexport safe */ _decodeAsync_mjs__WEBPACK_IMPORTED_MODULE_2__.decodeArrayStream),\n/* harmony export */   decodeAsync: () => (/* reexport safe */ _decodeAsync_mjs__WEBPACK_IMPORTED_MODULE_2__.decodeAsync),\n/* harmony export */   decodeMulti: () => (/* reexport safe */ _decode_mjs__WEBPACK_IMPORTED_MODULE_1__.decodeMulti),\n/* harmony export */   decodeMultiStream: () => (/* reexport safe */ _decodeAsync_mjs__WEBPACK_IMPORTED_MODULE_2__.decodeMultiStream),\n/* harmony export */   decodeTimestampExtension: () => (/* reexport safe */ _timestamp_mjs__WEBPACK_IMPORTED_MODULE_8__.decodeTimestampExtension),\n/* harmony export */   decodeTimestampToTimeSpec: () => (/* reexport safe */ _timestamp_mjs__WEBPACK_IMPORTED_MODULE_8__.decodeTimestampToTimeSpec),\n/* harmony export */   encode: () => (/* reexport safe */ _encode_mjs__WEBPACK_IMPORTED_MODULE_0__.encode),\n/* harmony export */   encodeDateToTimeSpec: () => (/* reexport safe */ _timestamp_mjs__WEBPACK_IMPORTED_MODULE_8__.encodeDateToTimeSpec),\n/* harmony export */   encodeTimeSpecToTimestamp: () => (/* reexport safe */ _timestamp_mjs__WEBPACK_IMPORTED_MODULE_8__.encodeTimeSpecToTimestamp),\n/* harmony export */   encodeTimestampExtension: () => (/* reexport safe */ _timestamp_mjs__WEBPACK_IMPORTED_MODULE_8__.encodeTimestampExtension)\n/* harmony export */ });\n/* harmony import */ var _encode_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./encode.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/encode.mjs\");\n/* harmony import */ var _decode_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./decode.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/decode.mjs\");\n/* harmony import */ var _decodeAsync_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./decodeAsync.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/decodeAsync.mjs\");\n/* harmony import */ var _Decoder_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Decoder.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/Decoder.mjs\");\n/* harmony import */ var _DecodeError_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./DecodeError.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/DecodeError.mjs\");\n/* harmony import */ var _Encoder_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Encoder.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/Encoder.mjs\");\n/* harmony import */ var _ExtensionCodec_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ExtensionCodec.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/ExtensionCodec.mjs\");\n/* harmony import */ var _ExtData_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ExtData.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/ExtData.mjs\");\n/* harmony import */ var _timestamp_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./timestamp.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/timestamp.mjs\");\n// Main Functions:\n\n\n\n\n\n\n\n\n\n\n\n\n// Utilities for Extension Types:\n\n\n\n\n\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@msgpack/msgpack/dist.esm/index.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/@msgpack/msgpack/dist.esm/timestamp.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/timestamp.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EXT_TIMESTAMP: () => (/* binding */ EXT_TIMESTAMP),\n/* harmony export */   decodeTimestampExtension: () => (/* binding */ decodeTimestampExtension),\n/* harmony export */   decodeTimestampToTimeSpec: () => (/* binding */ decodeTimestampToTimeSpec),\n/* harmony export */   encodeDateToTimeSpec: () => (/* binding */ encodeDateToTimeSpec),\n/* harmony export */   encodeTimeSpecToTimestamp: () => (/* binding */ encodeTimeSpecToTimestamp),\n/* harmony export */   encodeTimestampExtension: () => (/* binding */ encodeTimestampExtension),\n/* harmony export */   timestampExtension: () => (/* binding */ timestampExtension)\n/* harmony export */ });\n/* harmony import */ var _DecodeError_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./DecodeError.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/DecodeError.mjs\");\n/* harmony import */ var _utils_int_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils/int.mjs */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/utils/int.mjs\");\n// https://github.com/msgpack/msgpack/blob/master/spec.md#timestamp-extension-type\n\n\nconst EXT_TIMESTAMP = -1;\nconst TIMESTAMP32_MAX_SEC = 0x100000000 - 1; // 32-bit unsigned int\nconst TIMESTAMP64_MAX_SEC = 0x400000000 - 1; // 34-bit unsigned int\nfunction encodeTimeSpecToTimestamp({ sec, nsec }) {\n    if (sec >= 0 && nsec >= 0 && sec <= TIMESTAMP64_MAX_SEC) {\n        // Here sec >= 0 && nsec >= 0\n        if (nsec === 0 && sec <= TIMESTAMP32_MAX_SEC) {\n            // timestamp 32 = { sec32 (unsigned) }\n            const rv = new Uint8Array(4);\n            const view = new DataView(rv.buffer);\n            view.setUint32(0, sec);\n            return rv;\n        }\n        else {\n            // timestamp 64 = { nsec30 (unsigned), sec34 (unsigned) }\n            const secHigh = sec / 0x100000000;\n            const secLow = sec & 0xffffffff;\n            const rv = new Uint8Array(8);\n            const view = new DataView(rv.buffer);\n            // nsec30 | secHigh2\n            view.setUint32(0, (nsec << 2) | (secHigh & 0x3));\n            // secLow32\n            view.setUint32(4, secLow);\n            return rv;\n        }\n    }\n    else {\n        // timestamp 96 = { nsec32 (unsigned), sec64 (signed) }\n        const rv = new Uint8Array(12);\n        const view = new DataView(rv.buffer);\n        view.setUint32(0, nsec);\n        (0,_utils_int_mjs__WEBPACK_IMPORTED_MODULE_0__.setInt64)(view, 4, sec);\n        return rv;\n    }\n}\nfunction encodeDateToTimeSpec(date) {\n    const msec = date.getTime();\n    const sec = Math.floor(msec / 1e3);\n    const nsec = (msec - sec * 1e3) * 1e6;\n    // Normalizes { sec, nsec } to ensure nsec is unsigned.\n    const nsecInSec = Math.floor(nsec / 1e9);\n    return {\n        sec: sec + nsecInSec,\n        nsec: nsec - nsecInSec * 1e9,\n    };\n}\nfunction encodeTimestampExtension(object) {\n    if (object instanceof Date) {\n        const timeSpec = encodeDateToTimeSpec(object);\n        return encodeTimeSpecToTimestamp(timeSpec);\n    }\n    else {\n        return null;\n    }\n}\nfunction decodeTimestampToTimeSpec(data) {\n    const view = new DataView(data.buffer, data.byteOffset, data.byteLength);\n    // data may be 32, 64, or 96 bits\n    switch (data.byteLength) {\n        case 4: {\n            // timestamp 32 = { sec32 }\n            const sec = view.getUint32(0);\n            const nsec = 0;\n            return { sec, nsec };\n        }\n        case 8: {\n            // timestamp 64 = { nsec30, sec34 }\n            const nsec30AndSecHigh2 = view.getUint32(0);\n            const secLow32 = view.getUint32(4);\n            const sec = (nsec30AndSecHigh2 & 0x3) * 0x100000000 + secLow32;\n            const nsec = nsec30AndSecHigh2 >>> 2;\n            return { sec, nsec };\n        }\n        case 12: {\n            // timestamp 96 = { nsec32 (unsigned), sec64 (signed) }\n            const sec = (0,_utils_int_mjs__WEBPACK_IMPORTED_MODULE_0__.getInt64)(view, 4);\n            const nsec = view.getUint32(0);\n            return { sec, nsec };\n        }\n        default:\n            throw new _DecodeError_mjs__WEBPACK_IMPORTED_MODULE_1__.DecodeError(`Unrecognized data size for timestamp (expected 4, 8, or 12): ${data.length}`);\n    }\n}\nfunction decodeTimestampExtension(data) {\n    const timeSpec = decodeTimestampToTimeSpec(data);\n    return new Date(timeSpec.sec * 1e3 + timeSpec.nsec / 1e6);\n}\nconst timestampExtension = {\n    type: EXT_TIMESTAMP,\n    encode: encodeTimestampExtension,\n    decode: decodeTimestampExtension,\n};\n//# sourceMappingURL=timestamp.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@msgpack/msgpack/dist.esm/timestamp.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/@msgpack/msgpack/dist.esm/utils/int.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/utils/int.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UINT32_MAX: () => (/* binding */ UINT32_MAX),\n/* harmony export */   getInt64: () => (/* binding */ getInt64),\n/* harmony export */   getUint64: () => (/* binding */ getUint64),\n/* harmony export */   setInt64: () => (/* binding */ setInt64),\n/* harmony export */   setUint64: () => (/* binding */ setUint64)\n/* harmony export */ });\n// Integer Utility\nconst UINT32_MAX = 4294967295;\n// DataView extension to handle int64 / uint64,\n// where the actual range is 53-bits integer (a.k.a. safe integer)\nfunction setUint64(view, offset, value) {\n    const high = value / 4294967296;\n    const low = value; // high bits are truncated by DataView\n    view.setUint32(offset, high);\n    view.setUint32(offset + 4, low);\n}\nfunction setInt64(view, offset, value) {\n    const high = Math.floor(value / 4294967296);\n    const low = value; // high bits are truncated by DataView\n    view.setUint32(offset, high);\n    view.setUint32(offset + 4, low);\n}\nfunction getInt64(view, offset) {\n    const high = view.getInt32(offset);\n    const low = view.getUint32(offset + 4);\n    return high * 4294967296 + low;\n}\nfunction getUint64(view, offset) {\n    const high = view.getUint32(offset);\n    const low = view.getUint32(offset + 4);\n    return high * 4294967296 + low;\n}\n//# sourceMappingURL=int.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQG1zZ3BhY2svbXNncGFjay9kaXN0LmVzbS91dGlscy9pbnQubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDTztBQUNQO0FBQ0E7QUFDTztBQUNQO0FBQ0EsdUJBQXVCO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQSx1QkFBdUI7QUFDdkI7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2xpdXBlbmcvY3Vyc29yL+i/m+mYti9GbHV45a+56K+d5byP5bqU55SoL2ZsdXgtY2hhdC1hcHAvbm9kZV9tb2R1bGVzL0Btc2dwYWNrL21zZ3BhY2svZGlzdC5lc20vdXRpbHMvaW50Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBJbnRlZ2VyIFV0aWxpdHlcbmV4cG9ydCBjb25zdCBVSU5UMzJfTUFYID0gNDI5NDk2NzI5NTtcbi8vIERhdGFWaWV3IGV4dGVuc2lvbiB0byBoYW5kbGUgaW50NjQgLyB1aW50NjQsXG4vLyB3aGVyZSB0aGUgYWN0dWFsIHJhbmdlIGlzIDUzLWJpdHMgaW50ZWdlciAoYS5rLmEuIHNhZmUgaW50ZWdlcilcbmV4cG9ydCBmdW5jdGlvbiBzZXRVaW50NjQodmlldywgb2Zmc2V0LCB2YWx1ZSkge1xuICAgIGNvbnN0IGhpZ2ggPSB2YWx1ZSAvIDQyOTQ5NjcyOTY7XG4gICAgY29uc3QgbG93ID0gdmFsdWU7IC8vIGhpZ2ggYml0cyBhcmUgdHJ1bmNhdGVkIGJ5IERhdGFWaWV3XG4gICAgdmlldy5zZXRVaW50MzIob2Zmc2V0LCBoaWdoKTtcbiAgICB2aWV3LnNldFVpbnQzMihvZmZzZXQgKyA0LCBsb3cpO1xufVxuZXhwb3J0IGZ1bmN0aW9uIHNldEludDY0KHZpZXcsIG9mZnNldCwgdmFsdWUpIHtcbiAgICBjb25zdCBoaWdoID0gTWF0aC5mbG9vcih2YWx1ZSAvIDQyOTQ5NjcyOTYpO1xuICAgIGNvbnN0IGxvdyA9IHZhbHVlOyAvLyBoaWdoIGJpdHMgYXJlIHRydW5jYXRlZCBieSBEYXRhVmlld1xuICAgIHZpZXcuc2V0VWludDMyKG9mZnNldCwgaGlnaCk7XG4gICAgdmlldy5zZXRVaW50MzIob2Zmc2V0ICsgNCwgbG93KTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBnZXRJbnQ2NCh2aWV3LCBvZmZzZXQpIHtcbiAgICBjb25zdCBoaWdoID0gdmlldy5nZXRJbnQzMihvZmZzZXQpO1xuICAgIGNvbnN0IGxvdyA9IHZpZXcuZ2V0VWludDMyKG9mZnNldCArIDQpO1xuICAgIHJldHVybiBoaWdoICogNDI5NDk2NzI5NiArIGxvdztcbn1cbmV4cG9ydCBmdW5jdGlvbiBnZXRVaW50NjQodmlldywgb2Zmc2V0KSB7XG4gICAgY29uc3QgaGlnaCA9IHZpZXcuZ2V0VWludDMyKG9mZnNldCk7XG4gICAgY29uc3QgbG93ID0gdmlldy5nZXRVaW50MzIob2Zmc2V0ICsgNCk7XG4gICAgcmV0dXJuIGhpZ2ggKiA0Mjk0OTY3Mjk2ICsgbG93O1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW50Lm1qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@msgpack/msgpack/dist.esm/utils/int.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/@msgpack/msgpack/dist.esm/utils/prettyByte.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/utils/prettyByte.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prettyByte: () => (/* binding */ prettyByte)\n/* harmony export */ });\nfunction prettyByte(byte) {\n    return `${byte < 0 ? \"-\" : \"\"}0x${Math.abs(byte).toString(16).padStart(2, \"0\")}`;\n}\n//# sourceMappingURL=prettyByte.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQG1zZ3BhY2svbXNncGFjay9kaXN0LmVzbS91dGlscy9wcmV0dHlCeXRlLm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUCxjQUFjLG9CQUFvQixJQUFJLDZDQUE2QztBQUNuRjtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvbGl1cGVuZy9jdXJzb3Iv6L+b6Zi2L0ZsdXjlr7nor53lvI/lupTnlKgvZmx1eC1jaGF0LWFwcC9ub2RlX21vZHVsZXMvQG1zZ3BhY2svbXNncGFjay9kaXN0LmVzbS91dGlscy9wcmV0dHlCeXRlLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gcHJldHR5Qnl0ZShieXRlKSB7XG4gICAgcmV0dXJuIGAke2J5dGUgPCAwID8gXCItXCIgOiBcIlwifTB4JHtNYXRoLmFicyhieXRlKS50b1N0cmluZygxNikucGFkU3RhcnQoMiwgXCIwXCIpfWA7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1wcmV0dHlCeXRlLm1qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@msgpack/msgpack/dist.esm/utils/prettyByte.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/@msgpack/msgpack/dist.esm/utils/stream.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/utils/stream.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   asyncIterableFromStream: () => (/* binding */ asyncIterableFromStream),\n/* harmony export */   ensureAsyncIterable: () => (/* binding */ ensureAsyncIterable),\n/* harmony export */   isAsyncIterable: () => (/* binding */ isAsyncIterable)\n/* harmony export */ });\n// utility for whatwg streams\nfunction isAsyncIterable(object) {\n    return object[Symbol.asyncIterator] != null;\n}\nasync function* asyncIterableFromStream(stream) {\n    const reader = stream.getReader();\n    try {\n        while (true) {\n            const { done, value } = await reader.read();\n            if (done) {\n                return;\n            }\n            yield value;\n        }\n    }\n    finally {\n        reader.releaseLock();\n    }\n}\nfunction ensureAsyncIterable(streamLike) {\n    if (isAsyncIterable(streamLike)) {\n        return streamLike;\n    }\n    else {\n        return asyncIterableFromStream(streamLike);\n    }\n}\n//# sourceMappingURL=stream.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQG1zZ3BhY2svbXNncGFjay9kaXN0LmVzbS91dGlscy9zdHJlYW0ubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ087QUFDUDtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsY0FBYztBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2xpdXBlbmcvY3Vyc29yL+i/m+mYti9GbHV45a+56K+d5byP5bqU55SoL2ZsdXgtY2hhdC1hcHAvbm9kZV9tb2R1bGVzL0Btc2dwYWNrL21zZ3BhY2svZGlzdC5lc20vdXRpbHMvc3RyZWFtLm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyB1dGlsaXR5IGZvciB3aGF0d2cgc3RyZWFtc1xuZXhwb3J0IGZ1bmN0aW9uIGlzQXN5bmNJdGVyYWJsZShvYmplY3QpIHtcbiAgICByZXR1cm4gb2JqZWN0W1N5bWJvbC5hc3luY0l0ZXJhdG9yXSAhPSBudWxsO1xufVxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uKiBhc3luY0l0ZXJhYmxlRnJvbVN0cmVhbShzdHJlYW0pIHtcbiAgICBjb25zdCByZWFkZXIgPSBzdHJlYW0uZ2V0UmVhZGVyKCk7XG4gICAgdHJ5IHtcbiAgICAgICAgd2hpbGUgKHRydWUpIHtcbiAgICAgICAgICAgIGNvbnN0IHsgZG9uZSwgdmFsdWUgfSA9IGF3YWl0IHJlYWRlci5yZWFkKCk7XG4gICAgICAgICAgICBpZiAoZG9uZSkge1xuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHlpZWxkIHZhbHVlO1xuICAgICAgICB9XG4gICAgfVxuICAgIGZpbmFsbHkge1xuICAgICAgICByZWFkZXIucmVsZWFzZUxvY2soKTtcbiAgICB9XG59XG5leHBvcnQgZnVuY3Rpb24gZW5zdXJlQXN5bmNJdGVyYWJsZShzdHJlYW1MaWtlKSB7XG4gICAgaWYgKGlzQXN5bmNJdGVyYWJsZShzdHJlYW1MaWtlKSkge1xuICAgICAgICByZXR1cm4gc3RyZWFtTGlrZTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIHJldHVybiBhc3luY0l0ZXJhYmxlRnJvbVN0cmVhbShzdHJlYW1MaWtlKTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zdHJlYW0ubWpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@msgpack/msgpack/dist.esm/utils/stream.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/@msgpack/msgpack/dist.esm/utils/typedArrays.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/utils/typedArrays.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensureUint8Array: () => (/* binding */ ensureUint8Array)\n/* harmony export */ });\nfunction isArrayBufferLike(buffer) {\n    return (buffer instanceof ArrayBuffer || (typeof SharedArrayBuffer !== \"undefined\" && buffer instanceof SharedArrayBuffer));\n}\nfunction ensureUint8Array(buffer) {\n    if (buffer instanceof Uint8Array) {\n        return buffer;\n    }\n    else if (ArrayBuffer.isView(buffer)) {\n        return new Uint8Array(buffer.buffer, buffer.byteOffset, buffer.byteLength);\n    }\n    else if (isArrayBufferLike(buffer)) {\n        return new Uint8Array(buffer);\n    }\n    else {\n        // ArrayLike<number>\n        return Uint8Array.from(buffer);\n    }\n}\n//# sourceMappingURL=typedArrays.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQG1zZ3BhY2svbXNncGFjay9kaXN0LmVzbS91dGlscy90eXBlZEFycmF5cy5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvbGl1cGVuZy9jdXJzb3Iv6L+b6Zi2L0ZsdXjlr7nor53lvI/lupTnlKgvZmx1eC1jaGF0LWFwcC9ub2RlX21vZHVsZXMvQG1zZ3BhY2svbXNncGFjay9kaXN0LmVzbS91dGlscy90eXBlZEFycmF5cy5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gaXNBcnJheUJ1ZmZlckxpa2UoYnVmZmVyKSB7XG4gICAgcmV0dXJuIChidWZmZXIgaW5zdGFuY2VvZiBBcnJheUJ1ZmZlciB8fCAodHlwZW9mIFNoYXJlZEFycmF5QnVmZmVyICE9PSBcInVuZGVmaW5lZFwiICYmIGJ1ZmZlciBpbnN0YW5jZW9mIFNoYXJlZEFycmF5QnVmZmVyKSk7XG59XG5leHBvcnQgZnVuY3Rpb24gZW5zdXJlVWludDhBcnJheShidWZmZXIpIHtcbiAgICBpZiAoYnVmZmVyIGluc3RhbmNlb2YgVWludDhBcnJheSkge1xuICAgICAgICByZXR1cm4gYnVmZmVyO1xuICAgIH1cbiAgICBlbHNlIGlmIChBcnJheUJ1ZmZlci5pc1ZpZXcoYnVmZmVyKSkge1xuICAgICAgICByZXR1cm4gbmV3IFVpbnQ4QXJyYXkoYnVmZmVyLmJ1ZmZlciwgYnVmZmVyLmJ5dGVPZmZzZXQsIGJ1ZmZlci5ieXRlTGVuZ3RoKTtcbiAgICB9XG4gICAgZWxzZSBpZiAoaXNBcnJheUJ1ZmZlckxpa2UoYnVmZmVyKSkge1xuICAgICAgICByZXR1cm4gbmV3IFVpbnQ4QXJyYXkoYnVmZmVyKTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIC8vIEFycmF5TGlrZTxudW1iZXI+XG4gICAgICAgIHJldHVybiBVaW50OEFycmF5LmZyb20oYnVmZmVyKTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD10eXBlZEFycmF5cy5tanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@msgpack/msgpack/dist.esm/utils/typedArrays.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/@msgpack/msgpack/dist.esm/utils/utf8.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@msgpack/msgpack/dist.esm/utils/utf8.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   utf8Count: () => (/* binding */ utf8Count),\n/* harmony export */   utf8Decode: () => (/* binding */ utf8Decode),\n/* harmony export */   utf8DecodeJs: () => (/* binding */ utf8DecodeJs),\n/* harmony export */   utf8DecodeTD: () => (/* binding */ utf8DecodeTD),\n/* harmony export */   utf8Encode: () => (/* binding */ utf8Encode),\n/* harmony export */   utf8EncodeJs: () => (/* binding */ utf8EncodeJs),\n/* harmony export */   utf8EncodeTE: () => (/* binding */ utf8EncodeTE)\n/* harmony export */ });\nfunction utf8Count(str) {\n    const strLength = str.length;\n    let byteLength = 0;\n    let pos = 0;\n    while (pos < strLength) {\n        let value = str.charCodeAt(pos++);\n        if ((value & 0xffffff80) === 0) {\n            // 1-byte\n            byteLength++;\n            continue;\n        }\n        else if ((value & 0xfffff800) === 0) {\n            // 2-bytes\n            byteLength += 2;\n        }\n        else {\n            // handle surrogate pair\n            if (value >= 0xd800 && value <= 0xdbff) {\n                // high surrogate\n                if (pos < strLength) {\n                    const extra = str.charCodeAt(pos);\n                    if ((extra & 0xfc00) === 0xdc00) {\n                        ++pos;\n                        value = ((value & 0x3ff) << 10) + (extra & 0x3ff) + 0x10000;\n                    }\n                }\n            }\n            if ((value & 0xffff0000) === 0) {\n                // 3-byte\n                byteLength += 3;\n            }\n            else {\n                // 4-byte\n                byteLength += 4;\n            }\n        }\n    }\n    return byteLength;\n}\nfunction utf8EncodeJs(str, output, outputOffset) {\n    const strLength = str.length;\n    let offset = outputOffset;\n    let pos = 0;\n    while (pos < strLength) {\n        let value = str.charCodeAt(pos++);\n        if ((value & 0xffffff80) === 0) {\n            // 1-byte\n            output[offset++] = value;\n            continue;\n        }\n        else if ((value & 0xfffff800) === 0) {\n            // 2-bytes\n            output[offset++] = ((value >> 6) & 0x1f) | 0xc0;\n        }\n        else {\n            // handle surrogate pair\n            if (value >= 0xd800 && value <= 0xdbff) {\n                // high surrogate\n                if (pos < strLength) {\n                    const extra = str.charCodeAt(pos);\n                    if ((extra & 0xfc00) === 0xdc00) {\n                        ++pos;\n                        value = ((value & 0x3ff) << 10) + (extra & 0x3ff) + 0x10000;\n                    }\n                }\n            }\n            if ((value & 0xffff0000) === 0) {\n                // 3-byte\n                output[offset++] = ((value >> 12) & 0x0f) | 0xe0;\n                output[offset++] = ((value >> 6) & 0x3f) | 0x80;\n            }\n            else {\n                // 4-byte\n                output[offset++] = ((value >> 18) & 0x07) | 0xf0;\n                output[offset++] = ((value >> 12) & 0x3f) | 0x80;\n                output[offset++] = ((value >> 6) & 0x3f) | 0x80;\n            }\n        }\n        output[offset++] = (value & 0x3f) | 0x80;\n    }\n}\n// TextEncoder and TextDecoder are standardized in whatwg encoding:\n// https://encoding.spec.whatwg.org/\n// and available in all the modern browsers:\n// https://caniuse.com/textencoder\n// They are available in Node.js since v12 LTS as well:\n// https://nodejs.org/api/globals.html#textencoder\nconst sharedTextEncoder = new TextEncoder();\n// This threshold should be determined by benchmarking, which might vary in engines and input data.\n// Run `npx ts-node benchmark/encode-string.ts` for details.\nconst TEXT_ENCODER_THRESHOLD = 50;\nfunction utf8EncodeTE(str, output, outputOffset) {\n    sharedTextEncoder.encodeInto(str, output.subarray(outputOffset));\n}\nfunction utf8Encode(str, output, outputOffset) {\n    if (str.length > TEXT_ENCODER_THRESHOLD) {\n        utf8EncodeTE(str, output, outputOffset);\n    }\n    else {\n        utf8EncodeJs(str, output, outputOffset);\n    }\n}\nconst CHUNK_SIZE = 4096;\nfunction utf8DecodeJs(bytes, inputOffset, byteLength) {\n    let offset = inputOffset;\n    const end = offset + byteLength;\n    const units = [];\n    let result = \"\";\n    while (offset < end) {\n        const byte1 = bytes[offset++];\n        if ((byte1 & 0x80) === 0) {\n            // 1 byte\n            units.push(byte1);\n        }\n        else if ((byte1 & 0xe0) === 0xc0) {\n            // 2 bytes\n            const byte2 = bytes[offset++] & 0x3f;\n            units.push(((byte1 & 0x1f) << 6) | byte2);\n        }\n        else if ((byte1 & 0xf0) === 0xe0) {\n            // 3 bytes\n            const byte2 = bytes[offset++] & 0x3f;\n            const byte3 = bytes[offset++] & 0x3f;\n            units.push(((byte1 & 0x1f) << 12) | (byte2 << 6) | byte3);\n        }\n        else if ((byte1 & 0xf8) === 0xf0) {\n            // 4 bytes\n            const byte2 = bytes[offset++] & 0x3f;\n            const byte3 = bytes[offset++] & 0x3f;\n            const byte4 = bytes[offset++] & 0x3f;\n            let unit = ((byte1 & 0x07) << 0x12) | (byte2 << 0x0c) | (byte3 << 0x06) | byte4;\n            if (unit > 0xffff) {\n                unit -= 0x10000;\n                units.push(((unit >>> 10) & 0x3ff) | 0xd800);\n                unit = 0xdc00 | (unit & 0x3ff);\n            }\n            units.push(unit);\n        }\n        else {\n            units.push(byte1);\n        }\n        if (units.length >= CHUNK_SIZE) {\n            result += String.fromCharCode(...units);\n            units.length = 0;\n        }\n    }\n    if (units.length > 0) {\n        result += String.fromCharCode(...units);\n    }\n    return result;\n}\nconst sharedTextDecoder = new TextDecoder();\n// This threshold should be determined by benchmarking, which might vary in engines and input data.\n// Run `npx ts-node benchmark/decode-string.ts` for details.\nconst TEXT_DECODER_THRESHOLD = 200;\nfunction utf8DecodeTD(bytes, inputOffset, byteLength) {\n    const stringBytes = bytes.subarray(inputOffset, inputOffset + byteLength);\n    return sharedTextDecoder.decode(stringBytes);\n}\nfunction utf8Decode(bytes, inputOffset, byteLength) {\n    if (byteLength > TEXT_DECODER_THRESHOLD) {\n        return utf8DecodeTD(bytes, inputOffset, byteLength);\n    }\n    else {\n        return utf8DecodeJs(bytes, inputOffset, byteLength);\n    }\n}\n//# sourceMappingURL=utf8.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@msgpack/msgpack/dist.esm/utils/utf8.mjs\n");

/***/ })

};
;