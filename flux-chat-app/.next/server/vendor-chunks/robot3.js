"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/robot3";
exports.ids = ["vendor-chunks/robot3"];
exports.modules = {

/***/ "(rsc)/./node_modules/robot3/dist/machine.js":
/*!*********************************************!*\
  !*** ./node_modules/robot3/dist/machine.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n\nfunction valueEnumerable(value) {\n  return { enumerable: true, value };\n}\n\nfunction valueEnumerableWritable(value) {\n  return { enumerable: true, writable: true, value };\n}\n\nlet d = {};\nlet truthy = () => true;\nlet empty = () => ({});\nlet identity = a => a;\nlet callBoth = (par, fn, self, args) => par.apply(self, args) && fn.apply(self, args);\nlet callForward = (par, fn, self, [a, b]) => fn.call(self, par.call(self, a, b), b);\nlet create = (a, b) => Object.freeze(Object.create(a, b));\n\nfunction stack(fns, def, caller) {\n  return fns.reduce((par, fn) => {\n    return function(...args) {\n      return caller(par, fn, this, args);\n    };\n  }, def);\n}\n\nfunction fnType(fn) {\n  return create(this, { fn: valueEnumerable(fn) });\n}\n\nlet reduceType = {};\nlet reduce = fnType.bind(reduceType);\nlet action = fn => reduce((ctx, ev) => !!~fn(ctx, ev) && ctx);\n\nlet guardType = {};\nlet guard = fnType.bind(guardType);\n\nfunction filter(Type, arr) {\n  return arr.filter(value => Type.isPrototypeOf(value));\n}\n\nfunction makeTransition(from, to, ...args) {\n  let guards = stack(filter(guardType, args).map(t => t.fn), truthy, callBoth);\n  let reducers = stack(filter(reduceType, args).map(t => t.fn), identity, callForward);\n  return create(this, {\n    from: valueEnumerable(from),\n    to: valueEnumerable(to),\n    guards: valueEnumerable(guards),\n    reducers: valueEnumerable(reducers)\n  });\n}\n\nlet transitionType = {};\nlet immediateType = {};\nlet transition = makeTransition.bind(transitionType);\nlet immediate = makeTransition.bind(immediateType, null);\n\nfunction enterImmediate(machine, service, event) {\n  return transitionTo(service, machine, event, this.immediates) || machine;\n}\n\nfunction transitionsToMap(transitions) {\n  let m = new Map();\n  for(let t of transitions) {\n    if(!m.has(t.from)) m.set(t.from, []);\n    m.get(t.from).push(t);\n  }\n  return m;\n}\n\nlet stateType = { enter: identity };\nfunction state(...args) {\n  let transitions = filter(transitionType, args);\n  let immediates = filter(immediateType, args);\n  let desc = {\n    final: valueEnumerable(args.length === 0),\n    transitions: valueEnumerable(transitionsToMap(transitions))\n  };\n  if(immediates.length) {\n    desc.immediates = valueEnumerable(immediates);\n    desc.enter = valueEnumerable(enterImmediate);\n  }\n  return create(stateType, desc);\n}\n\nlet invokeFnType = {\n  enter(machine2, service, event) {\n    let rn = this.fn.call(service, service.context, event);\n    if(machine.isPrototypeOf(rn))\n      return create(invokeMachineType, {\n        machine: valueEnumerable(rn),\n        transitions: valueEnumerable(this.transitions)\n      }).enter(machine2, service, event)\n    rn.then(data => service.send({ type: 'done', data }))\n      .catch(error => service.send({ type: 'error', error }));\n    return machine2;\n  }\n};\nlet invokeMachineType = {\n  enter(machine, service, event) {\n    service.child = interpret(this.machine, s => {\n      service.onChange(s);\n      if(service.child == s && s.machine.state.value.final) {\n        delete service.child;\n        service.send({ type: 'done', data: s.context });\n      }\n    }, service.context, event);\n    if(service.child.machine.state.value.final) {\n      let data = service.child.context;\n      delete service.child;\n      return transitionTo(service, machine, { type: 'done', data }, this.transitions.get('done'));\n    }\n    return machine;\n  }\n};\nfunction invoke(fn, ...transitions) {\n  let t = valueEnumerable(transitionsToMap(transitions));\n  return machine.isPrototypeOf(fn) ?\n    create(invokeMachineType, {\n      machine: valueEnumerable(fn),\n      transitions: t\n    }) :\n    create(invokeFnType, {\n      fn: valueEnumerable(fn),\n      transitions: t\n    });\n}\n\nlet machine = {\n  get state() {\n    return {\n      name: this.current,\n      value: this.states[this.current]\n    };\n  }\n};\n\nfunction createMachine(current, states, contextFn = empty) {\n  if(typeof current !== 'string') {\n    contextFn = states || empty;\n    states = current;\n    current = Object.keys(states)[0];\n  }\n  if(d._create) d._create(current, states);\n  return create(machine, {\n    context: valueEnumerable(contextFn),\n    current: valueEnumerable(current),\n    states: valueEnumerable(states)\n  });\n}\n\nfunction transitionTo(service, machine, fromEvent, candidates) {\n  let { context } = service;\n  for(let { to, guards, reducers } of candidates) {  \n    if(guards(context, fromEvent)) {\n      service.context = reducers.call(service, context, fromEvent);\n\n      let original = machine.original || machine;\n      let newMachine = create(original, {\n        current: valueEnumerable(to),\n        original: { value: original }\n      });\n\n      if (d._onEnter) d._onEnter(machine, to, service.context, context, fromEvent);\n      let state = newMachine.state.value;\n      return state.enter(newMachine, service, fromEvent);\n    }\n  }\n}\n\nfunction send(service, event) {\n  let eventName = event.type || event;\n  let { machine } = service;\n  let { value: state, name: currentStateName } = machine.state;\n  \n  if(state.transitions.has(eventName)) {\n    return transitionTo(service, machine, event, state.transitions.get(eventName)) || machine;\n  } else {\n    if(d._send) d._send(eventName, currentStateName);\n  }\n  return machine;\n}\n\nlet service = {\n  send(event) {\n    this.machine = send(this, event);\n    \n    // TODO detect change\n    this.onChange(this);\n  }\n};\n\nfunction interpret(machine, onChange, initialContext, event) {\n  let s = Object.create(service, {\n    machine: valueEnumerableWritable(machine),\n    context: valueEnumerableWritable(machine.context(initialContext, event)),\n    onChange: valueEnumerable(onChange)\n  });\n  s.send = s.send.bind(s);\n  s.machine = s.machine.state.value.enter(s.machine, s, event);\n  return s;\n}\n\nexports.action = action;\nexports.createMachine = createMachine;\nexports.d = d;\nexports.guard = guard;\nexports.immediate = immediate;\nexports.interpret = interpret;\nexports.invoke = invoke;\nexports.reduce = reduce;\nexports.state = state;\nexports.transition = transition;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/robot3/dist/machine.js\n");

/***/ })

};
;