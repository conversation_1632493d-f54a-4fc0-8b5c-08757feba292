"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@fal-ai";
exports.ids = ["vendor-chunks/@fal-ai"];
exports.modules = {

/***/ "(rsc)/./node_modules/@fal-ai/serverless-client/package.json":
/*!*************************************************************!*\
  !*** ./node_modules/@fal-ai/serverless-client/package.json ***!
  \*************************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"name":"@fal-ai/serverless-client","description":"Deprecation note: this library has been deprecated in favor of @fal-ai/client","version":"0.15.0","license":"MIT","repository":{"type":"git","url":"https://github.com/fal-ai/fal-js.git","directory":"libs/client"},"keywords":["fal","serverless","client","ai","ml"],"dependencies":{"@msgpack/msgpack":"^3.0.0-beta2","eventsource-parser":"^1.1.2","robot3":"^0.4.1"},"engines":{"node":">=18.0.0"},"main":"./src/index.js","type":"commonjs"}');

/***/ }),

/***/ "(rsc)/./node_modules/@fal-ai/serverless-client/src/auth.js":
/*!************************************************************!*\
  !*** ./node_modules/@fal-ai/serverless-client/src/auth.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.TOKEN_EXPIRATION_SECONDS = void 0;\nexports.getTemporaryAuthToken = getTemporaryAuthToken;\nconst config_1 = __webpack_require__(/*! ./config */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/config.js\");\nconst request_1 = __webpack_require__(/*! ./request */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/request.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/utils.js\");\nexports.TOKEN_EXPIRATION_SECONDS = 120;\n/**\n * Get a token to connect to the realtime endpoint.\n */\nfunction getTemporaryAuthToken(app) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const appId = (0, utils_1.parseAppId)(app);\n        const token = yield (0, request_1.dispatchRequest)(\"POST\", `${(0, config_1.getRestApiUrl)()}/tokens/`, {\n            allowed_apps: [appId.alias],\n            token_expiration: exports.TOKEN_EXPIRATION_SECONDS,\n        });\n        // keep this in case the response was wrapped (old versions of the proxy do that)\n        // should be safe to remove in the future\n        if (typeof token !== \"string\" && token[\"detail\"]) {\n            return token[\"detail\"];\n        }\n        return token;\n    });\n}\n//# sourceMappingURL=auth.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGZhbC1haS9zZXJ2ZXJsZXNzLWNsaWVudC9zcmMvYXV0aC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0EsNEJBQTRCLCtEQUErRCxpQkFBaUI7QUFDNUc7QUFDQSxvQ0FBb0MsTUFBTSwrQkFBK0IsWUFBWTtBQUNyRixtQ0FBbUMsTUFBTSxtQ0FBbUMsWUFBWTtBQUN4RixnQ0FBZ0M7QUFDaEM7QUFDQSxLQUFLO0FBQ0w7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsZ0NBQWdDO0FBQ2hDLDZCQUE2QjtBQUM3QixpQkFBaUIsbUJBQU8sQ0FBQyw4RUFBVTtBQUNuQyxrQkFBa0IsbUJBQU8sQ0FBQyxnRkFBVztBQUNyQyxnQkFBZ0IsbUJBQU8sQ0FBQyw0RUFBUztBQUNqQyxnQ0FBZ0M7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0VBQXNFLDhCQUE4QjtBQUNwRztBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvbGl1cGVuZy9jdXJzb3Iv6L+b6Zi2L0ZsdXjlr7nor53lvI/lupTnlKgvZmx1eC1jaGF0LWFwcC9ub2RlX21vZHVsZXMvQGZhbC1haS9zZXJ2ZXJsZXNzLWNsaWVudC9zcmMvYXV0aC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfX2F3YWl0ZXIgPSAodGhpcyAmJiB0aGlzLl9fYXdhaXRlcikgfHwgZnVuY3Rpb24gKHRoaXNBcmcsIF9hcmd1bWVudHMsIFAsIGdlbmVyYXRvcikge1xuICAgIGZ1bmN0aW9uIGFkb3B0KHZhbHVlKSB7IHJldHVybiB2YWx1ZSBpbnN0YW5jZW9mIFAgPyB2YWx1ZSA6IG5ldyBQKGZ1bmN0aW9uIChyZXNvbHZlKSB7IHJlc29sdmUodmFsdWUpOyB9KTsgfVxuICAgIHJldHVybiBuZXcgKFAgfHwgKFAgPSBQcm9taXNlKSkoZnVuY3Rpb24gKHJlc29sdmUsIHJlamVjdCkge1xuICAgICAgICBmdW5jdGlvbiBmdWxmaWxsZWQodmFsdWUpIHsgdHJ5IHsgc3RlcChnZW5lcmF0b3IubmV4dCh2YWx1ZSkpOyB9IGNhdGNoIChlKSB7IHJlamVjdChlKTsgfSB9XG4gICAgICAgIGZ1bmN0aW9uIHJlamVjdGVkKHZhbHVlKSB7IHRyeSB7IHN0ZXAoZ2VuZXJhdG9yW1widGhyb3dcIl0odmFsdWUpKTsgfSBjYXRjaCAoZSkgeyByZWplY3QoZSk7IH0gfVxuICAgICAgICBmdW5jdGlvbiBzdGVwKHJlc3VsdCkgeyByZXN1bHQuZG9uZSA/IHJlc29sdmUocmVzdWx0LnZhbHVlKSA6IGFkb3B0KHJlc3VsdC52YWx1ZSkudGhlbihmdWxmaWxsZWQsIHJlamVjdGVkKTsgfVxuICAgICAgICBzdGVwKChnZW5lcmF0b3IgPSBnZW5lcmF0b3IuYXBwbHkodGhpc0FyZywgX2FyZ3VtZW50cyB8fCBbXSkpLm5leHQoKSk7XG4gICAgfSk7XG59O1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5UT0tFTl9FWFBJUkFUSU9OX1NFQ09ORFMgPSB2b2lkIDA7XG5leHBvcnRzLmdldFRlbXBvcmFyeUF1dGhUb2tlbiA9IGdldFRlbXBvcmFyeUF1dGhUb2tlbjtcbmNvbnN0IGNvbmZpZ18xID0gcmVxdWlyZShcIi4vY29uZmlnXCIpO1xuY29uc3QgcmVxdWVzdF8xID0gcmVxdWlyZShcIi4vcmVxdWVzdFwiKTtcbmNvbnN0IHV0aWxzXzEgPSByZXF1aXJlKFwiLi91dGlsc1wiKTtcbmV4cG9ydHMuVE9LRU5fRVhQSVJBVElPTl9TRUNPTkRTID0gMTIwO1xuLyoqXG4gKiBHZXQgYSB0b2tlbiB0byBjb25uZWN0IHRvIHRoZSByZWFsdGltZSBlbmRwb2ludC5cbiAqL1xuZnVuY3Rpb24gZ2V0VGVtcG9yYXJ5QXV0aFRva2VuKGFwcCkge1xuICAgIHJldHVybiBfX2F3YWl0ZXIodGhpcywgdm9pZCAwLCB2b2lkIDAsIGZ1bmN0aW9uKiAoKSB7XG4gICAgICAgIGNvbnN0IGFwcElkID0gKDAsIHV0aWxzXzEucGFyc2VBcHBJZCkoYXBwKTtcbiAgICAgICAgY29uc3QgdG9rZW4gPSB5aWVsZCAoMCwgcmVxdWVzdF8xLmRpc3BhdGNoUmVxdWVzdCkoXCJQT1NUXCIsIGAkeygwLCBjb25maWdfMS5nZXRSZXN0QXBpVXJsKSgpfS90b2tlbnMvYCwge1xuICAgICAgICAgICAgYWxsb3dlZF9hcHBzOiBbYXBwSWQuYWxpYXNdLFxuICAgICAgICAgICAgdG9rZW5fZXhwaXJhdGlvbjogZXhwb3J0cy5UT0tFTl9FWFBJUkFUSU9OX1NFQ09ORFMsXG4gICAgICAgIH0pO1xuICAgICAgICAvLyBrZWVwIHRoaXMgaW4gY2FzZSB0aGUgcmVzcG9uc2Ugd2FzIHdyYXBwZWQgKG9sZCB2ZXJzaW9ucyBvZiB0aGUgcHJveHkgZG8gdGhhdClcbiAgICAgICAgLy8gc2hvdWxkIGJlIHNhZmUgdG8gcmVtb3ZlIGluIHRoZSBmdXR1cmVcbiAgICAgICAgaWYgKHR5cGVvZiB0b2tlbiAhPT0gXCJzdHJpbmdcIiAmJiB0b2tlbltcImRldGFpbFwiXSkge1xuICAgICAgICAgICAgcmV0dXJuIHRva2VuW1wiZGV0YWlsXCJdO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0b2tlbjtcbiAgICB9KTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWF1dGguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@fal-ai/serverless-client/src/auth.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@fal-ai/serverless-client/src/config.js":
/*!**************************************************************!*\
  !*** ./node_modules/@fal-ai/serverless-client/src/config.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.credentialsFromEnv = void 0;\nexports.resolveDefaultFetch = resolveDefaultFetch;\nexports.config = config;\nexports.getConfig = getConfig;\nexports.getRestApiUrl = getRestApiUrl;\nconst middleware_1 = __webpack_require__(/*! ./middleware */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/middleware.js\");\nconst response_1 = __webpack_require__(/*! ./response */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/response.js\");\nfunction resolveDefaultFetch() {\n    if (typeof fetch === \"undefined\") {\n        throw new Error(\"Your environment does not support fetch. Please provide your own fetch implementation.\");\n    }\n    return fetch;\n}\n/**\n * Checks if the required FAL environment variables are set.\n *\n * @returns `true` if the required environment variables are set,\n * `false` otherwise.\n */\nfunction hasEnvVariables() {\n    return (typeof process !== \"undefined\" &&\n        process.env &&\n        (typeof process.env.FAL_KEY !== \"undefined\" ||\n            (typeof process.env.FAL_KEY_ID !== \"undefined\" &&\n                typeof process.env.FAL_KEY_SECRET !== \"undefined\")));\n}\nconst credentialsFromEnv = () => {\n    if (!hasEnvVariables()) {\n        return undefined;\n    }\n    if (typeof process.env.FAL_KEY !== \"undefined\") {\n        return process.env.FAL_KEY;\n    }\n    return `${process.env.FAL_KEY_ID}:${process.env.FAL_KEY_SECRET}`;\n};\nexports.credentialsFromEnv = credentialsFromEnv;\nconst DEFAULT_CONFIG = {\n    credentials: exports.credentialsFromEnv,\n    suppressLocalCredentialsWarning: false,\n    requestMiddleware: (request) => Promise.resolve(request),\n    responseHandler: response_1.defaultResponseHandler,\n};\nlet configuration;\n/**\n * Configures the fal serverless client.\n *\n * @param config the new configuration.\n */\nfunction config(config) {\n    var _a;\n    configuration = Object.assign(Object.assign(Object.assign({}, DEFAULT_CONFIG), config), { fetch: (_a = config.fetch) !== null && _a !== void 0 ? _a : resolveDefaultFetch() });\n    if (config.proxyUrl) {\n        configuration = Object.assign(Object.assign({}, configuration), { requestMiddleware: (0, middleware_1.withMiddleware)((0, middleware_1.withProxy)({ targetUrl: config.proxyUrl }), configuration.requestMiddleware) });\n    }\n    const { credentials: resolveCredentials, suppressLocalCredentialsWarning } = configuration;\n    const credentials = typeof resolveCredentials === \"function\"\n        ? resolveCredentials()\n        : resolveCredentials;\n    if (typeof window !== \"undefined\" &&\n        credentials &&\n        !suppressLocalCredentialsWarning) {\n        console.warn(\"The fal credentials are exposed in the browser's environment. \" +\n            \"That's not recommended for production use cases.\");\n    }\n}\n/**\n * Get the current fal serverless client configuration.\n *\n * @returns the current client configuration.\n */\nfunction getConfig() {\n    if (!configuration) {\n        console.info(\"Using default configuration for the fal client\");\n        return Object.assign(Object.assign({}, DEFAULT_CONFIG), { fetch: resolveDefaultFetch() });\n    }\n    return configuration;\n}\n/**\n * @returns the URL of the fal serverless rest api endpoint.\n */\nfunction getRestApiUrl() {\n    return \"https://rest.alpha.fal.ai\";\n}\n//# sourceMappingURL=config.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@fal-ai/serverless-client/src/config.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@fal-ai/serverless-client/src/function.js":
/*!****************************************************************!*\
  !*** ./node_modules/@fal-ai/serverless-client/src/function.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __rest = (this && this.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.queue = void 0;\nexports.buildUrl = buildUrl;\nexports.send = send;\nexports.run = run;\nexports.subscribe = subscribe;\nconst request_1 = __webpack_require__(/*! ./request */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/request.js\");\nconst storage_1 = __webpack_require__(/*! ./storage */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/storage.js\");\nconst streaming_1 = __webpack_require__(/*! ./streaming */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/streaming.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/utils.js\");\n/**\n * Builds the final url to run the function based on its `id` or alias and\n * a the options from `RunOptions<Input>`.\n *\n * @private\n * @param id the function id or alias\n * @param options the run options\n * @returns the final url to run the function\n */\nfunction buildUrl(id, options = {}) {\n    var _a, _b;\n    const method = ((_a = options.method) !== null && _a !== void 0 ? _a : \"post\").toLowerCase();\n    const path = ((_b = options.path) !== null && _b !== void 0 ? _b : \"\").replace(/^\\//, \"\").replace(/\\/{2,}/, \"/\");\n    const input = options.input;\n    const params = Object.assign(Object.assign({}, (options.query || {})), (method === \"get\" ? input : {}));\n    const queryParams = Object.keys(params).length > 0\n        ? `?${new URLSearchParams(params).toString()}`\n        : \"\";\n    // if a fal url is passed, just use it\n    if ((0, utils_1.isValidUrl)(id)) {\n        const url = id.endsWith(\"/\") ? id : `${id}/`;\n        return `${url}${path}${queryParams}`;\n    }\n    const appId = (0, utils_1.ensureAppIdFormat)(id);\n    const subdomain = options.subdomain ? `${options.subdomain}.` : \"\";\n    const url = `https://${subdomain}fal.run/${appId}/${path}`;\n    return `${url.replace(/\\/$/, \"\")}${queryParams}`;\n}\nfunction send(id_1) {\n    return __awaiter(this, arguments, void 0, function* (id, options = {}) {\n        var _a;\n        const input = options.input && options.autoUpload !== false\n            ? yield storage_1.storageImpl.transformInput(options.input)\n            : options.input;\n        return (0, request_1.dispatchRequest)((_a = options.method) !== null && _a !== void 0 ? _a : \"post\", buildUrl(id, options), input);\n    });\n}\n/**\n * Runs a fal serverless function identified by its `id`.\n *\n * @param id the registered function revision id or alias.\n * @returns the remote function output\n */\nfunction run(id_1) {\n    return __awaiter(this, arguments, void 0, function* (id, options = {}) {\n        return send(id, options);\n    });\n}\nconst DEFAULT_POLL_INTERVAL = 500;\n/**\n * The fal run queue module. It allows to submit a function to the queue and get its result\n * on a separate call. This is useful for long running functions that can be executed\n * asynchronously and not .\n */\nexports.queue = {\n    submit(endpointId, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const { webhookUrl, path = \"\" } = options, runOptions = __rest(options, [\"webhookUrl\", \"path\"]);\n            return send(endpointId, Object.assign(Object.assign({}, runOptions), { subdomain: \"queue\", method: \"post\", path: path, query: webhookUrl ? { fal_webhook: webhookUrl } : undefined }));\n        });\n    },\n    status(endpointId_1, _a) {\n        return __awaiter(this, arguments, void 0, function* (endpointId, { requestId, logs = false }) {\n            const appId = (0, utils_1.parseAppId)(endpointId);\n            const prefix = appId.namespace ? `${appId.namespace}/` : \"\";\n            return send(`${prefix}${appId.owner}/${appId.alias}`, {\n                subdomain: \"queue\",\n                method: \"get\",\n                path: `/requests/${requestId}/status`,\n                input: {\n                    logs: logs ? \"1\" : \"0\",\n                },\n            });\n        });\n    },\n    streamStatus(endpointId_1, _a) {\n        return __awaiter(this, arguments, void 0, function* (endpointId, { requestId, logs = false, connectionMode }) {\n            const appId = (0, utils_1.parseAppId)(endpointId);\n            const prefix = appId.namespace ? `${appId.namespace}/` : \"\";\n            const queryParams = {\n                logs: logs ? \"1\" : \"0\",\n            };\n            const url = buildUrl(`${prefix}${appId.owner}/${appId.alias}`, {\n                subdomain: \"queue\",\n                path: `/requests/${requestId}/status/stream`,\n                query: queryParams,\n            });\n            return new streaming_1.FalStream(endpointId, {\n                url,\n                method: \"get\",\n                connectionMode,\n                queryParams,\n            });\n        });\n    },\n    subscribeToStatus(endpointId, options) {\n        return __awaiter(this, void 0, void 0, function* () {\n            const requestId = options.requestId;\n            const timeout = options.timeout;\n            let timeoutId = undefined;\n            const handleCancelError = () => {\n                // Ignore errors as the client will follow through with the timeout\n                // regardless of the server response. In case cancelation fails, we\n                // still want to reject the promise and consider the client call canceled.\n            };\n            if (options.mode === \"streaming\") {\n                const status = yield exports.queue.streamStatus(endpointId, {\n                    requestId,\n                    logs: options.logs,\n                    connectionMode: \"connectionMode\" in options\n                        ? options.connectionMode\n                        : undefined,\n                });\n                const logs = [];\n                if (timeout) {\n                    timeoutId = setTimeout(() => {\n                        status.abort();\n                        exports.queue.cancel(endpointId, { requestId }).catch(handleCancelError);\n                        // TODO this error cannot bubble up to the user since it's thrown in\n                        // a closure in the global scope due to setTimeout behavior.\n                        // User will get a platform error instead. We should find a way to\n                        // make this behavior aligned with polling.\n                        throw new Error(`Client timed out waiting for the request to complete after ${timeout}ms`);\n                    }, timeout);\n                }\n                status.on(\"data\", (data) => {\n                    if (options.onQueueUpdate) {\n                        // accumulate logs to match previous polling behavior\n                        if (\"logs\" in data &&\n                            Array.isArray(data.logs) &&\n                            data.logs.length > 0) {\n                            logs.push(...data.logs);\n                        }\n                        options.onQueueUpdate(\"logs\" in data ? Object.assign(Object.assign({}, data), { logs }) : data);\n                    }\n                });\n                const doneStatus = yield status.done();\n                if (timeoutId) {\n                    clearTimeout(timeoutId);\n                }\n                return doneStatus;\n            }\n            // default to polling until status streaming is stable and faster\n            return new Promise((resolve, reject) => {\n                var _a;\n                let pollingTimeoutId;\n                // type resolution isn't great in this case, so check for its presence\n                // and and type so the typechecker behaves as expected\n                const pollInterval = \"pollInterval\" in options && typeof options.pollInterval === \"number\"\n                    ? ((_a = options.pollInterval) !== null && _a !== void 0 ? _a : DEFAULT_POLL_INTERVAL)\n                    : DEFAULT_POLL_INTERVAL;\n                const clearScheduledTasks = () => {\n                    if (timeoutId) {\n                        clearTimeout(timeoutId);\n                    }\n                    if (pollingTimeoutId) {\n                        clearTimeout(pollingTimeoutId);\n                    }\n                };\n                if (timeout) {\n                    timeoutId = setTimeout(() => {\n                        clearScheduledTasks();\n                        exports.queue.cancel(endpointId, { requestId }).catch(handleCancelError);\n                        reject(new Error(`Client timed out waiting for the request to complete after ${timeout}ms`));\n                    }, timeout);\n                }\n                const poll = () => __awaiter(this, void 0, void 0, function* () {\n                    var _a;\n                    try {\n                        const requestStatus = yield exports.queue.status(endpointId, {\n                            requestId,\n                            logs: (_a = options.logs) !== null && _a !== void 0 ? _a : false,\n                        });\n                        if (options.onQueueUpdate) {\n                            options.onQueueUpdate(requestStatus);\n                        }\n                        if (requestStatus.status === \"COMPLETED\") {\n                            clearScheduledTasks();\n                            resolve(requestStatus);\n                            return;\n                        }\n                        pollingTimeoutId = setTimeout(poll, pollInterval);\n                    }\n                    catch (error) {\n                        clearScheduledTasks();\n                        reject(error);\n                    }\n                });\n                poll().catch(reject);\n            });\n        });\n    },\n    result(endpointId_1, _a) {\n        return __awaiter(this, arguments, void 0, function* (endpointId, { requestId }) {\n            const appId = (0, utils_1.parseAppId)(endpointId);\n            const prefix = appId.namespace ? `${appId.namespace}/` : \"\";\n            return send(`${prefix}${appId.owner}/${appId.alias}`, {\n                subdomain: \"queue\",\n                method: \"get\",\n                path: `/requests/${requestId}`,\n            });\n        });\n    },\n    cancel(endpointId_1, _a) {\n        return __awaiter(this, arguments, void 0, function* (endpointId, { requestId }) {\n            const appId = (0, utils_1.parseAppId)(endpointId);\n            const prefix = appId.namespace ? `${appId.namespace}/` : \"\";\n            yield send(`${prefix}${appId.owner}/${appId.alias}`, {\n                subdomain: \"queue\",\n                method: \"put\",\n                path: `/requests/${requestId}/cancel`,\n            });\n        });\n    },\n};\n/**\n * Subscribes to updates for a specific request in the queue.\n *\n * @param endpointId - The ID of the function web endpoint.\n * @param options - Options to configure how the request is run and how updates are received.\n * @returns A promise that resolves to the result of the request once it's completed.\n */\nfunction subscribe(endpointId_1) {\n    return __awaiter(this, arguments, void 0, function* (endpointId, options = {}) {\n        const { request_id: requestId } = yield exports.queue.submit(endpointId, options);\n        if (options.onEnqueue) {\n            options.onEnqueue(requestId);\n        }\n        yield exports.queue.subscribeToStatus(endpointId, Object.assign({ requestId }, options));\n        return exports.queue.result(endpointId, { requestId });\n    });\n}\n//# sourceMappingURL=function.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGZhbC1haS9zZXJ2ZXJsZXNzLWNsaWVudC9zcmMvZnVuY3Rpb24uanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBLDRCQUE0QiwrREFBK0QsaUJBQWlCO0FBQzVHO0FBQ0Esb0NBQW9DLE1BQU0sK0JBQStCLFlBQVk7QUFDckYsbUNBQW1DLE1BQU0sbUNBQW1DLFlBQVk7QUFDeEYsZ0NBQWdDO0FBQ2hDO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZEQUE2RCxjQUFjO0FBQzNFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsYUFBYTtBQUNiLGdCQUFnQjtBQUNoQixZQUFZO0FBQ1osV0FBVztBQUNYLGlCQUFpQjtBQUNqQixrQkFBa0IsbUJBQU8sQ0FBQyxnRkFBVztBQUNyQyxrQkFBa0IsbUJBQU8sQ0FBQyxnRkFBVztBQUNyQyxvQkFBb0IsbUJBQU8sQ0FBQyxvRkFBYTtBQUN6QyxnQkFBZ0IsbUJBQU8sQ0FBQyw0RUFBUztBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0M7QUFDbEM7QUFDQTtBQUNBLDBHQUEwRyxHQUFHO0FBQzdHO0FBQ0EsaURBQWlELHNCQUFzQixrQ0FBa0M7QUFDekc7QUFDQSxjQUFjLHVDQUF1QztBQUNyRDtBQUNBO0FBQ0E7QUFDQSwrQ0FBK0MsR0FBRztBQUNsRCxrQkFBa0IsSUFBSSxFQUFFLEtBQUssRUFBRSxZQUFZO0FBQzNDO0FBQ0E7QUFDQSw2Q0FBNkMsa0JBQWtCO0FBQy9ELDJCQUEyQixVQUFVLFVBQVUsTUFBTSxHQUFHLEtBQUs7QUFDN0QsY0FBYyx1QkFBdUIsRUFBRSxZQUFZO0FBQ25EO0FBQ0E7QUFDQSx5RUFBeUU7QUFDekU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUVBQXlFO0FBQ3pFO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxvQkFBb0Isd0JBQXdCO0FBQzVDLGtFQUFrRSxpQkFBaUIsc0VBQXNFLDBCQUEwQixhQUFhO0FBQ2hNLFNBQVM7QUFDVCxLQUFLO0FBQ0w7QUFDQSwyRUFBMkUseUJBQXlCO0FBQ3BHO0FBQ0EsZ0RBQWdELGdCQUFnQjtBQUNoRSwyQkFBMkIsT0FBTyxFQUFFLFlBQVksR0FBRyxZQUFZO0FBQy9EO0FBQ0E7QUFDQSxtQ0FBbUMsVUFBVTtBQUM3QztBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCLGFBQWE7QUFDYixTQUFTO0FBQ1QsS0FBSztBQUNMO0FBQ0EsMkVBQTJFLHlDQUF5QztBQUNwSDtBQUNBLGdEQUFnRCxnQkFBZ0I7QUFDaEU7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLE9BQU8sRUFBRSxZQUFZLEdBQUcsWUFBWTtBQUN4RTtBQUNBLG1DQUFtQyxVQUFVO0FBQzdDO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkRBQTJELFdBQVc7QUFDdEU7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzR0FBc0csUUFBUTtBQUM5RyxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkZBQTZGLFdBQVcsTUFBTTtBQUM5RztBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkRBQTJELFdBQVc7QUFDdEUsdUdBQXVHLFFBQVE7QUFDL0cscUJBQXFCO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQSxhQUFhO0FBQ2IsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBLDJFQUEyRSxXQUFXO0FBQ3RGO0FBQ0EsZ0RBQWdELGdCQUFnQjtBQUNoRSwyQkFBMkIsT0FBTyxFQUFFLFlBQVksR0FBRyxZQUFZO0FBQy9EO0FBQ0E7QUFDQSxtQ0FBbUMsVUFBVTtBQUM3QyxhQUFhO0FBQ2IsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBLDJFQUEyRSxXQUFXO0FBQ3RGO0FBQ0EsZ0RBQWdELGdCQUFnQjtBQUNoRSwwQkFBMEIsT0FBTyxFQUFFLFlBQVksR0FBRyxZQUFZO0FBQzlEO0FBQ0E7QUFDQSxtQ0FBbUMsVUFBVTtBQUM3QyxhQUFhO0FBQ2IsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpRkFBaUY7QUFDakYsZ0JBQWdCLHdCQUF3QjtBQUN4QztBQUNBO0FBQ0E7QUFDQSwwRUFBMEUsV0FBVztBQUNyRixrREFBa0QsV0FBVztBQUM3RCxLQUFLO0FBQ0w7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2xpdXBlbmcvY3Vyc29yL+i/m+mYti9GbHV45a+56K+d5byP5bqU55SoL2ZsdXgtY2hhdC1hcHAvbm9kZV9tb2R1bGVzL0BmYWwtYWkvc2VydmVybGVzcy1jbGllbnQvc3JjL2Z1bmN0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xudmFyIF9fYXdhaXRlciA9ICh0aGlzICYmIHRoaXMuX19hd2FpdGVyKSB8fCBmdW5jdGlvbiAodGhpc0FyZywgX2FyZ3VtZW50cywgUCwgZ2VuZXJhdG9yKSB7XG4gICAgZnVuY3Rpb24gYWRvcHQodmFsdWUpIHsgcmV0dXJuIHZhbHVlIGluc3RhbmNlb2YgUCA/IHZhbHVlIDogbmV3IFAoZnVuY3Rpb24gKHJlc29sdmUpIHsgcmVzb2x2ZSh2YWx1ZSk7IH0pOyB9XG4gICAgcmV0dXJuIG5ldyAoUCB8fCAoUCA9IFByb21pc2UpKShmdW5jdGlvbiAocmVzb2x2ZSwgcmVqZWN0KSB7XG4gICAgICAgIGZ1bmN0aW9uIGZ1bGZpbGxlZCh2YWx1ZSkgeyB0cnkgeyBzdGVwKGdlbmVyYXRvci5uZXh0KHZhbHVlKSk7IH0gY2F0Y2ggKGUpIHsgcmVqZWN0KGUpOyB9IH1cbiAgICAgICAgZnVuY3Rpb24gcmVqZWN0ZWQodmFsdWUpIHsgdHJ5IHsgc3RlcChnZW5lcmF0b3JbXCJ0aHJvd1wiXSh2YWx1ZSkpOyB9IGNhdGNoIChlKSB7IHJlamVjdChlKTsgfSB9XG4gICAgICAgIGZ1bmN0aW9uIHN0ZXAocmVzdWx0KSB7IHJlc3VsdC5kb25lID8gcmVzb2x2ZShyZXN1bHQudmFsdWUpIDogYWRvcHQocmVzdWx0LnZhbHVlKS50aGVuKGZ1bGZpbGxlZCwgcmVqZWN0ZWQpOyB9XG4gICAgICAgIHN0ZXAoKGdlbmVyYXRvciA9IGdlbmVyYXRvci5hcHBseSh0aGlzQXJnLCBfYXJndW1lbnRzIHx8IFtdKSkubmV4dCgpKTtcbiAgICB9KTtcbn07XG52YXIgX19yZXN0ID0gKHRoaXMgJiYgdGhpcy5fX3Jlc3QpIHx8IGZ1bmN0aW9uIChzLCBlKSB7XG4gICAgdmFyIHQgPSB7fTtcbiAgICBmb3IgKHZhciBwIGluIHMpIGlmIChPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwocywgcCkgJiYgZS5pbmRleE9mKHApIDwgMClcbiAgICAgICAgdFtwXSA9IHNbcF07XG4gICAgaWYgKHMgIT0gbnVsbCAmJiB0eXBlb2YgT2JqZWN0LmdldE93blByb3BlcnR5U3ltYm9scyA9PT0gXCJmdW5jdGlvblwiKVxuICAgICAgICBmb3IgKHZhciBpID0gMCwgcCA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHMocyk7IGkgPCBwLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgICBpZiAoZS5pbmRleE9mKHBbaV0pIDwgMCAmJiBPYmplY3QucHJvdG90eXBlLnByb3BlcnR5SXNFbnVtZXJhYmxlLmNhbGwocywgcFtpXSkpXG4gICAgICAgICAgICAgICAgdFtwW2ldXSA9IHNbcFtpXV07XG4gICAgICAgIH1cbiAgICByZXR1cm4gdDtcbn07XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLnF1ZXVlID0gdm9pZCAwO1xuZXhwb3J0cy5idWlsZFVybCA9IGJ1aWxkVXJsO1xuZXhwb3J0cy5zZW5kID0gc2VuZDtcbmV4cG9ydHMucnVuID0gcnVuO1xuZXhwb3J0cy5zdWJzY3JpYmUgPSBzdWJzY3JpYmU7XG5jb25zdCByZXF1ZXN0XzEgPSByZXF1aXJlKFwiLi9yZXF1ZXN0XCIpO1xuY29uc3Qgc3RvcmFnZV8xID0gcmVxdWlyZShcIi4vc3RvcmFnZVwiKTtcbmNvbnN0IHN0cmVhbWluZ18xID0gcmVxdWlyZShcIi4vc3RyZWFtaW5nXCIpO1xuY29uc3QgdXRpbHNfMSA9IHJlcXVpcmUoXCIuL3V0aWxzXCIpO1xuLyoqXG4gKiBCdWlsZHMgdGhlIGZpbmFsIHVybCB0byBydW4gdGhlIGZ1bmN0aW9uIGJhc2VkIG9uIGl0cyBgaWRgIG9yIGFsaWFzIGFuZFxuICogYSB0aGUgb3B0aW9ucyBmcm9tIGBSdW5PcHRpb25zPElucHV0PmAuXG4gKlxuICogQHByaXZhdGVcbiAqIEBwYXJhbSBpZCB0aGUgZnVuY3Rpb24gaWQgb3IgYWxpYXNcbiAqIEBwYXJhbSBvcHRpb25zIHRoZSBydW4gb3B0aW9uc1xuICogQHJldHVybnMgdGhlIGZpbmFsIHVybCB0byBydW4gdGhlIGZ1bmN0aW9uXG4gKi9cbmZ1bmN0aW9uIGJ1aWxkVXJsKGlkLCBvcHRpb25zID0ge30pIHtcbiAgICB2YXIgX2EsIF9iO1xuICAgIGNvbnN0IG1ldGhvZCA9ICgoX2EgPSBvcHRpb25zLm1ldGhvZCkgIT09IG51bGwgJiYgX2EgIT09IHZvaWQgMCA/IF9hIDogXCJwb3N0XCIpLnRvTG93ZXJDYXNlKCk7XG4gICAgY29uc3QgcGF0aCA9ICgoX2IgPSBvcHRpb25zLnBhdGgpICE9PSBudWxsICYmIF9iICE9PSB2b2lkIDAgPyBfYiA6IFwiXCIpLnJlcGxhY2UoL15cXC8vLCBcIlwiKS5yZXBsYWNlKC9cXC97Mix9LywgXCIvXCIpO1xuICAgIGNvbnN0IGlucHV0ID0gb3B0aW9ucy5pbnB1dDtcbiAgICBjb25zdCBwYXJhbXMgPSBPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIChvcHRpb25zLnF1ZXJ5IHx8IHt9KSksIChtZXRob2QgPT09IFwiZ2V0XCIgPyBpbnB1dCA6IHt9KSk7XG4gICAgY29uc3QgcXVlcnlQYXJhbXMgPSBPYmplY3Qua2V5cyhwYXJhbXMpLmxlbmd0aCA+IDBcbiAgICAgICAgPyBgPyR7bmV3IFVSTFNlYXJjaFBhcmFtcyhwYXJhbXMpLnRvU3RyaW5nKCl9YFxuICAgICAgICA6IFwiXCI7XG4gICAgLy8gaWYgYSBmYWwgdXJsIGlzIHBhc3NlZCwganVzdCB1c2UgaXRcbiAgICBpZiAoKDAsIHV0aWxzXzEuaXNWYWxpZFVybCkoaWQpKSB7XG4gICAgICAgIGNvbnN0IHVybCA9IGlkLmVuZHNXaXRoKFwiL1wiKSA/IGlkIDogYCR7aWR9L2A7XG4gICAgICAgIHJldHVybiBgJHt1cmx9JHtwYXRofSR7cXVlcnlQYXJhbXN9YDtcbiAgICB9XG4gICAgY29uc3QgYXBwSWQgPSAoMCwgdXRpbHNfMS5lbnN1cmVBcHBJZEZvcm1hdCkoaWQpO1xuICAgIGNvbnN0IHN1YmRvbWFpbiA9IG9wdGlvbnMuc3ViZG9tYWluID8gYCR7b3B0aW9ucy5zdWJkb21haW59LmAgOiBcIlwiO1xuICAgIGNvbnN0IHVybCA9IGBodHRwczovLyR7c3ViZG9tYWlufWZhbC5ydW4vJHthcHBJZH0vJHtwYXRofWA7XG4gICAgcmV0dXJuIGAke3VybC5yZXBsYWNlKC9cXC8kLywgXCJcIil9JHtxdWVyeVBhcmFtc31gO1xufVxuZnVuY3Rpb24gc2VuZChpZF8xKSB7XG4gICAgcmV0dXJuIF9fYXdhaXRlcih0aGlzLCBhcmd1bWVudHMsIHZvaWQgMCwgZnVuY3Rpb24qIChpZCwgb3B0aW9ucyA9IHt9KSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgY29uc3QgaW5wdXQgPSBvcHRpb25zLmlucHV0ICYmIG9wdGlvbnMuYXV0b1VwbG9hZCAhPT0gZmFsc2VcbiAgICAgICAgICAgID8geWllbGQgc3RvcmFnZV8xLnN0b3JhZ2VJbXBsLnRyYW5zZm9ybUlucHV0KG9wdGlvbnMuaW5wdXQpXG4gICAgICAgICAgICA6IG9wdGlvbnMuaW5wdXQ7XG4gICAgICAgIHJldHVybiAoMCwgcmVxdWVzdF8xLmRpc3BhdGNoUmVxdWVzdCkoKF9hID0gb3B0aW9ucy5tZXRob2QpICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IFwicG9zdFwiLCBidWlsZFVybChpZCwgb3B0aW9ucyksIGlucHV0KTtcbiAgICB9KTtcbn1cbi8qKlxuICogUnVucyBhIGZhbCBzZXJ2ZXJsZXNzIGZ1bmN0aW9uIGlkZW50aWZpZWQgYnkgaXRzIGBpZGAuXG4gKlxuICogQHBhcmFtIGlkIHRoZSByZWdpc3RlcmVkIGZ1bmN0aW9uIHJldmlzaW9uIGlkIG9yIGFsaWFzLlxuICogQHJldHVybnMgdGhlIHJlbW90ZSBmdW5jdGlvbiBvdXRwdXRcbiAqL1xuZnVuY3Rpb24gcnVuKGlkXzEpIHtcbiAgICByZXR1cm4gX19hd2FpdGVyKHRoaXMsIGFyZ3VtZW50cywgdm9pZCAwLCBmdW5jdGlvbiogKGlkLCBvcHRpb25zID0ge30pIHtcbiAgICAgICAgcmV0dXJuIHNlbmQoaWQsIG9wdGlvbnMpO1xuICAgIH0pO1xufVxuY29uc3QgREVGQVVMVF9QT0xMX0lOVEVSVkFMID0gNTAwO1xuLyoqXG4gKiBUaGUgZmFsIHJ1biBxdWV1ZSBtb2R1bGUuIEl0IGFsbG93cyB0byBzdWJtaXQgYSBmdW5jdGlvbiB0byB0aGUgcXVldWUgYW5kIGdldCBpdHMgcmVzdWx0XG4gKiBvbiBhIHNlcGFyYXRlIGNhbGwuIFRoaXMgaXMgdXNlZnVsIGZvciBsb25nIHJ1bm5pbmcgZnVuY3Rpb25zIHRoYXQgY2FuIGJlIGV4ZWN1dGVkXG4gKiBhc3luY2hyb25vdXNseSBhbmQgbm90IC5cbiAqL1xuZXhwb3J0cy5xdWV1ZSA9IHtcbiAgICBzdWJtaXQoZW5kcG9pbnRJZCwgb3B0aW9ucykge1xuICAgICAgICByZXR1cm4gX19hd2FpdGVyKHRoaXMsIHZvaWQgMCwgdm9pZCAwLCBmdW5jdGlvbiogKCkge1xuICAgICAgICAgICAgY29uc3QgeyB3ZWJob29rVXJsLCBwYXRoID0gXCJcIiB9ID0gb3B0aW9ucywgcnVuT3B0aW9ucyA9IF9fcmVzdChvcHRpb25zLCBbXCJ3ZWJob29rVXJsXCIsIFwicGF0aFwiXSk7XG4gICAgICAgICAgICByZXR1cm4gc2VuZChlbmRwb2ludElkLCBPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIHJ1bk9wdGlvbnMpLCB7IHN1YmRvbWFpbjogXCJxdWV1ZVwiLCBtZXRob2Q6IFwicG9zdFwiLCBwYXRoOiBwYXRoLCBxdWVyeTogd2ViaG9va1VybCA/IHsgZmFsX3dlYmhvb2s6IHdlYmhvb2tVcmwgfSA6IHVuZGVmaW5lZCB9KSk7XG4gICAgICAgIH0pO1xuICAgIH0sXG4gICAgc3RhdHVzKGVuZHBvaW50SWRfMSwgX2EpIHtcbiAgICAgICAgcmV0dXJuIF9fYXdhaXRlcih0aGlzLCBhcmd1bWVudHMsIHZvaWQgMCwgZnVuY3Rpb24qIChlbmRwb2ludElkLCB7IHJlcXVlc3RJZCwgbG9ncyA9IGZhbHNlIH0pIHtcbiAgICAgICAgICAgIGNvbnN0IGFwcElkID0gKDAsIHV0aWxzXzEucGFyc2VBcHBJZCkoZW5kcG9pbnRJZCk7XG4gICAgICAgICAgICBjb25zdCBwcmVmaXggPSBhcHBJZC5uYW1lc3BhY2UgPyBgJHthcHBJZC5uYW1lc3BhY2V9L2AgOiBcIlwiO1xuICAgICAgICAgICAgcmV0dXJuIHNlbmQoYCR7cHJlZml4fSR7YXBwSWQub3duZXJ9LyR7YXBwSWQuYWxpYXN9YCwge1xuICAgICAgICAgICAgICAgIHN1YmRvbWFpbjogXCJxdWV1ZVwiLFxuICAgICAgICAgICAgICAgIG1ldGhvZDogXCJnZXRcIixcbiAgICAgICAgICAgICAgICBwYXRoOiBgL3JlcXVlc3RzLyR7cmVxdWVzdElkfS9zdGF0dXNgLFxuICAgICAgICAgICAgICAgIGlucHV0OiB7XG4gICAgICAgICAgICAgICAgICAgIGxvZ3M6IGxvZ3MgPyBcIjFcIiA6IFwiMFwiLFxuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSk7XG4gICAgfSxcbiAgICBzdHJlYW1TdGF0dXMoZW5kcG9pbnRJZF8xLCBfYSkge1xuICAgICAgICByZXR1cm4gX19hd2FpdGVyKHRoaXMsIGFyZ3VtZW50cywgdm9pZCAwLCBmdW5jdGlvbiogKGVuZHBvaW50SWQsIHsgcmVxdWVzdElkLCBsb2dzID0gZmFsc2UsIGNvbm5lY3Rpb25Nb2RlIH0pIHtcbiAgICAgICAgICAgIGNvbnN0IGFwcElkID0gKDAsIHV0aWxzXzEucGFyc2VBcHBJZCkoZW5kcG9pbnRJZCk7XG4gICAgICAgICAgICBjb25zdCBwcmVmaXggPSBhcHBJZC5uYW1lc3BhY2UgPyBgJHthcHBJZC5uYW1lc3BhY2V9L2AgOiBcIlwiO1xuICAgICAgICAgICAgY29uc3QgcXVlcnlQYXJhbXMgPSB7XG4gICAgICAgICAgICAgICAgbG9nczogbG9ncyA/IFwiMVwiIDogXCIwXCIsXG4gICAgICAgICAgICB9O1xuICAgICAgICAgICAgY29uc3QgdXJsID0gYnVpbGRVcmwoYCR7cHJlZml4fSR7YXBwSWQub3duZXJ9LyR7YXBwSWQuYWxpYXN9YCwge1xuICAgICAgICAgICAgICAgIHN1YmRvbWFpbjogXCJxdWV1ZVwiLFxuICAgICAgICAgICAgICAgIHBhdGg6IGAvcmVxdWVzdHMvJHtyZXF1ZXN0SWR9L3N0YXR1cy9zdHJlYW1gLFxuICAgICAgICAgICAgICAgIHF1ZXJ5OiBxdWVyeVBhcmFtcyxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgcmV0dXJuIG5ldyBzdHJlYW1pbmdfMS5GYWxTdHJlYW0oZW5kcG9pbnRJZCwge1xuICAgICAgICAgICAgICAgIHVybCxcbiAgICAgICAgICAgICAgICBtZXRob2Q6IFwiZ2V0XCIsXG4gICAgICAgICAgICAgICAgY29ubmVjdGlvbk1vZGUsXG4gICAgICAgICAgICAgICAgcXVlcnlQYXJhbXMsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSk7XG4gICAgfSxcbiAgICBzdWJzY3JpYmVUb1N0YXR1cyhlbmRwb2ludElkLCBvcHRpb25zKSB7XG4gICAgICAgIHJldHVybiBfX2F3YWl0ZXIodGhpcywgdm9pZCAwLCB2b2lkIDAsIGZ1bmN0aW9uKiAoKSB7XG4gICAgICAgICAgICBjb25zdCByZXF1ZXN0SWQgPSBvcHRpb25zLnJlcXVlc3RJZDtcbiAgICAgICAgICAgIGNvbnN0IHRpbWVvdXQgPSBvcHRpb25zLnRpbWVvdXQ7XG4gICAgICAgICAgICBsZXQgdGltZW91dElkID0gdW5kZWZpbmVkO1xuICAgICAgICAgICAgY29uc3QgaGFuZGxlQ2FuY2VsRXJyb3IgPSAoKSA9PiB7XG4gICAgICAgICAgICAgICAgLy8gSWdub3JlIGVycm9ycyBhcyB0aGUgY2xpZW50IHdpbGwgZm9sbG93IHRocm91Z2ggd2l0aCB0aGUgdGltZW91dFxuICAgICAgICAgICAgICAgIC8vIHJlZ2FyZGxlc3Mgb2YgdGhlIHNlcnZlciByZXNwb25zZS4gSW4gY2FzZSBjYW5jZWxhdGlvbiBmYWlscywgd2VcbiAgICAgICAgICAgICAgICAvLyBzdGlsbCB3YW50IHRvIHJlamVjdCB0aGUgcHJvbWlzZSBhbmQgY29uc2lkZXIgdGhlIGNsaWVudCBjYWxsIGNhbmNlbGVkLlxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIGlmIChvcHRpb25zLm1vZGUgPT09IFwic3RyZWFtaW5nXCIpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBzdGF0dXMgPSB5aWVsZCBleHBvcnRzLnF1ZXVlLnN0cmVhbVN0YXR1cyhlbmRwb2ludElkLCB7XG4gICAgICAgICAgICAgICAgICAgIHJlcXVlc3RJZCxcbiAgICAgICAgICAgICAgICAgICAgbG9nczogb3B0aW9ucy5sb2dzLFxuICAgICAgICAgICAgICAgICAgICBjb25uZWN0aW9uTW9kZTogXCJjb25uZWN0aW9uTW9kZVwiIGluIG9wdGlvbnNcbiAgICAgICAgICAgICAgICAgICAgICAgID8gb3B0aW9ucy5jb25uZWN0aW9uTW9kZVxuICAgICAgICAgICAgICAgICAgICAgICAgOiB1bmRlZmluZWQsXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgY29uc3QgbG9ncyA9IFtdO1xuICAgICAgICAgICAgICAgIGlmICh0aW1lb3V0KSB7XG4gICAgICAgICAgICAgICAgICAgIHRpbWVvdXRJZCA9IHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgc3RhdHVzLmFib3J0KCk7XG4gICAgICAgICAgICAgICAgICAgICAgICBleHBvcnRzLnF1ZXVlLmNhbmNlbChlbmRwb2ludElkLCB7IHJlcXVlc3RJZCB9KS5jYXRjaChoYW5kbGVDYW5jZWxFcnJvcik7XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBUT0RPIHRoaXMgZXJyb3IgY2Fubm90IGJ1YmJsZSB1cCB0byB0aGUgdXNlciBzaW5jZSBpdCdzIHRocm93biBpblxuICAgICAgICAgICAgICAgICAgICAgICAgLy8gYSBjbG9zdXJlIGluIHRoZSBnbG9iYWwgc2NvcGUgZHVlIHRvIHNldFRpbWVvdXQgYmVoYXZpb3IuXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBVc2VyIHdpbGwgZ2V0IGEgcGxhdGZvcm0gZXJyb3IgaW5zdGVhZC4gV2Ugc2hvdWxkIGZpbmQgYSB3YXkgdG9cbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIG1ha2UgdGhpcyBiZWhhdmlvciBhbGlnbmVkIHdpdGggcG9sbGluZy5cbiAgICAgICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgQ2xpZW50IHRpbWVkIG91dCB3YWl0aW5nIGZvciB0aGUgcmVxdWVzdCB0byBjb21wbGV0ZSBhZnRlciAke3RpbWVvdXR9bXNgKTtcbiAgICAgICAgICAgICAgICAgICAgfSwgdGltZW91dCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHN0YXR1cy5vbihcImRhdGFcIiwgKGRhdGEpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKG9wdGlvbnMub25RdWV1ZVVwZGF0ZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gYWNjdW11bGF0ZSBsb2dzIHRvIG1hdGNoIHByZXZpb3VzIHBvbGxpbmcgYmVoYXZpb3JcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChcImxvZ3NcIiBpbiBkYXRhICYmXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgQXJyYXkuaXNBcnJheShkYXRhLmxvZ3MpICYmXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YS5sb2dzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBsb2dzLnB1c2goLi4uZGF0YS5sb2dzKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbnMub25RdWV1ZVVwZGF0ZShcImxvZ3NcIiBpbiBkYXRhID8gT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKHt9LCBkYXRhKSwgeyBsb2dzIH0pIDogZGF0YSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICBjb25zdCBkb25lU3RhdHVzID0geWllbGQgc3RhdHVzLmRvbmUoKTtcbiAgICAgICAgICAgICAgICBpZiAodGltZW91dElkKSB7XG4gICAgICAgICAgICAgICAgICAgIGNsZWFyVGltZW91dCh0aW1lb3V0SWQpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICByZXR1cm4gZG9uZVN0YXR1cztcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIGRlZmF1bHQgdG8gcG9sbGluZyB1bnRpbCBzdGF0dXMgc3RyZWFtaW5nIGlzIHN0YWJsZSBhbmQgZmFzdGVyXG4gICAgICAgICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICAgICAgICAgIHZhciBfYTtcbiAgICAgICAgICAgICAgICBsZXQgcG9sbGluZ1RpbWVvdXRJZDtcbiAgICAgICAgICAgICAgICAvLyB0eXBlIHJlc29sdXRpb24gaXNuJ3QgZ3JlYXQgaW4gdGhpcyBjYXNlLCBzbyBjaGVjayBmb3IgaXRzIHByZXNlbmNlXG4gICAgICAgICAgICAgICAgLy8gYW5kIGFuZCB0eXBlIHNvIHRoZSB0eXBlY2hlY2tlciBiZWhhdmVzIGFzIGV4cGVjdGVkXG4gICAgICAgICAgICAgICAgY29uc3QgcG9sbEludGVydmFsID0gXCJwb2xsSW50ZXJ2YWxcIiBpbiBvcHRpb25zICYmIHR5cGVvZiBvcHRpb25zLnBvbGxJbnRlcnZhbCA9PT0gXCJudW1iZXJcIlxuICAgICAgICAgICAgICAgICAgICA/ICgoX2EgPSBvcHRpb25zLnBvbGxJbnRlcnZhbCkgIT09IG51bGwgJiYgX2EgIT09IHZvaWQgMCA/IF9hIDogREVGQVVMVF9QT0xMX0lOVEVSVkFMKVxuICAgICAgICAgICAgICAgICAgICA6IERFRkFVTFRfUE9MTF9JTlRFUlZBTDtcbiAgICAgICAgICAgICAgICBjb25zdCBjbGVhclNjaGVkdWxlZFRhc2tzID0gKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBpZiAodGltZW91dElkKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGVhclRpbWVvdXQodGltZW91dElkKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBpZiAocG9sbGluZ1RpbWVvdXRJZCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgY2xlYXJUaW1lb3V0KHBvbGxpbmdUaW1lb3V0SWQpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICBpZiAodGltZW91dCkge1xuICAgICAgICAgICAgICAgICAgICB0aW1lb3V0SWQgPSBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsZWFyU2NoZWR1bGVkVGFza3MoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGV4cG9ydHMucXVldWUuY2FuY2VsKGVuZHBvaW50SWQsIHsgcmVxdWVzdElkIH0pLmNhdGNoKGhhbmRsZUNhbmNlbEVycm9yKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJlamVjdChuZXcgRXJyb3IoYENsaWVudCB0aW1lZCBvdXQgd2FpdGluZyBmb3IgdGhlIHJlcXVlc3QgdG8gY29tcGxldGUgYWZ0ZXIgJHt0aW1lb3V0fW1zYCkpO1xuICAgICAgICAgICAgICAgICAgICB9LCB0aW1lb3V0KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY29uc3QgcG9sbCA9ICgpID0+IF9fYXdhaXRlcih0aGlzLCB2b2lkIDAsIHZvaWQgMCwgZnVuY3Rpb24qICgpIHtcbiAgICAgICAgICAgICAgICAgICAgdmFyIF9hO1xuICAgICAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgcmVxdWVzdFN0YXR1cyA9IHlpZWxkIGV4cG9ydHMucXVldWUuc3RhdHVzKGVuZHBvaW50SWQsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXF1ZXN0SWQsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbG9nczogKF9hID0gb3B0aW9ucy5sb2dzKSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiBmYWxzZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKG9wdGlvbnMub25RdWV1ZVVwZGF0ZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9wdGlvbnMub25RdWV1ZVVwZGF0ZShyZXF1ZXN0U3RhdHVzKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChyZXF1ZXN0U3RhdHVzLnN0YXR1cyA9PT0gXCJDT01QTEVURURcIikge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsZWFyU2NoZWR1bGVkVGFza3MoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXNvbHZlKHJlcXVlc3RTdGF0dXMpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIHBvbGxpbmdUaW1lb3V0SWQgPSBzZXRUaW1lb3V0KHBvbGwsIHBvbGxJbnRlcnZhbCk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGVhclNjaGVkdWxlZFRhc2tzKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICByZWplY3QoZXJyb3IpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgcG9sbCgpLmNhdGNoKHJlamVjdCk7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSk7XG4gICAgfSxcbiAgICByZXN1bHQoZW5kcG9pbnRJZF8xLCBfYSkge1xuICAgICAgICByZXR1cm4gX19hd2FpdGVyKHRoaXMsIGFyZ3VtZW50cywgdm9pZCAwLCBmdW5jdGlvbiogKGVuZHBvaW50SWQsIHsgcmVxdWVzdElkIH0pIHtcbiAgICAgICAgICAgIGNvbnN0IGFwcElkID0gKDAsIHV0aWxzXzEucGFyc2VBcHBJZCkoZW5kcG9pbnRJZCk7XG4gICAgICAgICAgICBjb25zdCBwcmVmaXggPSBhcHBJZC5uYW1lc3BhY2UgPyBgJHthcHBJZC5uYW1lc3BhY2V9L2AgOiBcIlwiO1xuICAgICAgICAgICAgcmV0dXJuIHNlbmQoYCR7cHJlZml4fSR7YXBwSWQub3duZXJ9LyR7YXBwSWQuYWxpYXN9YCwge1xuICAgICAgICAgICAgICAgIHN1YmRvbWFpbjogXCJxdWV1ZVwiLFxuICAgICAgICAgICAgICAgIG1ldGhvZDogXCJnZXRcIixcbiAgICAgICAgICAgICAgICBwYXRoOiBgL3JlcXVlc3RzLyR7cmVxdWVzdElkfWAsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSk7XG4gICAgfSxcbiAgICBjYW5jZWwoZW5kcG9pbnRJZF8xLCBfYSkge1xuICAgICAgICByZXR1cm4gX19hd2FpdGVyKHRoaXMsIGFyZ3VtZW50cywgdm9pZCAwLCBmdW5jdGlvbiogKGVuZHBvaW50SWQsIHsgcmVxdWVzdElkIH0pIHtcbiAgICAgICAgICAgIGNvbnN0IGFwcElkID0gKDAsIHV0aWxzXzEucGFyc2VBcHBJZCkoZW5kcG9pbnRJZCk7XG4gICAgICAgICAgICBjb25zdCBwcmVmaXggPSBhcHBJZC5uYW1lc3BhY2UgPyBgJHthcHBJZC5uYW1lc3BhY2V9L2AgOiBcIlwiO1xuICAgICAgICAgICAgeWllbGQgc2VuZChgJHtwcmVmaXh9JHthcHBJZC5vd25lcn0vJHthcHBJZC5hbGlhc31gLCB7XG4gICAgICAgICAgICAgICAgc3ViZG9tYWluOiBcInF1ZXVlXCIsXG4gICAgICAgICAgICAgICAgbWV0aG9kOiBcInB1dFwiLFxuICAgICAgICAgICAgICAgIHBhdGg6IGAvcmVxdWVzdHMvJHtyZXF1ZXN0SWR9L2NhbmNlbGAsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSk7XG4gICAgfSxcbn07XG4vKipcbiAqIFN1YnNjcmliZXMgdG8gdXBkYXRlcyBmb3IgYSBzcGVjaWZpYyByZXF1ZXN0IGluIHRoZSBxdWV1ZS5cbiAqXG4gKiBAcGFyYW0gZW5kcG9pbnRJZCAtIFRoZSBJRCBvZiB0aGUgZnVuY3Rpb24gd2ViIGVuZHBvaW50LlxuICogQHBhcmFtIG9wdGlvbnMgLSBPcHRpb25zIHRvIGNvbmZpZ3VyZSBob3cgdGhlIHJlcXVlc3QgaXMgcnVuIGFuZCBob3cgdXBkYXRlcyBhcmUgcmVjZWl2ZWQuXG4gKiBAcmV0dXJucyBBIHByb21pc2UgdGhhdCByZXNvbHZlcyB0byB0aGUgcmVzdWx0IG9mIHRoZSByZXF1ZXN0IG9uY2UgaXQncyBjb21wbGV0ZWQuXG4gKi9cbmZ1bmN0aW9uIHN1YnNjcmliZShlbmRwb2ludElkXzEpIHtcbiAgICByZXR1cm4gX19hd2FpdGVyKHRoaXMsIGFyZ3VtZW50cywgdm9pZCAwLCBmdW5jdGlvbiogKGVuZHBvaW50SWQsIG9wdGlvbnMgPSB7fSkge1xuICAgICAgICBjb25zdCB7IHJlcXVlc3RfaWQ6IHJlcXVlc3RJZCB9ID0geWllbGQgZXhwb3J0cy5xdWV1ZS5zdWJtaXQoZW5kcG9pbnRJZCwgb3B0aW9ucyk7XG4gICAgICAgIGlmIChvcHRpb25zLm9uRW5xdWV1ZSkge1xuICAgICAgICAgICAgb3B0aW9ucy5vbkVucXVldWUocmVxdWVzdElkKTtcbiAgICAgICAgfVxuICAgICAgICB5aWVsZCBleHBvcnRzLnF1ZXVlLnN1YnNjcmliZVRvU3RhdHVzKGVuZHBvaW50SWQsIE9iamVjdC5hc3NpZ24oeyByZXF1ZXN0SWQgfSwgb3B0aW9ucykpO1xuICAgICAgICByZXR1cm4gZXhwb3J0cy5xdWV1ZS5yZXN1bHQoZW5kcG9pbnRJZCwgeyByZXF1ZXN0SWQgfSk7XG4gICAgfSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1mdW5jdGlvbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@fal-ai/serverless-client/src/function.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@fal-ai/serverless-client/src/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/@fal-ai/serverless-client/src/index.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.parseAppId = exports.stream = exports.storage = exports.ValidationError = exports.ApiError = exports.realtime = exports.withProxy = exports.withMiddleware = exports.subscribe = exports.run = exports.queue = exports.getConfig = exports.config = void 0;\nvar config_1 = __webpack_require__(/*! ./config */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/config.js\");\nObject.defineProperty(exports, \"config\", ({ enumerable: true, get: function () { return config_1.config; } }));\nObject.defineProperty(exports, \"getConfig\", ({ enumerable: true, get: function () { return config_1.getConfig; } }));\nvar function_1 = __webpack_require__(/*! ./function */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/function.js\");\nObject.defineProperty(exports, \"queue\", ({ enumerable: true, get: function () { return function_1.queue; } }));\nObject.defineProperty(exports, \"run\", ({ enumerable: true, get: function () { return function_1.run; } }));\nObject.defineProperty(exports, \"subscribe\", ({ enumerable: true, get: function () { return function_1.subscribe; } }));\nvar middleware_1 = __webpack_require__(/*! ./middleware */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/middleware.js\");\nObject.defineProperty(exports, \"withMiddleware\", ({ enumerable: true, get: function () { return middleware_1.withMiddleware; } }));\nObject.defineProperty(exports, \"withProxy\", ({ enumerable: true, get: function () { return middleware_1.withProxy; } }));\nvar realtime_1 = __webpack_require__(/*! ./realtime */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/realtime.js\");\nObject.defineProperty(exports, \"realtime\", ({ enumerable: true, get: function () { return realtime_1.realtimeImpl; } }));\nvar response_1 = __webpack_require__(/*! ./response */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/response.js\");\nObject.defineProperty(exports, \"ApiError\", ({ enumerable: true, get: function () { return response_1.ApiError; } }));\nObject.defineProperty(exports, \"ValidationError\", ({ enumerable: true, get: function () { return response_1.ValidationError; } }));\nvar storage_1 = __webpack_require__(/*! ./storage */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/storage.js\");\nObject.defineProperty(exports, \"storage\", ({ enumerable: true, get: function () { return storage_1.storageImpl; } }));\nvar streaming_1 = __webpack_require__(/*! ./streaming */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/streaming.js\");\nObject.defineProperty(exports, \"stream\", ({ enumerable: true, get: function () { return streaming_1.stream; } }));\nvar utils_1 = __webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/utils.js\");\nObject.defineProperty(exports, \"parseAppId\", ({ enumerable: true, get: function () { return utils_1.parseAppId; } }));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@fal-ai/serverless-client/src/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@fal-ai/serverless-client/src/middleware.js":
/*!******************************************************************!*\
  !*** ./node_modules/@fal-ai/serverless-client/src/middleware.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.TARGET_URL_HEADER = void 0;\nexports.withMiddleware = withMiddleware;\nexports.withProxy = withProxy;\n/**\n * Setup a execution chain of middleware functions.\n *\n * @param middlewares one or more middleware functions.\n * @returns a middleware function that executes the given middlewares in order.\n */\nfunction withMiddleware(...middlewares) {\n    return (config) => middlewares.reduce((configPromise, middleware) => configPromise.then((req) => middleware(req)), Promise.resolve(config));\n}\nexports.TARGET_URL_HEADER = \"x-fal-target-url\";\nfunction withProxy(config) {\n    // when running on the server, we don't need to proxy the request\n    if (typeof window === \"undefined\") {\n        return (requestConfig) => Promise.resolve(requestConfig);\n    }\n    return (requestConfig) => Promise.resolve(Object.assign(Object.assign({}, requestConfig), { url: config.targetUrl, headers: Object.assign(Object.assign({}, (requestConfig.headers || {})), { [exports.TARGET_URL_HEADER]: requestConfig.url }) }));\n}\n//# sourceMappingURL=middleware.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@fal-ai/serverless-client/src/middleware.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@fal-ai/serverless-client/src/realtime.js":
/*!****************************************************************!*\
  !*** ./node_modules/@fal-ai/serverless-client/src/realtime.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.realtimeImpl = void 0;\n/* eslint-disable @typescript-eslint/no-explicit-any */\nconst msgpack_1 = __webpack_require__(/*! @msgpack/msgpack */ \"(rsc)/./node_modules/@msgpack/msgpack/dist.esm/index.mjs\");\nconst robot3_1 = __webpack_require__(/*! robot3 */ \"(rsc)/./node_modules/robot3/dist/machine.js\");\nconst auth_1 = __webpack_require__(/*! ./auth */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/auth.js\");\nconst response_1 = __webpack_require__(/*! ./response */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/response.js\");\nconst runtime_1 = __webpack_require__(/*! ./runtime */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/runtime.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/utils.js\");\nconst initialState = () => ({\n    enqueuedMessage: undefined,\n});\nfunction hasToken(context) {\n    return context.token !== undefined;\n}\nfunction noToken(context) {\n    return !hasToken(context);\n}\nfunction enqueueMessage(context, event) {\n    return Object.assign(Object.assign({}, context), { enqueuedMessage: event.message });\n}\nfunction closeConnection(context) {\n    if (context.websocket && context.websocket.readyState === WebSocket.OPEN) {\n        context.websocket.close();\n    }\n    return Object.assign(Object.assign({}, context), { websocket: undefined });\n}\nfunction sendMessage(context, event) {\n    if (context.websocket && context.websocket.readyState === WebSocket.OPEN) {\n        if (event.message instanceof Uint8Array) {\n            context.websocket.send(event.message);\n        }\n        else {\n            context.websocket.send((0, msgpack_1.encode)(event.message));\n        }\n        return Object.assign(Object.assign({}, context), { enqueuedMessage: undefined });\n    }\n    return Object.assign(Object.assign({}, context), { enqueuedMessage: event.message });\n}\nfunction expireToken(context) {\n    return Object.assign(Object.assign({}, context), { token: undefined });\n}\nfunction setToken(context, event) {\n    return Object.assign(Object.assign({}, context), { token: event.token });\n}\nfunction connectionEstablished(context, event) {\n    return Object.assign(Object.assign({}, context), { websocket: event.websocket });\n}\n// State machine\nconst connectionStateMachine = (0, robot3_1.createMachine)(\"idle\", {\n    idle: (0, robot3_1.state)((0, robot3_1.transition)(\"send\", \"connecting\", (0, robot3_1.reduce)(enqueueMessage)), (0, robot3_1.transition)(\"expireToken\", \"idle\", (0, robot3_1.reduce)(expireToken)), (0, robot3_1.transition)(\"close\", \"idle\", (0, robot3_1.reduce)(closeConnection))),\n    connecting: (0, robot3_1.state)((0, robot3_1.transition)(\"connecting\", \"connecting\"), (0, robot3_1.transition)(\"connected\", \"active\", (0, robot3_1.reduce)(connectionEstablished)), (0, robot3_1.transition)(\"connectionClosed\", \"idle\", (0, robot3_1.reduce)(closeConnection)), (0, robot3_1.transition)(\"send\", \"connecting\", (0, robot3_1.reduce)(enqueueMessage)), (0, robot3_1.transition)(\"close\", \"idle\", (0, robot3_1.reduce)(closeConnection)), (0, robot3_1.immediate)(\"authRequired\", (0, robot3_1.guard)(noToken))),\n    authRequired: (0, robot3_1.state)((0, robot3_1.transition)(\"initiateAuth\", \"authInProgress\"), (0, robot3_1.transition)(\"send\", \"authRequired\", (0, robot3_1.reduce)(enqueueMessage)), (0, robot3_1.transition)(\"close\", \"idle\", (0, robot3_1.reduce)(closeConnection))),\n    authInProgress: (0, robot3_1.state)((0, robot3_1.transition)(\"authenticated\", \"connecting\", (0, robot3_1.reduce)(setToken)), (0, robot3_1.transition)(\"unauthorized\", \"idle\", (0, robot3_1.reduce)(expireToken), (0, robot3_1.reduce)(closeConnection)), (0, robot3_1.transition)(\"send\", \"authInProgress\", (0, robot3_1.reduce)(enqueueMessage)), (0, robot3_1.transition)(\"close\", \"idle\", (0, robot3_1.reduce)(closeConnection))),\n    active: (0, robot3_1.state)((0, robot3_1.transition)(\"send\", \"active\", (0, robot3_1.reduce)(sendMessage)), (0, robot3_1.transition)(\"unauthorized\", \"idle\", (0, robot3_1.reduce)(expireToken)), (0, robot3_1.transition)(\"connectionClosed\", \"idle\", (0, robot3_1.reduce)(closeConnection)), (0, robot3_1.transition)(\"close\", \"idle\", (0, robot3_1.reduce)(closeConnection))),\n    failed: (0, robot3_1.state)((0, robot3_1.transition)(\"send\", \"failed\"), (0, robot3_1.transition)(\"close\", \"idle\", (0, robot3_1.reduce)(closeConnection))),\n}, initialState);\nfunction buildRealtimeUrl(app, { token, maxBuffering }) {\n    if (maxBuffering !== undefined && (maxBuffering < 1 || maxBuffering > 60)) {\n        throw new Error(\"The `maxBuffering` must be between 1 and 60 (inclusive)\");\n    }\n    const queryParams = new URLSearchParams({\n        fal_jwt_token: token,\n    });\n    if (maxBuffering !== undefined) {\n        queryParams.set(\"max_buffering\", maxBuffering.toFixed(0));\n    }\n    const appId = (0, utils_1.ensureAppIdFormat)(app);\n    return `wss://fal.run/${appId}/realtime?${queryParams.toString()}`;\n}\nconst DEFAULT_THROTTLE_INTERVAL = 128;\nfunction isUnauthorizedError(message) {\n    // TODO we need better protocol definition with error codes\n    return message[\"status\"] === \"error\" && message[\"error\"] === \"Unauthorized\";\n}\n/**\n * See https://www.rfc-editor.org/rfc/rfc6455.html#section-7.4.1\n */\nconst WebSocketErrorCodes = {\n    NORMAL_CLOSURE: 1000,\n    GOING_AWAY: 1001,\n};\nconst connectionCache = new Map();\nconst connectionCallbacks = new Map();\nfunction reuseInterpreter(key, throttleInterval, onChange) {\n    if (!connectionCache.has(key)) {\n        const machine = (0, robot3_1.interpret)(connectionStateMachine, onChange);\n        connectionCache.set(key, Object.assign(Object.assign({}, machine), { throttledSend: throttleInterval > 0\n                ? (0, utils_1.throttle)(machine.send, throttleInterval, true)\n                : machine.send }));\n    }\n    return connectionCache.get(key);\n}\nconst noop = () => {\n    /* No-op */\n};\n/**\n * A no-op connection that does not send any message.\n * Useful on the frameworks that reuse code for both ssr and csr (e.g. Next)\n * so the call when doing ssr has no side-effects.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst NoOpConnection = {\n    send: noop,\n    close: noop,\n};\nfunction isSuccessfulResult(data) {\n    return (data.status !== \"error\" &&\n        data.type !== \"x-fal-message\" &&\n        !isFalErrorResult(data));\n}\nfunction isFalErrorResult(data) {\n    return data.type === \"x-fal-error\";\n}\n/**\n * The default implementation of the realtime client.\n */\nexports.realtimeImpl = {\n    connect(app, handler) {\n        const { \n        // if running on React in the server, set clientOnly to true by default\n        clientOnly = (0, utils_1.isReact)() && !(0, runtime_1.isBrowser)(), connectionKey = crypto.randomUUID(), maxBuffering, throttleInterval = DEFAULT_THROTTLE_INTERVAL, } = handler;\n        if (clientOnly && !(0, runtime_1.isBrowser)()) {\n            return NoOpConnection;\n        }\n        let previousState;\n        // Although the state machine is cached so we don't open multiple connections,\n        // we still need to update the callbacks so we can call the correct references\n        // when the state machine is reused. This is needed because the callbacks\n        // are passed as part of the handler object, which can be different across\n        // different calls to `connect`.\n        connectionCallbacks.set(connectionKey, {\n            onError: handler.onError,\n            onResult: handler.onResult,\n        });\n        const getCallbacks = () => connectionCallbacks.get(connectionKey);\n        const stateMachine = reuseInterpreter(connectionKey, throttleInterval, ({ context, machine, send }) => {\n            const { enqueuedMessage, token } = context;\n            if (machine.current === \"active\" && enqueuedMessage) {\n                send({ type: \"send\", message: enqueuedMessage });\n            }\n            if (machine.current === \"authRequired\" &&\n                token === undefined &&\n                previousState !== machine.current) {\n                send({ type: \"initiateAuth\" });\n                (0, auth_1.getTemporaryAuthToken)(app)\n                    .then((token) => {\n                    send({ type: \"authenticated\", token });\n                    const tokenExpirationTimeout = Math.round(auth_1.TOKEN_EXPIRATION_SECONDS * 0.9 * 1000);\n                    setTimeout(() => {\n                        send({ type: \"expireToken\" });\n                    }, tokenExpirationTimeout);\n                })\n                    .catch((error) => {\n                    send({ type: \"unauthorized\", error });\n                });\n            }\n            if (machine.current === \"connecting\" &&\n                previousState !== machine.current &&\n                token !== undefined) {\n                const ws = new WebSocket(buildRealtimeUrl(app, { token, maxBuffering }));\n                ws.onopen = () => {\n                    send({ type: \"connected\", websocket: ws });\n                };\n                ws.onclose = (event) => {\n                    if (event.code !== WebSocketErrorCodes.NORMAL_CLOSURE) {\n                        const { onError = noop } = getCallbacks();\n                        onError(new response_1.ApiError({\n                            message: `Error closing the connection: ${event.reason}`,\n                            status: event.code,\n                        }));\n                    }\n                    send({ type: \"connectionClosed\", code: event.code });\n                };\n                ws.onerror = (event) => {\n                    // TODO specify error protocol for identified errors\n                    const { onError = noop } = getCallbacks();\n                    onError(new response_1.ApiError({ message: \"Unknown error\", status: 500 }));\n                };\n                ws.onmessage = (event) => {\n                    const { onResult } = getCallbacks();\n                    // Handle binary messages as msgpack messages\n                    if (event.data instanceof ArrayBuffer) {\n                        const result = (0, msgpack_1.decode)(new Uint8Array(event.data));\n                        onResult(result);\n                        return;\n                    }\n                    if (event.data instanceof Uint8Array) {\n                        const result = (0, msgpack_1.decode)(event.data);\n                        onResult(result);\n                        return;\n                    }\n                    if (event.data instanceof Blob) {\n                        event.data.arrayBuffer().then((buffer) => {\n                            const result = (0, msgpack_1.decode)(new Uint8Array(buffer));\n                            onResult(result);\n                        });\n                        return;\n                    }\n                    // Otherwise handle strings as plain JSON messages\n                    const data = JSON.parse(event.data);\n                    // Drop messages that are not related to the actual result.\n                    // In the future, we might want to handle other types of messages.\n                    // TODO: specify the fal ws protocol format\n                    if (isUnauthorizedError(data)) {\n                        send({ type: \"unauthorized\", error: new Error(\"Unauthorized\") });\n                        return;\n                    }\n                    if (isSuccessfulResult(data)) {\n                        onResult(data);\n                        return;\n                    }\n                    if (isFalErrorResult(data)) {\n                        if (data.error === \"TIMEOUT\") {\n                            // Timeout error messages just indicate that the connection hasn't\n                            // received an incoming message for a while. We don't need to\n                            // handle them as errors.\n                            return;\n                        }\n                        const { onError = noop } = getCallbacks();\n                        onError(new response_1.ApiError({\n                            message: `${data.error}: ${data.reason}`,\n                            // TODO better error status code\n                            status: 400,\n                            body: data,\n                        }));\n                        return;\n                    }\n                };\n            }\n            previousState = machine.current;\n        });\n        const send = (input) => {\n            // Use throttled send to avoid sending too many messages\n            var _a;\n            const message = input instanceof Uint8Array\n                ? input\n                : Object.assign(Object.assign({}, input), { request_id: (_a = input[\"request_id\"]) !== null && _a !== void 0 ? _a : crypto.randomUUID() });\n            stateMachine.throttledSend({\n                type: \"send\",\n                message,\n            });\n        };\n        const close = () => {\n            stateMachine.send({ type: \"close\" });\n        };\n        return {\n            send,\n            close,\n        };\n    },\n};\n//# sourceMappingURL=realtime.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGZhbC1haS9zZXJ2ZXJsZXNzLWNsaWVudC9zcmMvcmVhbHRpbWUuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsb0JBQW9CO0FBQ3BCO0FBQ0Esa0JBQWtCLG1CQUFPLENBQUMsa0ZBQWtCO0FBQzVDLGlCQUFpQixtQkFBTyxDQUFDLDJEQUFRO0FBQ2pDLGVBQWUsbUJBQU8sQ0FBQywwRUFBUTtBQUMvQixtQkFBbUIsbUJBQU8sQ0FBQyxrRkFBWTtBQUN2QyxrQkFBa0IsbUJBQU8sQ0FBQyxnRkFBVztBQUNyQyxnQkFBZ0IsbUJBQU8sQ0FBQyw0RUFBUztBQUNqQztBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUNBQXlDLGNBQWMsZ0NBQWdDO0FBQ3ZGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5Q0FBeUMsY0FBYyxzQkFBc0I7QUFDN0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkNBQTZDLGNBQWMsNEJBQTRCO0FBQ3ZGO0FBQ0EseUNBQXlDLGNBQWMsZ0NBQWdDO0FBQ3ZGO0FBQ0E7QUFDQSx5Q0FBeUMsY0FBYyxrQkFBa0I7QUFDekU7QUFDQTtBQUNBLHlDQUF5QyxjQUFjLG9CQUFvQjtBQUMzRTtBQUNBO0FBQ0EseUNBQXlDLGNBQWMsNEJBQTRCO0FBQ25GO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRCxpQ0FBaUMscUJBQXFCO0FBQ3REO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEIsTUFBTSxZQUFZLHVCQUF1QjtBQUNyRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrREFBK0QsY0FBYztBQUM3RTtBQUNBLGdDQUFnQztBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0I7QUFDcEI7QUFDQTtBQUNBO0FBQ0EsK0tBQStLO0FBQy9LO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLGtGQUFrRix3QkFBd0I7QUFDMUcsb0JBQW9CLHlCQUF5QjtBQUM3QztBQUNBLHVCQUF1Qix3Q0FBd0M7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsc0JBQXNCO0FBQzdDO0FBQ0E7QUFDQSwyQkFBMkIsOEJBQThCO0FBQ3pEO0FBQ0E7QUFDQSwrQkFBK0IscUJBQXFCO0FBQ3BELHFCQUFxQjtBQUNyQixpQkFBaUI7QUFDakI7QUFDQSwyQkFBMkIsNkJBQTZCO0FBQ3hELGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlFQUFpRSxxQkFBcUI7QUFDdEY7QUFDQSwyQkFBMkIsa0NBQWtDO0FBQzdEO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyxpQkFBaUI7QUFDakQ7QUFDQSxzRUFBc0UsYUFBYTtBQUNuRjtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBLDJCQUEyQiw0Q0FBNEM7QUFDdkU7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLGlCQUFpQjtBQUM3QyxzREFBc0QsdUNBQXVDO0FBQzdGO0FBQ0E7QUFDQSw0QkFBNEIsV0FBVztBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtCQUErQix3REFBd0Q7QUFDdkY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0MsaUJBQWlCO0FBQ2pEO0FBQ0Esd0NBQXdDLFdBQVcsSUFBSSxZQUFZO0FBQ25FO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnREFBZ0QsWUFBWSw2RkFBNkY7QUFDeko7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxnQ0FBZ0MsZUFBZTtBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9saXVwZW5nL2N1cnNvci/ov5vpmLYvRmx1eOWvueivneW8j+W6lOeUqC9mbHV4LWNoYXQtYXBwL25vZGVfbW9kdWxlcy9AZmFsLWFpL3NlcnZlcmxlc3MtY2xpZW50L3NyYy9yZWFsdGltZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMucmVhbHRpbWVJbXBsID0gdm9pZCAwO1xuLyogZXNsaW50LWRpc2FibGUgQHR5cGVzY3JpcHQtZXNsaW50L25vLWV4cGxpY2l0LWFueSAqL1xuY29uc3QgbXNncGFja18xID0gcmVxdWlyZShcIkBtc2dwYWNrL21zZ3BhY2tcIik7XG5jb25zdCByb2JvdDNfMSA9IHJlcXVpcmUoXCJyb2JvdDNcIik7XG5jb25zdCBhdXRoXzEgPSByZXF1aXJlKFwiLi9hdXRoXCIpO1xuY29uc3QgcmVzcG9uc2VfMSA9IHJlcXVpcmUoXCIuL3Jlc3BvbnNlXCIpO1xuY29uc3QgcnVudGltZV8xID0gcmVxdWlyZShcIi4vcnVudGltZVwiKTtcbmNvbnN0IHV0aWxzXzEgPSByZXF1aXJlKFwiLi91dGlsc1wiKTtcbmNvbnN0IGluaXRpYWxTdGF0ZSA9ICgpID0+ICh7XG4gICAgZW5xdWV1ZWRNZXNzYWdlOiB1bmRlZmluZWQsXG59KTtcbmZ1bmN0aW9uIGhhc1Rva2VuKGNvbnRleHQpIHtcbiAgICByZXR1cm4gY29udGV4dC50b2tlbiAhPT0gdW5kZWZpbmVkO1xufVxuZnVuY3Rpb24gbm9Ub2tlbihjb250ZXh0KSB7XG4gICAgcmV0dXJuICFoYXNUb2tlbihjb250ZXh0KTtcbn1cbmZ1bmN0aW9uIGVucXVldWVNZXNzYWdlKGNvbnRleHQsIGV2ZW50KSB7XG4gICAgcmV0dXJuIE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgY29udGV4dCksIHsgZW5xdWV1ZWRNZXNzYWdlOiBldmVudC5tZXNzYWdlIH0pO1xufVxuZnVuY3Rpb24gY2xvc2VDb25uZWN0aW9uKGNvbnRleHQpIHtcbiAgICBpZiAoY29udGV4dC53ZWJzb2NrZXQgJiYgY29udGV4dC53ZWJzb2NrZXQucmVhZHlTdGF0ZSA9PT0gV2ViU29ja2V0Lk9QRU4pIHtcbiAgICAgICAgY29udGV4dC53ZWJzb2NrZXQuY2xvc2UoKTtcbiAgICB9XG4gICAgcmV0dXJuIE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgY29udGV4dCksIHsgd2Vic29ja2V0OiB1bmRlZmluZWQgfSk7XG59XG5mdW5jdGlvbiBzZW5kTWVzc2FnZShjb250ZXh0LCBldmVudCkge1xuICAgIGlmIChjb250ZXh0LndlYnNvY2tldCAmJiBjb250ZXh0LndlYnNvY2tldC5yZWFkeVN0YXRlID09PSBXZWJTb2NrZXQuT1BFTikge1xuICAgICAgICBpZiAoZXZlbnQubWVzc2FnZSBpbnN0YW5jZW9mIFVpbnQ4QXJyYXkpIHtcbiAgICAgICAgICAgIGNvbnRleHQud2Vic29ja2V0LnNlbmQoZXZlbnQubWVzc2FnZSk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBjb250ZXh0LndlYnNvY2tldC5zZW5kKCgwLCBtc2dwYWNrXzEuZW5jb2RlKShldmVudC5tZXNzYWdlKSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgY29udGV4dCksIHsgZW5xdWV1ZWRNZXNzYWdlOiB1bmRlZmluZWQgfSk7XG4gICAgfVxuICAgIHJldHVybiBPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIGNvbnRleHQpLCB7IGVucXVldWVkTWVzc2FnZTogZXZlbnQubWVzc2FnZSB9KTtcbn1cbmZ1bmN0aW9uIGV4cGlyZVRva2VuKGNvbnRleHQpIHtcbiAgICByZXR1cm4gT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKHt9LCBjb250ZXh0KSwgeyB0b2tlbjogdW5kZWZpbmVkIH0pO1xufVxuZnVuY3Rpb24gc2V0VG9rZW4oY29udGV4dCwgZXZlbnQpIHtcbiAgICByZXR1cm4gT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKHt9LCBjb250ZXh0KSwgeyB0b2tlbjogZXZlbnQudG9rZW4gfSk7XG59XG5mdW5jdGlvbiBjb25uZWN0aW9uRXN0YWJsaXNoZWQoY29udGV4dCwgZXZlbnQpIHtcbiAgICByZXR1cm4gT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKHt9LCBjb250ZXh0KSwgeyB3ZWJzb2NrZXQ6IGV2ZW50LndlYnNvY2tldCB9KTtcbn1cbi8vIFN0YXRlIG1hY2hpbmVcbmNvbnN0IGNvbm5lY3Rpb25TdGF0ZU1hY2hpbmUgPSAoMCwgcm9ib3QzXzEuY3JlYXRlTWFjaGluZSkoXCJpZGxlXCIsIHtcbiAgICBpZGxlOiAoMCwgcm9ib3QzXzEuc3RhdGUpKCgwLCByb2JvdDNfMS50cmFuc2l0aW9uKShcInNlbmRcIiwgXCJjb25uZWN0aW5nXCIsICgwLCByb2JvdDNfMS5yZWR1Y2UpKGVucXVldWVNZXNzYWdlKSksICgwLCByb2JvdDNfMS50cmFuc2l0aW9uKShcImV4cGlyZVRva2VuXCIsIFwiaWRsZVwiLCAoMCwgcm9ib3QzXzEucmVkdWNlKShleHBpcmVUb2tlbikpLCAoMCwgcm9ib3QzXzEudHJhbnNpdGlvbikoXCJjbG9zZVwiLCBcImlkbGVcIiwgKDAsIHJvYm90M18xLnJlZHVjZSkoY2xvc2VDb25uZWN0aW9uKSkpLFxuICAgIGNvbm5lY3Rpbmc6ICgwLCByb2JvdDNfMS5zdGF0ZSkoKDAsIHJvYm90M18xLnRyYW5zaXRpb24pKFwiY29ubmVjdGluZ1wiLCBcImNvbm5lY3RpbmdcIiksICgwLCByb2JvdDNfMS50cmFuc2l0aW9uKShcImNvbm5lY3RlZFwiLCBcImFjdGl2ZVwiLCAoMCwgcm9ib3QzXzEucmVkdWNlKShjb25uZWN0aW9uRXN0YWJsaXNoZWQpKSwgKDAsIHJvYm90M18xLnRyYW5zaXRpb24pKFwiY29ubmVjdGlvbkNsb3NlZFwiLCBcImlkbGVcIiwgKDAsIHJvYm90M18xLnJlZHVjZSkoY2xvc2VDb25uZWN0aW9uKSksICgwLCByb2JvdDNfMS50cmFuc2l0aW9uKShcInNlbmRcIiwgXCJjb25uZWN0aW5nXCIsICgwLCByb2JvdDNfMS5yZWR1Y2UpKGVucXVldWVNZXNzYWdlKSksICgwLCByb2JvdDNfMS50cmFuc2l0aW9uKShcImNsb3NlXCIsIFwiaWRsZVwiLCAoMCwgcm9ib3QzXzEucmVkdWNlKShjbG9zZUNvbm5lY3Rpb24pKSwgKDAsIHJvYm90M18xLmltbWVkaWF0ZSkoXCJhdXRoUmVxdWlyZWRcIiwgKDAsIHJvYm90M18xLmd1YXJkKShub1Rva2VuKSkpLFxuICAgIGF1dGhSZXF1aXJlZDogKDAsIHJvYm90M18xLnN0YXRlKSgoMCwgcm9ib3QzXzEudHJhbnNpdGlvbikoXCJpbml0aWF0ZUF1dGhcIiwgXCJhdXRoSW5Qcm9ncmVzc1wiKSwgKDAsIHJvYm90M18xLnRyYW5zaXRpb24pKFwic2VuZFwiLCBcImF1dGhSZXF1aXJlZFwiLCAoMCwgcm9ib3QzXzEucmVkdWNlKShlbnF1ZXVlTWVzc2FnZSkpLCAoMCwgcm9ib3QzXzEudHJhbnNpdGlvbikoXCJjbG9zZVwiLCBcImlkbGVcIiwgKDAsIHJvYm90M18xLnJlZHVjZSkoY2xvc2VDb25uZWN0aW9uKSkpLFxuICAgIGF1dGhJblByb2dyZXNzOiAoMCwgcm9ib3QzXzEuc3RhdGUpKCgwLCByb2JvdDNfMS50cmFuc2l0aW9uKShcImF1dGhlbnRpY2F0ZWRcIiwgXCJjb25uZWN0aW5nXCIsICgwLCByb2JvdDNfMS5yZWR1Y2UpKHNldFRva2VuKSksICgwLCByb2JvdDNfMS50cmFuc2l0aW9uKShcInVuYXV0aG9yaXplZFwiLCBcImlkbGVcIiwgKDAsIHJvYm90M18xLnJlZHVjZSkoZXhwaXJlVG9rZW4pLCAoMCwgcm9ib3QzXzEucmVkdWNlKShjbG9zZUNvbm5lY3Rpb24pKSwgKDAsIHJvYm90M18xLnRyYW5zaXRpb24pKFwic2VuZFwiLCBcImF1dGhJblByb2dyZXNzXCIsICgwLCByb2JvdDNfMS5yZWR1Y2UpKGVucXVldWVNZXNzYWdlKSksICgwLCByb2JvdDNfMS50cmFuc2l0aW9uKShcImNsb3NlXCIsIFwiaWRsZVwiLCAoMCwgcm9ib3QzXzEucmVkdWNlKShjbG9zZUNvbm5lY3Rpb24pKSksXG4gICAgYWN0aXZlOiAoMCwgcm9ib3QzXzEuc3RhdGUpKCgwLCByb2JvdDNfMS50cmFuc2l0aW9uKShcInNlbmRcIiwgXCJhY3RpdmVcIiwgKDAsIHJvYm90M18xLnJlZHVjZSkoc2VuZE1lc3NhZ2UpKSwgKDAsIHJvYm90M18xLnRyYW5zaXRpb24pKFwidW5hdXRob3JpemVkXCIsIFwiaWRsZVwiLCAoMCwgcm9ib3QzXzEucmVkdWNlKShleHBpcmVUb2tlbikpLCAoMCwgcm9ib3QzXzEudHJhbnNpdGlvbikoXCJjb25uZWN0aW9uQ2xvc2VkXCIsIFwiaWRsZVwiLCAoMCwgcm9ib3QzXzEucmVkdWNlKShjbG9zZUNvbm5lY3Rpb24pKSwgKDAsIHJvYm90M18xLnRyYW5zaXRpb24pKFwiY2xvc2VcIiwgXCJpZGxlXCIsICgwLCByb2JvdDNfMS5yZWR1Y2UpKGNsb3NlQ29ubmVjdGlvbikpKSxcbiAgICBmYWlsZWQ6ICgwLCByb2JvdDNfMS5zdGF0ZSkoKDAsIHJvYm90M18xLnRyYW5zaXRpb24pKFwic2VuZFwiLCBcImZhaWxlZFwiKSwgKDAsIHJvYm90M18xLnRyYW5zaXRpb24pKFwiY2xvc2VcIiwgXCJpZGxlXCIsICgwLCByb2JvdDNfMS5yZWR1Y2UpKGNsb3NlQ29ubmVjdGlvbikpKSxcbn0sIGluaXRpYWxTdGF0ZSk7XG5mdW5jdGlvbiBidWlsZFJlYWx0aW1lVXJsKGFwcCwgeyB0b2tlbiwgbWF4QnVmZmVyaW5nIH0pIHtcbiAgICBpZiAobWF4QnVmZmVyaW5nICE9PSB1bmRlZmluZWQgJiYgKG1heEJ1ZmZlcmluZyA8IDEgfHwgbWF4QnVmZmVyaW5nID4gNjApKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIlRoZSBgbWF4QnVmZmVyaW5nYCBtdXN0IGJlIGJldHdlZW4gMSBhbmQgNjAgKGluY2x1c2l2ZSlcIik7XG4gICAgfVxuICAgIGNvbnN0IHF1ZXJ5UGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcyh7XG4gICAgICAgIGZhbF9qd3RfdG9rZW46IHRva2VuLFxuICAgIH0pO1xuICAgIGlmIChtYXhCdWZmZXJpbmcgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICBxdWVyeVBhcmFtcy5zZXQoXCJtYXhfYnVmZmVyaW5nXCIsIG1heEJ1ZmZlcmluZy50b0ZpeGVkKDApKTtcbiAgICB9XG4gICAgY29uc3QgYXBwSWQgPSAoMCwgdXRpbHNfMS5lbnN1cmVBcHBJZEZvcm1hdCkoYXBwKTtcbiAgICByZXR1cm4gYHdzczovL2ZhbC5ydW4vJHthcHBJZH0vcmVhbHRpbWU/JHtxdWVyeVBhcmFtcy50b1N0cmluZygpfWA7XG59XG5jb25zdCBERUZBVUxUX1RIUk9UVExFX0lOVEVSVkFMID0gMTI4O1xuZnVuY3Rpb24gaXNVbmF1dGhvcml6ZWRFcnJvcihtZXNzYWdlKSB7XG4gICAgLy8gVE9ETyB3ZSBuZWVkIGJldHRlciBwcm90b2NvbCBkZWZpbml0aW9uIHdpdGggZXJyb3IgY29kZXNcbiAgICByZXR1cm4gbWVzc2FnZVtcInN0YXR1c1wiXSA9PT0gXCJlcnJvclwiICYmIG1lc3NhZ2VbXCJlcnJvclwiXSA9PT0gXCJVbmF1dGhvcml6ZWRcIjtcbn1cbi8qKlxuICogU2VlIGh0dHBzOi8vd3d3LnJmYy1lZGl0b3Iub3JnL3JmYy9yZmM2NDU1Lmh0bWwjc2VjdGlvbi03LjQuMVxuICovXG5jb25zdCBXZWJTb2NrZXRFcnJvckNvZGVzID0ge1xuICAgIE5PUk1BTF9DTE9TVVJFOiAxMDAwLFxuICAgIEdPSU5HX0FXQVk6IDEwMDEsXG59O1xuY29uc3QgY29ubmVjdGlvbkNhY2hlID0gbmV3IE1hcCgpO1xuY29uc3QgY29ubmVjdGlvbkNhbGxiYWNrcyA9IG5ldyBNYXAoKTtcbmZ1bmN0aW9uIHJldXNlSW50ZXJwcmV0ZXIoa2V5LCB0aHJvdHRsZUludGVydmFsLCBvbkNoYW5nZSkge1xuICAgIGlmICghY29ubmVjdGlvbkNhY2hlLmhhcyhrZXkpKSB7XG4gICAgICAgIGNvbnN0IG1hY2hpbmUgPSAoMCwgcm9ib3QzXzEuaW50ZXJwcmV0KShjb25uZWN0aW9uU3RhdGVNYWNoaW5lLCBvbkNoYW5nZSk7XG4gICAgICAgIGNvbm5lY3Rpb25DYWNoZS5zZXQoa2V5LCBPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIG1hY2hpbmUpLCB7IHRocm90dGxlZFNlbmQ6IHRocm90dGxlSW50ZXJ2YWwgPiAwXG4gICAgICAgICAgICAgICAgPyAoMCwgdXRpbHNfMS50aHJvdHRsZSkobWFjaGluZS5zZW5kLCB0aHJvdHRsZUludGVydmFsLCB0cnVlKVxuICAgICAgICAgICAgICAgIDogbWFjaGluZS5zZW5kIH0pKTtcbiAgICB9XG4gICAgcmV0dXJuIGNvbm5lY3Rpb25DYWNoZS5nZXQoa2V5KTtcbn1cbmNvbnN0IG5vb3AgPSAoKSA9PiB7XG4gICAgLyogTm8tb3AgKi9cbn07XG4vKipcbiAqIEEgbm8tb3AgY29ubmVjdGlvbiB0aGF0IGRvZXMgbm90IHNlbmQgYW55IG1lc3NhZ2UuXG4gKiBVc2VmdWwgb24gdGhlIGZyYW1ld29ya3MgdGhhdCByZXVzZSBjb2RlIGZvciBib3RoIHNzciBhbmQgY3NyIChlLmcuIE5leHQpXG4gKiBzbyB0aGUgY2FsbCB3aGVuIGRvaW5nIHNzciBoYXMgbm8gc2lkZS1lZmZlY3RzLlxuICovXG4vLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L25vLWV4cGxpY2l0LWFueVxuY29uc3QgTm9PcENvbm5lY3Rpb24gPSB7XG4gICAgc2VuZDogbm9vcCxcbiAgICBjbG9zZTogbm9vcCxcbn07XG5mdW5jdGlvbiBpc1N1Y2Nlc3NmdWxSZXN1bHQoZGF0YSkge1xuICAgIHJldHVybiAoZGF0YS5zdGF0dXMgIT09IFwiZXJyb3JcIiAmJlxuICAgICAgICBkYXRhLnR5cGUgIT09IFwieC1mYWwtbWVzc2FnZVwiICYmXG4gICAgICAgICFpc0ZhbEVycm9yUmVzdWx0KGRhdGEpKTtcbn1cbmZ1bmN0aW9uIGlzRmFsRXJyb3JSZXN1bHQoZGF0YSkge1xuICAgIHJldHVybiBkYXRhLnR5cGUgPT09IFwieC1mYWwtZXJyb3JcIjtcbn1cbi8qKlxuICogVGhlIGRlZmF1bHQgaW1wbGVtZW50YXRpb24gb2YgdGhlIHJlYWx0aW1lIGNsaWVudC5cbiAqL1xuZXhwb3J0cy5yZWFsdGltZUltcGwgPSB7XG4gICAgY29ubmVjdChhcHAsIGhhbmRsZXIpIHtcbiAgICAgICAgY29uc3QgeyBcbiAgICAgICAgLy8gaWYgcnVubmluZyBvbiBSZWFjdCBpbiB0aGUgc2VydmVyLCBzZXQgY2xpZW50T25seSB0byB0cnVlIGJ5IGRlZmF1bHRcbiAgICAgICAgY2xpZW50T25seSA9ICgwLCB1dGlsc18xLmlzUmVhY3QpKCkgJiYgISgwLCBydW50aW1lXzEuaXNCcm93c2VyKSgpLCBjb25uZWN0aW9uS2V5ID0gY3J5cHRvLnJhbmRvbVVVSUQoKSwgbWF4QnVmZmVyaW5nLCB0aHJvdHRsZUludGVydmFsID0gREVGQVVMVF9USFJPVFRMRV9JTlRFUlZBTCwgfSA9IGhhbmRsZXI7XG4gICAgICAgIGlmIChjbGllbnRPbmx5ICYmICEoMCwgcnVudGltZV8xLmlzQnJvd3NlcikoKSkge1xuICAgICAgICAgICAgcmV0dXJuIE5vT3BDb25uZWN0aW9uO1xuICAgICAgICB9XG4gICAgICAgIGxldCBwcmV2aW91c1N0YXRlO1xuICAgICAgICAvLyBBbHRob3VnaCB0aGUgc3RhdGUgbWFjaGluZSBpcyBjYWNoZWQgc28gd2UgZG9uJ3Qgb3BlbiBtdWx0aXBsZSBjb25uZWN0aW9ucyxcbiAgICAgICAgLy8gd2Ugc3RpbGwgbmVlZCB0byB1cGRhdGUgdGhlIGNhbGxiYWNrcyBzbyB3ZSBjYW4gY2FsbCB0aGUgY29ycmVjdCByZWZlcmVuY2VzXG4gICAgICAgIC8vIHdoZW4gdGhlIHN0YXRlIG1hY2hpbmUgaXMgcmV1c2VkLiBUaGlzIGlzIG5lZWRlZCBiZWNhdXNlIHRoZSBjYWxsYmFja3NcbiAgICAgICAgLy8gYXJlIHBhc3NlZCBhcyBwYXJ0IG9mIHRoZSBoYW5kbGVyIG9iamVjdCwgd2hpY2ggY2FuIGJlIGRpZmZlcmVudCBhY3Jvc3NcbiAgICAgICAgLy8gZGlmZmVyZW50IGNhbGxzIHRvIGBjb25uZWN0YC5cbiAgICAgICAgY29ubmVjdGlvbkNhbGxiYWNrcy5zZXQoY29ubmVjdGlvbktleSwge1xuICAgICAgICAgICAgb25FcnJvcjogaGFuZGxlci5vbkVycm9yLFxuICAgICAgICAgICAgb25SZXN1bHQ6IGhhbmRsZXIub25SZXN1bHQsXG4gICAgICAgIH0pO1xuICAgICAgICBjb25zdCBnZXRDYWxsYmFja3MgPSAoKSA9PiBjb25uZWN0aW9uQ2FsbGJhY2tzLmdldChjb25uZWN0aW9uS2V5KTtcbiAgICAgICAgY29uc3Qgc3RhdGVNYWNoaW5lID0gcmV1c2VJbnRlcnByZXRlcihjb25uZWN0aW9uS2V5LCB0aHJvdHRsZUludGVydmFsLCAoeyBjb250ZXh0LCBtYWNoaW5lLCBzZW5kIH0pID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHsgZW5xdWV1ZWRNZXNzYWdlLCB0b2tlbiB9ID0gY29udGV4dDtcbiAgICAgICAgICAgIGlmIChtYWNoaW5lLmN1cnJlbnQgPT09IFwiYWN0aXZlXCIgJiYgZW5xdWV1ZWRNZXNzYWdlKSB7XG4gICAgICAgICAgICAgICAgc2VuZCh7IHR5cGU6IFwic2VuZFwiLCBtZXNzYWdlOiBlbnF1ZXVlZE1lc3NhZ2UgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAobWFjaGluZS5jdXJyZW50ID09PSBcImF1dGhSZXF1aXJlZFwiICYmXG4gICAgICAgICAgICAgICAgdG9rZW4gPT09IHVuZGVmaW5lZCAmJlxuICAgICAgICAgICAgICAgIHByZXZpb3VzU3RhdGUgIT09IG1hY2hpbmUuY3VycmVudCkge1xuICAgICAgICAgICAgICAgIHNlbmQoeyB0eXBlOiBcImluaXRpYXRlQXV0aFwiIH0pO1xuICAgICAgICAgICAgICAgICgwLCBhdXRoXzEuZ2V0VGVtcG9yYXJ5QXV0aFRva2VuKShhcHApXG4gICAgICAgICAgICAgICAgICAgIC50aGVuKCh0b2tlbikgPT4ge1xuICAgICAgICAgICAgICAgICAgICBzZW5kKHsgdHlwZTogXCJhdXRoZW50aWNhdGVkXCIsIHRva2VuIH0pO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCB0b2tlbkV4cGlyYXRpb25UaW1lb3V0ID0gTWF0aC5yb3VuZChhdXRoXzEuVE9LRU5fRVhQSVJBVElPTl9TRUNPTkRTICogMC45ICogMTAwMCk7XG4gICAgICAgICAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgc2VuZCh7IHR5cGU6IFwiZXhwaXJlVG9rZW5cIiB9KTtcbiAgICAgICAgICAgICAgICAgICAgfSwgdG9rZW5FeHBpcmF0aW9uVGltZW91dCk7XG4gICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICAgICAgLmNhdGNoKChlcnJvcikgPT4ge1xuICAgICAgICAgICAgICAgICAgICBzZW5kKHsgdHlwZTogXCJ1bmF1dGhvcml6ZWRcIiwgZXJyb3IgfSk7XG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAobWFjaGluZS5jdXJyZW50ID09PSBcImNvbm5lY3RpbmdcIiAmJlxuICAgICAgICAgICAgICAgIHByZXZpb3VzU3RhdGUgIT09IG1hY2hpbmUuY3VycmVudCAmJlxuICAgICAgICAgICAgICAgIHRva2VuICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgICAgICBjb25zdCB3cyA9IG5ldyBXZWJTb2NrZXQoYnVpbGRSZWFsdGltZVVybChhcHAsIHsgdG9rZW4sIG1heEJ1ZmZlcmluZyB9KSk7XG4gICAgICAgICAgICAgICAgd3Mub25vcGVuID0gKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICBzZW5kKHsgdHlwZTogXCJjb25uZWN0ZWRcIiwgd2Vic29ja2V0OiB3cyB9KTtcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgIHdzLm9uY2xvc2UgPSAoZXZlbnQpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGV2ZW50LmNvZGUgIT09IFdlYlNvY2tldEVycm9yQ29kZXMuTk9STUFMX0NMT1NVUkUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHsgb25FcnJvciA9IG5vb3AgfSA9IGdldENhbGxiYWNrcygpO1xuICAgICAgICAgICAgICAgICAgICAgICAgb25FcnJvcihuZXcgcmVzcG9uc2VfMS5BcGlFcnJvcih7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogYEVycm9yIGNsb3NpbmcgdGhlIGNvbm5lY3Rpb246ICR7ZXZlbnQucmVhc29ufWAsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc3RhdHVzOiBldmVudC5jb2RlLFxuICAgICAgICAgICAgICAgICAgICAgICAgfSkpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIHNlbmQoeyB0eXBlOiBcImNvbm5lY3Rpb25DbG9zZWRcIiwgY29kZTogZXZlbnQuY29kZSB9KTtcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgICAgIHdzLm9uZXJyb3IgPSAoZXZlbnQpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgLy8gVE9ETyBzcGVjaWZ5IGVycm9yIHByb3RvY29sIGZvciBpZGVudGlmaWVkIGVycm9yc1xuICAgICAgICAgICAgICAgICAgICBjb25zdCB7IG9uRXJyb3IgPSBub29wIH0gPSBnZXRDYWxsYmFja3MoKTtcbiAgICAgICAgICAgICAgICAgICAgb25FcnJvcihuZXcgcmVzcG9uc2VfMS5BcGlFcnJvcih7IG1lc3NhZ2U6IFwiVW5rbm93biBlcnJvclwiLCBzdGF0dXM6IDUwMCB9KSk7XG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICB3cy5vbm1lc3NhZ2UgPSAoZXZlbnQpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgeyBvblJlc3VsdCB9ID0gZ2V0Q2FsbGJhY2tzKCk7XG4gICAgICAgICAgICAgICAgICAgIC8vIEhhbmRsZSBiaW5hcnkgbWVzc2FnZXMgYXMgbXNncGFjayBtZXNzYWdlc1xuICAgICAgICAgICAgICAgICAgICBpZiAoZXZlbnQuZGF0YSBpbnN0YW5jZW9mIEFycmF5QnVmZmVyKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCByZXN1bHQgPSAoMCwgbXNncGFja18xLmRlY29kZSkobmV3IFVpbnQ4QXJyYXkoZXZlbnQuZGF0YSkpO1xuICAgICAgICAgICAgICAgICAgICAgICAgb25SZXN1bHQocmVzdWx0KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBpZiAoZXZlbnQuZGF0YSBpbnN0YW5jZW9mIFVpbnQ4QXJyYXkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9ICgwLCBtc2dwYWNrXzEuZGVjb2RlKShldmVudC5kYXRhKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uUmVzdWx0KHJlc3VsdCk7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgaWYgKGV2ZW50LmRhdGEgaW5zdGFuY2VvZiBCbG9iKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBldmVudC5kYXRhLmFycmF5QnVmZmVyKCkudGhlbigoYnVmZmVyKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgcmVzdWx0ID0gKDAsIG1zZ3BhY2tfMS5kZWNvZGUpKG5ldyBVaW50OEFycmF5KGJ1ZmZlcikpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uUmVzdWx0KHJlc3VsdCk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAvLyBPdGhlcndpc2UgaGFuZGxlIHN0cmluZ3MgYXMgcGxhaW4gSlNPTiBtZXNzYWdlc1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBkYXRhID0gSlNPTi5wYXJzZShldmVudC5kYXRhKTtcbiAgICAgICAgICAgICAgICAgICAgLy8gRHJvcCBtZXNzYWdlcyB0aGF0IGFyZSBub3QgcmVsYXRlZCB0byB0aGUgYWN0dWFsIHJlc3VsdC5cbiAgICAgICAgICAgICAgICAgICAgLy8gSW4gdGhlIGZ1dHVyZSwgd2UgbWlnaHQgd2FudCB0byBoYW5kbGUgb3RoZXIgdHlwZXMgb2YgbWVzc2FnZXMuXG4gICAgICAgICAgICAgICAgICAgIC8vIFRPRE86IHNwZWNpZnkgdGhlIGZhbCB3cyBwcm90b2NvbCBmb3JtYXRcbiAgICAgICAgICAgICAgICAgICAgaWYgKGlzVW5hdXRob3JpemVkRXJyb3IoZGF0YSkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHNlbmQoeyB0eXBlOiBcInVuYXV0aG9yaXplZFwiLCBlcnJvcjogbmV3IEVycm9yKFwiVW5hdXRob3JpemVkXCIpIH0pO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGlmIChpc1N1Y2Nlc3NmdWxSZXN1bHQoZGF0YSkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uUmVzdWx0KGRhdGEpO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGlmIChpc0ZhbEVycm9yUmVzdWx0KGRhdGEpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoZGF0YS5lcnJvciA9PT0gXCJUSU1FT1VUXCIpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBUaW1lb3V0IGVycm9yIG1lc3NhZ2VzIGp1c3QgaW5kaWNhdGUgdGhhdCB0aGUgY29ubmVjdGlvbiBoYXNuJ3RcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyByZWNlaXZlZCBhbiBpbmNvbWluZyBtZXNzYWdlIGZvciBhIHdoaWxlLiBXZSBkb24ndCBuZWVkIHRvXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gaGFuZGxlIHRoZW0gYXMgZXJyb3JzLlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IHsgb25FcnJvciA9IG5vb3AgfSA9IGdldENhbGxiYWNrcygpO1xuICAgICAgICAgICAgICAgICAgICAgICAgb25FcnJvcihuZXcgcmVzcG9uc2VfMS5BcGlFcnJvcih7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogYCR7ZGF0YS5lcnJvcn06ICR7ZGF0YS5yZWFzb259YCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAvLyBUT0RPIGJldHRlciBlcnJvciBzdGF0dXMgY29kZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHN0YXR1czogNDAwLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJvZHk6IGRhdGEsXG4gICAgICAgICAgICAgICAgICAgICAgICB9KSk7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcHJldmlvdXNTdGF0ZSA9IG1hY2hpbmUuY3VycmVudDtcbiAgICAgICAgfSk7XG4gICAgICAgIGNvbnN0IHNlbmQgPSAoaW5wdXQpID0+IHtcbiAgICAgICAgICAgIC8vIFVzZSB0aHJvdHRsZWQgc2VuZCB0byBhdm9pZCBzZW5kaW5nIHRvbyBtYW55IG1lc3NhZ2VzXG4gICAgICAgICAgICB2YXIgX2E7XG4gICAgICAgICAgICBjb25zdCBtZXNzYWdlID0gaW5wdXQgaW5zdGFuY2VvZiBVaW50OEFycmF5XG4gICAgICAgICAgICAgICAgPyBpbnB1dFxuICAgICAgICAgICAgICAgIDogT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKHt9LCBpbnB1dCksIHsgcmVxdWVzdF9pZDogKF9hID0gaW5wdXRbXCJyZXF1ZXN0X2lkXCJdKSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiBjcnlwdG8ucmFuZG9tVVVJRCgpIH0pO1xuICAgICAgICAgICAgc3RhdGVNYWNoaW5lLnRocm90dGxlZFNlbmQoe1xuICAgICAgICAgICAgICAgIHR5cGU6IFwic2VuZFwiLFxuICAgICAgICAgICAgICAgIG1lc3NhZ2UsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfTtcbiAgICAgICAgY29uc3QgY2xvc2UgPSAoKSA9PiB7XG4gICAgICAgICAgICBzdGF0ZU1hY2hpbmUuc2VuZCh7IHR5cGU6IFwiY2xvc2VcIiB9KTtcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHNlbmQsXG4gICAgICAgICAgICBjbG9zZSxcbiAgICAgICAgfTtcbiAgICB9LFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXJlYWx0aW1lLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@fal-ai/serverless-client/src/realtime.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@fal-ai/serverless-client/src/request.js":
/*!***************************************************************!*\
  !*** ./node_modules/@fal-ai/serverless-client/src/request.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __rest = (this && this.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.dispatchRequest = dispatchRequest;\nconst config_1 = __webpack_require__(/*! ./config */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/config.js\");\nconst runtime_1 = __webpack_require__(/*! ./runtime */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/runtime.js\");\nconst isCloudflareWorkers = typeof navigator !== \"undefined\" &&\n    (navigator === null || navigator === void 0 ? void 0 : navigator.userAgent) === \"Cloudflare-Workers\";\nfunction dispatchRequest(method_1, targetUrl_1, input_1) {\n    return __awaiter(this, arguments, void 0, function* (method, targetUrl, input, options = {}) {\n        var _a;\n        const { credentials: credentialsValue, requestMiddleware, responseHandler, fetch, } = (0, config_1.getConfig)();\n        const userAgent = (0, runtime_1.isBrowser)() ? {} : { \"User-Agent\": (0, runtime_1.getUserAgent)() };\n        const credentials = typeof credentialsValue === \"function\"\n            ? credentialsValue()\n            : credentialsValue;\n        const { url, headers } = yield requestMiddleware({\n            url: targetUrl,\n            method: method.toUpperCase(),\n        });\n        const authHeader = credentials ? { Authorization: `Key ${credentials}` } : {};\n        const requestHeaders = Object.assign(Object.assign(Object.assign(Object.assign({}, authHeader), { Accept: \"application/json\", \"Content-Type\": \"application/json\" }), userAgent), (headers !== null && headers !== void 0 ? headers : {}));\n        const { responseHandler: customResponseHandler } = options, requestInit = __rest(options, [\"responseHandler\"]);\n        const response = yield fetch(url, Object.assign(Object.assign(Object.assign(Object.assign({}, requestInit), { method, headers: Object.assign(Object.assign({}, requestHeaders), ((_a = requestInit.headers) !== null && _a !== void 0 ? _a : {})) }), (!isCloudflareWorkers && { mode: \"cors\" })), { body: method.toLowerCase() !== \"get\" && input\n                ? JSON.stringify(input)\n                : undefined }));\n        const handleResponse = customResponseHandler !== null && customResponseHandler !== void 0 ? customResponseHandler : responseHandler;\n        return yield handleResponse(response);\n    });\n}\n//# sourceMappingURL=request.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGZhbC1haS9zZXJ2ZXJsZXNzLWNsaWVudC9zcmMvcmVxdWVzdC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0EsNEJBQTRCLCtEQUErRCxpQkFBaUI7QUFDNUc7QUFDQSxvQ0FBb0MsTUFBTSwrQkFBK0IsWUFBWTtBQUNyRixtQ0FBbUMsTUFBTSxtQ0FBbUMsWUFBWTtBQUN4RixnQ0FBZ0M7QUFDaEM7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkRBQTZELGNBQWM7QUFDM0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCx1QkFBdUI7QUFDdkIsaUJBQWlCLG1CQUFPLENBQUMsOEVBQVU7QUFDbkMsa0JBQWtCLG1CQUFPLENBQUMsZ0ZBQVc7QUFDckM7QUFDQTtBQUNBO0FBQ0EsK0ZBQStGO0FBQy9GO0FBQ0EsZ0JBQWdCLDRFQUE0RTtBQUM1RiwwREFBMEQsSUFBSTtBQUM5RDtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsZUFBZTtBQUMvQjtBQUNBO0FBQ0EsU0FBUztBQUNULDJDQUEyQyxzQkFBc0IsWUFBWSxJQUFJO0FBQ2pGLHlGQUF5RixpQkFBaUIsZ0VBQWdFLHFFQUFxRTtBQUMvTyxnQkFBZ0IseUNBQXlDO0FBQ3pELG9HQUFvRyxrQkFBa0IsK0NBQStDLGtGQUFrRixJQUFJLDhCQUE4QixjQUFjLE1BQU07QUFDN1M7QUFDQSw2QkFBNkI7QUFDN0I7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvbGl1cGVuZy9jdXJzb3Iv6L+b6Zi2L0ZsdXjlr7nor53lvI/lupTnlKgvZmx1eC1jaGF0LWFwcC9ub2RlX21vZHVsZXMvQGZhbC1haS9zZXJ2ZXJsZXNzLWNsaWVudC9zcmMvcmVxdWVzdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBfX2F3YWl0ZXIgPSAodGhpcyAmJiB0aGlzLl9fYXdhaXRlcikgfHwgZnVuY3Rpb24gKHRoaXNBcmcsIF9hcmd1bWVudHMsIFAsIGdlbmVyYXRvcikge1xuICAgIGZ1bmN0aW9uIGFkb3B0KHZhbHVlKSB7IHJldHVybiB2YWx1ZSBpbnN0YW5jZW9mIFAgPyB2YWx1ZSA6IG5ldyBQKGZ1bmN0aW9uIChyZXNvbHZlKSB7IHJlc29sdmUodmFsdWUpOyB9KTsgfVxuICAgIHJldHVybiBuZXcgKFAgfHwgKFAgPSBQcm9taXNlKSkoZnVuY3Rpb24gKHJlc29sdmUsIHJlamVjdCkge1xuICAgICAgICBmdW5jdGlvbiBmdWxmaWxsZWQodmFsdWUpIHsgdHJ5IHsgc3RlcChnZW5lcmF0b3IubmV4dCh2YWx1ZSkpOyB9IGNhdGNoIChlKSB7IHJlamVjdChlKTsgfSB9XG4gICAgICAgIGZ1bmN0aW9uIHJlamVjdGVkKHZhbHVlKSB7IHRyeSB7IHN0ZXAoZ2VuZXJhdG9yW1widGhyb3dcIl0odmFsdWUpKTsgfSBjYXRjaCAoZSkgeyByZWplY3QoZSk7IH0gfVxuICAgICAgICBmdW5jdGlvbiBzdGVwKHJlc3VsdCkgeyByZXN1bHQuZG9uZSA/IHJlc29sdmUocmVzdWx0LnZhbHVlKSA6IGFkb3B0KHJlc3VsdC52YWx1ZSkudGhlbihmdWxmaWxsZWQsIHJlamVjdGVkKTsgfVxuICAgICAgICBzdGVwKChnZW5lcmF0b3IgPSBnZW5lcmF0b3IuYXBwbHkodGhpc0FyZywgX2FyZ3VtZW50cyB8fCBbXSkpLm5leHQoKSk7XG4gICAgfSk7XG59O1xudmFyIF9fcmVzdCA9ICh0aGlzICYmIHRoaXMuX19yZXN0KSB8fCBmdW5jdGlvbiAocywgZSkge1xuICAgIHZhciB0ID0ge307XG4gICAgZm9yICh2YXIgcCBpbiBzKSBpZiAoT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKHMsIHApICYmIGUuaW5kZXhPZihwKSA8IDApXG4gICAgICAgIHRbcF0gPSBzW3BdO1xuICAgIGlmIChzICE9IG51bGwgJiYgdHlwZW9mIE9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHMgPT09IFwiZnVuY3Rpb25cIilcbiAgICAgICAgZm9yICh2YXIgaSA9IDAsIHAgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlTeW1ib2xzKHMpOyBpIDwgcC5sZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgaWYgKGUuaW5kZXhPZihwW2ldKSA8IDAgJiYgT2JqZWN0LnByb3RvdHlwZS5wcm9wZXJ0eUlzRW51bWVyYWJsZS5jYWxsKHMsIHBbaV0pKVxuICAgICAgICAgICAgICAgIHRbcFtpXV0gPSBzW3BbaV1dO1xuICAgICAgICB9XG4gICAgcmV0dXJuIHQ7XG59O1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5kaXNwYXRjaFJlcXVlc3QgPSBkaXNwYXRjaFJlcXVlc3Q7XG5jb25zdCBjb25maWdfMSA9IHJlcXVpcmUoXCIuL2NvbmZpZ1wiKTtcbmNvbnN0IHJ1bnRpbWVfMSA9IHJlcXVpcmUoXCIuL3J1bnRpbWVcIik7XG5jb25zdCBpc0Nsb3VkZmxhcmVXb3JrZXJzID0gdHlwZW9mIG5hdmlnYXRvciAhPT0gXCJ1bmRlZmluZWRcIiAmJlxuICAgIChuYXZpZ2F0b3IgPT09IG51bGwgfHwgbmF2aWdhdG9yID09PSB2b2lkIDAgPyB2b2lkIDAgOiBuYXZpZ2F0b3IudXNlckFnZW50KSA9PT0gXCJDbG91ZGZsYXJlLVdvcmtlcnNcIjtcbmZ1bmN0aW9uIGRpc3BhdGNoUmVxdWVzdChtZXRob2RfMSwgdGFyZ2V0VXJsXzEsIGlucHV0XzEpIHtcbiAgICByZXR1cm4gX19hd2FpdGVyKHRoaXMsIGFyZ3VtZW50cywgdm9pZCAwLCBmdW5jdGlvbiogKG1ldGhvZCwgdGFyZ2V0VXJsLCBpbnB1dCwgb3B0aW9ucyA9IHt9KSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgY29uc3QgeyBjcmVkZW50aWFsczogY3JlZGVudGlhbHNWYWx1ZSwgcmVxdWVzdE1pZGRsZXdhcmUsIHJlc3BvbnNlSGFuZGxlciwgZmV0Y2gsIH0gPSAoMCwgY29uZmlnXzEuZ2V0Q29uZmlnKSgpO1xuICAgICAgICBjb25zdCB1c2VyQWdlbnQgPSAoMCwgcnVudGltZV8xLmlzQnJvd3NlcikoKSA/IHt9IDogeyBcIlVzZXItQWdlbnRcIjogKDAsIHJ1bnRpbWVfMS5nZXRVc2VyQWdlbnQpKCkgfTtcbiAgICAgICAgY29uc3QgY3JlZGVudGlhbHMgPSB0eXBlb2YgY3JlZGVudGlhbHNWYWx1ZSA9PT0gXCJmdW5jdGlvblwiXG4gICAgICAgICAgICA/IGNyZWRlbnRpYWxzVmFsdWUoKVxuICAgICAgICAgICAgOiBjcmVkZW50aWFsc1ZhbHVlO1xuICAgICAgICBjb25zdCB7IHVybCwgaGVhZGVycyB9ID0geWllbGQgcmVxdWVzdE1pZGRsZXdhcmUoe1xuICAgICAgICAgICAgdXJsOiB0YXJnZXRVcmwsXG4gICAgICAgICAgICBtZXRob2Q6IG1ldGhvZC50b1VwcGVyQ2FzZSgpLFxuICAgICAgICB9KTtcbiAgICAgICAgY29uc3QgYXV0aEhlYWRlciA9IGNyZWRlbnRpYWxzID8geyBBdXRob3JpemF0aW9uOiBgS2V5ICR7Y3JlZGVudGlhbHN9YCB9IDoge307XG4gICAgICAgIGNvbnN0IHJlcXVlc3RIZWFkZXJzID0gT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgYXV0aEhlYWRlciksIHsgQWNjZXB0OiBcImFwcGxpY2F0aW9uL2pzb25cIiwgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIgfSksIHVzZXJBZ2VudCksIChoZWFkZXJzICE9PSBudWxsICYmIGhlYWRlcnMgIT09IHZvaWQgMCA/IGhlYWRlcnMgOiB7fSkpO1xuICAgICAgICBjb25zdCB7IHJlc3BvbnNlSGFuZGxlcjogY3VzdG9tUmVzcG9uc2VIYW5kbGVyIH0gPSBvcHRpb25zLCByZXF1ZXN0SW5pdCA9IF9fcmVzdChvcHRpb25zLCBbXCJyZXNwb25zZUhhbmRsZXJcIl0pO1xuICAgICAgICBjb25zdCByZXNwb25zZSA9IHlpZWxkIGZldGNoKHVybCwgT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgcmVxdWVzdEluaXQpLCB7IG1ldGhvZCwgaGVhZGVyczogT2JqZWN0LmFzc2lnbihPYmplY3QuYXNzaWduKHt9LCByZXF1ZXN0SGVhZGVycyksICgoX2EgPSByZXF1ZXN0SW5pdC5oZWFkZXJzKSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiB7fSkpIH0pLCAoIWlzQ2xvdWRmbGFyZVdvcmtlcnMgJiYgeyBtb2RlOiBcImNvcnNcIiB9KSksIHsgYm9keTogbWV0aG9kLnRvTG93ZXJDYXNlKCkgIT09IFwiZ2V0XCIgJiYgaW5wdXRcbiAgICAgICAgICAgICAgICA/IEpTT04uc3RyaW5naWZ5KGlucHV0KVxuICAgICAgICAgICAgICAgIDogdW5kZWZpbmVkIH0pKTtcbiAgICAgICAgY29uc3QgaGFuZGxlUmVzcG9uc2UgPSBjdXN0b21SZXNwb25zZUhhbmRsZXIgIT09IG51bGwgJiYgY3VzdG9tUmVzcG9uc2VIYW5kbGVyICE9PSB2b2lkIDAgPyBjdXN0b21SZXNwb25zZUhhbmRsZXIgOiByZXNwb25zZUhhbmRsZXI7XG4gICAgICAgIHJldHVybiB5aWVsZCBoYW5kbGVSZXNwb25zZShyZXNwb25zZSk7XG4gICAgfSk7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1yZXF1ZXN0LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@fal-ai/serverless-client/src/request.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@fal-ai/serverless-client/src/response.js":
/*!****************************************************************!*\
  !*** ./node_modules/@fal-ai/serverless-client/src/response.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, exports) {

eval("\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ValidationError = exports.ApiError = void 0;\nexports.defaultResponseHandler = defaultResponseHandler;\nclass ApiError extends Error {\n    constructor({ message, status, body }) {\n        super(message);\n        this.name = \"ApiError\";\n        this.status = status;\n        this.body = body;\n    }\n}\nexports.ApiError = ApiError;\nclass ValidationError extends ApiError {\n    constructor(args) {\n        super(args);\n        this.name = \"ValidationError\";\n    }\n    get fieldErrors() {\n        // NOTE: this is a hack to support both FastAPI/Pydantic errors\n        // and some custom 422 errors that might not be in the Pydantic format.\n        if (typeof this.body.detail === \"string\") {\n            return [\n                {\n                    loc: [\"body\"],\n                    msg: this.body.detail,\n                    type: \"value_error\",\n                },\n            ];\n        }\n        return this.body.detail || [];\n    }\n    getFieldErrors(field) {\n        return this.fieldErrors.filter((error) => error.loc[error.loc.length - 1] === field);\n    }\n}\nexports.ValidationError = ValidationError;\nfunction defaultResponseHandler(response) {\n    return __awaiter(this, void 0, void 0, function* () {\n        var _a;\n        const { status, statusText } = response;\n        const contentType = (_a = response.headers.get(\"Content-Type\")) !== null && _a !== void 0 ? _a : \"\";\n        if (!response.ok) {\n            if (contentType.includes(\"application/json\")) {\n                const body = yield response.json();\n                const ErrorType = status === 422 ? ValidationError : ApiError;\n                throw new ErrorType({\n                    message: body.message || statusText,\n                    status,\n                    body,\n                });\n            }\n            throw new ApiError({ message: `HTTP ${status}: ${statusText}`, status });\n        }\n        if (contentType.includes(\"application/json\")) {\n            return response.json();\n        }\n        if (contentType.includes(\"text/html\")) {\n            return response.text();\n        }\n        if (contentType.includes(\"application/octet-stream\")) {\n            return response.arrayBuffer();\n        }\n        // TODO convert to either number or bool automatically\n        return response.text();\n    });\n}\n//# sourceMappingURL=response.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@fal-ai/serverless-client/src/response.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@fal-ai/serverless-client/src/runtime.js":
/*!***************************************************************!*\
  !*** ./node_modules/@fal-ai/serverless-client/src/runtime.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/* eslint-disable @typescript-eslint/no-var-requires */\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.isBrowser = isBrowser;\nexports.getUserAgent = getUserAgent;\nfunction isBrowser() {\n    return (typeof window !== \"undefined\" && typeof window.document !== \"undefined\");\n}\nlet memoizedUserAgent = null;\nfunction getUserAgent() {\n    if (memoizedUserAgent !== null) {\n        return memoizedUserAgent;\n    }\n    const packageInfo = __webpack_require__(/*! ../package.json */ \"(rsc)/./node_modules/@fal-ai/serverless-client/package.json\");\n    memoizedUserAgent = `${packageInfo.name}/${packageInfo.version}`;\n    return memoizedUserAgent;\n}\n//# sourceMappingURL=runtime.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGZhbC1haS9zZXJ2ZXJsZXNzLWNsaWVudC9zcmMvcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiO0FBQ0EsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGlCQUFpQjtBQUNqQixvQkFBb0I7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixtQkFBTyxDQUFDLG9GQUFpQjtBQUNqRCwyQkFBMkIsaUJBQWlCLEdBQUcsb0JBQW9CO0FBQ25FO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL1VzZXJzL2xpdXBlbmcvY3Vyc29yL+i/m+mYti9GbHV45a+56K+d5byP5bqU55SoL2ZsdXgtY2hhdC1hcHAvbm9kZV9tb2R1bGVzL0BmYWwtYWkvc2VydmVybGVzcy1jbGllbnQvc3JjL3J1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKiBlc2xpbnQtZGlzYWJsZSBAdHlwZXNjcmlwdC1lc2xpbnQvbm8tdmFyLXJlcXVpcmVzICovXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmlzQnJvd3NlciA9IGlzQnJvd3NlcjtcbmV4cG9ydHMuZ2V0VXNlckFnZW50ID0gZ2V0VXNlckFnZW50O1xuZnVuY3Rpb24gaXNCcm93c2VyKCkge1xuICAgIHJldHVybiAodHlwZW9mIHdpbmRvdyAhPT0gXCJ1bmRlZmluZWRcIiAmJiB0eXBlb2Ygd2luZG93LmRvY3VtZW50ICE9PSBcInVuZGVmaW5lZFwiKTtcbn1cbmxldCBtZW1vaXplZFVzZXJBZ2VudCA9IG51bGw7XG5mdW5jdGlvbiBnZXRVc2VyQWdlbnQoKSB7XG4gICAgaWYgKG1lbW9pemVkVXNlckFnZW50ICE9PSBudWxsKSB7XG4gICAgICAgIHJldHVybiBtZW1vaXplZFVzZXJBZ2VudDtcbiAgICB9XG4gICAgY29uc3QgcGFja2FnZUluZm8gPSByZXF1aXJlKFwiLi4vcGFja2FnZS5qc29uXCIpO1xuICAgIG1lbW9pemVkVXNlckFnZW50ID0gYCR7cGFja2FnZUluZm8ubmFtZX0vJHtwYWNrYWdlSW5mby52ZXJzaW9ufWA7XG4gICAgcmV0dXJuIG1lbW9pemVkVXNlckFnZW50O1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cnVudGltZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@fal-ai/serverless-client/src/runtime.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@fal-ai/serverless-client/src/storage.js":
/*!***************************************************************!*\
  !*** ./node_modules/@fal-ai/serverless-client/src/storage.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.storageImpl = void 0;\nconst config_1 = __webpack_require__(/*! ./config */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/config.js\");\nconst request_1 = __webpack_require__(/*! ./request */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/request.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/utils.js\");\n/**\n * Get the file extension from the content type. This is used to generate\n * a file name if the file name is not provided.\n *\n * @param contentType the content type of the file.\n * @returns the file extension or `bin` if the content type is not recognized.\n */\nfunction getExtensionFromContentType(contentType) {\n    var _a;\n    const [_, fileType] = contentType.split(\"/\");\n    return (_a = fileType.split(/[-;]/)[0]) !== null && _a !== void 0 ? _a : \"bin\";\n}\n/**\n * Initiate the upload of a file to the server. This returns the URL to upload\n * the file to and the URL of the file once it is uploaded.\n *\n * @param file the file to upload\n * @returns the URL to upload the file to and the URL of the file once it is uploaded.\n */\nfunction initiateUpload(file) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const contentType = file.type || \"application/octet-stream\";\n        const filename = file.name || `${Date.now()}.${getExtensionFromContentType(contentType)}`;\n        return yield (0, request_1.dispatchRequest)(\"POST\", `${(0, config_1.getRestApiUrl)()}/storage/upload/initiate`, {\n            content_type: contentType,\n            file_name: filename,\n        });\n    });\n}\nexports.storageImpl = {\n    upload: (file) => __awaiter(void 0, void 0, void 0, function* () {\n        const { fetch } = (0, config_1.getConfig)();\n        const { upload_url: uploadUrl, file_url: url } = yield initiateUpload(file);\n        const response = yield fetch(uploadUrl, {\n            method: \"PUT\",\n            body: file,\n            headers: {\n                \"Content-Type\": file.type || \"application/octet-stream\",\n            },\n        });\n        const { responseHandler } = (0, config_1.getConfig)();\n        yield responseHandler(response);\n        return url;\n    }),\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    transformInput: (input) => __awaiter(void 0, void 0, void 0, function* () {\n        if (Array.isArray(input)) {\n            return Promise.all(input.map((item) => exports.storageImpl.transformInput(item)));\n        }\n        else if (input instanceof Blob) {\n            return yield exports.storageImpl.upload(input);\n        }\n        else if ((0, utils_1.isPlainObject)(input)) {\n            const inputObject = input;\n            const promises = Object.entries(inputObject).map((_a) => __awaiter(void 0, [_a], void 0, function* ([key, value]) {\n                return [key, yield exports.storageImpl.transformInput(value)];\n            }));\n            const results = yield Promise.all(promises);\n            return Object.fromEntries(results);\n        }\n        // Return the input as is if it's neither an object nor a file/blob/data URI\n        return input;\n    }),\n};\n//# sourceMappingURL=storage.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@fal-ai/serverless-client/src/storage.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@fal-ai/serverless-client/src/streaming.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@fal-ai/serverless-client/src/streaming.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __await = (this && this.__await) || function (v) { return this instanceof __await ? (this.v = v, this) : new __await(v); }\nvar __asyncGenerator = (this && this.__asyncGenerator) || function (thisArg, _arguments, generator) {\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n    function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n    function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n    function fulfill(value) { resume(\"next\", value); }\n    function reject(value) { resume(\"throw\", value); }\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.FalStream = void 0;\nexports.stream = stream;\nconst eventsource_parser_1 = __webpack_require__(/*! eventsource-parser */ \"(rsc)/./node_modules/eventsource-parser/dist/index.cjs\");\nconst auth_1 = __webpack_require__(/*! ./auth */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/auth.js\");\nconst config_1 = __webpack_require__(/*! ./config */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/config.js\");\nconst function_1 = __webpack_require__(/*! ./function */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/function.js\");\nconst request_1 = __webpack_require__(/*! ./request */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/request.js\");\nconst response_1 = __webpack_require__(/*! ./response */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/response.js\");\nconst storage_1 = __webpack_require__(/*! ./storage */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/storage.js\");\nconst CONTENT_TYPE_EVENT_STREAM = \"text/event-stream\";\nconst EVENT_STREAM_TIMEOUT = 15 * 1000;\n/**\n * The class representing a streaming response. With t\n */\nclass FalStream {\n    constructor(endpointId, options) {\n        var _a;\n        // support for event listeners\n        this.listeners = new Map();\n        this.buffer = [];\n        // local state\n        this.currentData = undefined;\n        this.lastEventTimestamp = 0;\n        this.streamClosed = false;\n        this.abortController = new AbortController();\n        this.start = () => __awaiter(this, void 0, void 0, function* () {\n            var _a, _b;\n            const { endpointId, options } = this;\n            const { input, method = \"post\", connectionMode = \"server\" } = options;\n            try {\n                if (connectionMode === \"client\") {\n                    // if we are in the browser, we need to get a temporary token\n                    // to authenticate the request\n                    const token = yield (0, auth_1.getTemporaryAuthToken)(endpointId);\n                    const { fetch } = (0, config_1.getConfig)();\n                    const parsedUrl = new URL(this.url);\n                    parsedUrl.searchParams.set(\"fal_jwt_token\", token);\n                    const response = yield fetch(parsedUrl.toString(), {\n                        method: method.toUpperCase(),\n                        headers: {\n                            accept: (_a = options.accept) !== null && _a !== void 0 ? _a : CONTENT_TYPE_EVENT_STREAM,\n                            \"content-type\": \"application/json\",\n                        },\n                        body: input && method !== \"get\" ? JSON.stringify(input) : undefined,\n                        signal: this.abortController.signal,\n                    });\n                    return yield this.handleResponse(response);\n                }\n                return yield (0, request_1.dispatchRequest)(method.toUpperCase(), this.url, input, {\n                    headers: {\n                        accept: (_b = options.accept) !== null && _b !== void 0 ? _b : CONTENT_TYPE_EVENT_STREAM,\n                    },\n                    responseHandler: this.handleResponse,\n                    signal: this.abortController.signal,\n                });\n            }\n            catch (error) {\n                this.handleError(error);\n            }\n        });\n        this.handleResponse = (response) => __awaiter(this, void 0, void 0, function* () {\n            var _a;\n            if (!response.ok) {\n                try {\n                    // we know the response failed, call the response handler\n                    // so the exception gets converted to ApiError correctly\n                    yield (0, response_1.defaultResponseHandler)(response);\n                }\n                catch (error) {\n                    this.emit(\"error\", error);\n                }\n                return;\n            }\n            const body = response.body;\n            if (!body) {\n                this.emit(\"error\", new response_1.ApiError({\n                    message: \"Response body is empty.\",\n                    status: 400,\n                    body: undefined,\n                }));\n                return;\n            }\n            const isEventStream = response.headers\n                .get(\"content-type\")\n                .startsWith(CONTENT_TYPE_EVENT_STREAM);\n            // any response that is not a text/event-stream will be handled as a binary stream\n            if (!isEventStream) {\n                const reader = body.getReader();\n                const emitRawChunk = () => {\n                    reader.read().then(({ done, value }) => {\n                        if (done) {\n                            this.emit(\"done\", this.currentData);\n                            return;\n                        }\n                        this.currentData = value;\n                        this.emit(\"data\", value);\n                        emitRawChunk();\n                    });\n                };\n                emitRawChunk();\n                return;\n            }\n            const decoder = new TextDecoder(\"utf-8\");\n            const reader = response.body.getReader();\n            const parser = (0, eventsource_parser_1.createParser)((event) => {\n                if (event.type === \"event\") {\n                    const data = event.data;\n                    try {\n                        const parsedData = JSON.parse(data);\n                        this.buffer.push(parsedData);\n                        this.currentData = parsedData;\n                        this.emit(\"data\", parsedData);\n                        // also emit 'message'for backwards compatibility\n                        this.emit(\"message\", parsedData);\n                    }\n                    catch (e) {\n                        this.emit(\"error\", e);\n                    }\n                }\n            });\n            const timeout = (_a = this.options.timeout) !== null && _a !== void 0 ? _a : EVENT_STREAM_TIMEOUT;\n            const readPartialResponse = () => __awaiter(this, void 0, void 0, function* () {\n                const { value, done } = yield reader.read();\n                this.lastEventTimestamp = Date.now();\n                parser.feed(decoder.decode(value));\n                if (Date.now() - this.lastEventTimestamp > timeout) {\n                    this.emit(\"error\", new response_1.ApiError({\n                        message: `Event stream timed out after ${(timeout / 1000).toFixed(0)} seconds with no messages.`,\n                        status: 408,\n                    }));\n                }\n                if (!done) {\n                    readPartialResponse().catch(this.handleError);\n                }\n                else {\n                    this.emit(\"done\", this.currentData);\n                }\n            });\n            readPartialResponse().catch(this.handleError);\n            return;\n        });\n        this.handleError = (error) => {\n            var _a;\n            const apiError = error instanceof response_1.ApiError\n                ? error\n                : new response_1.ApiError({\n                    message: (_a = error.message) !== null && _a !== void 0 ? _a : \"An unknown error occurred\",\n                    status: 500,\n                });\n            this.emit(\"error\", apiError);\n            return;\n        };\n        this.on = (type, listener) => {\n            var _a;\n            if (!this.listeners.has(type)) {\n                this.listeners.set(type, []);\n            }\n            (_a = this.listeners.get(type)) === null || _a === void 0 ? void 0 : _a.push(listener);\n        };\n        this.emit = (type, event) => {\n            const listeners = this.listeners.get(type) || [];\n            for (const listener of listeners) {\n                listener(event);\n            }\n        };\n        /**\n         * Gets a reference to the `Promise` that indicates whether the streaming\n         * is done or not. Developers should always call this in their apps to ensure\n         * the request is over.\n         *\n         * An alternative to this, is to use `on('done')` in case your application\n         * architecture works best with event listeners.\n         *\n         * @returns the promise that resolves when the request is done.\n         */\n        this.done = () => __awaiter(this, void 0, void 0, function* () { return this.donePromise; });\n        /**\n         * Aborts the streaming request.\n         */\n        this.abort = () => {\n            this.abortController.abort();\n        };\n        this.endpointId = endpointId;\n        this.url =\n            (_a = options.url) !== null && _a !== void 0 ? _a : (0, function_1.buildUrl)(endpointId, {\n                path: \"/stream\",\n                query: options.queryParams,\n            });\n        this.options = options;\n        this.donePromise = new Promise((resolve, reject) => {\n            if (this.streamClosed) {\n                reject(new response_1.ApiError({\n                    message: \"Streaming connection is already closed.\",\n                    status: 400,\n                    body: undefined,\n                }));\n            }\n            this.on(\"done\", (data) => {\n                this.streamClosed = true;\n                resolve(data);\n            });\n            this.on(\"error\", (error) => {\n                this.streamClosed = true;\n                reject(error);\n            });\n        });\n        this.start().catch(this.handleError);\n    }\n    [Symbol.asyncIterator]() {\n        return __asyncGenerator(this, arguments, function* _a() {\n            let running = true;\n            const stopAsyncIterator = () => (running = false);\n            this.on(\"error\", stopAsyncIterator);\n            this.on(\"done\", stopAsyncIterator);\n            while (running) {\n                const data = this.buffer.shift();\n                if (data) {\n                    yield yield __await(data);\n                }\n                // the short timeout ensures the while loop doesn't block other\n                // frames getting executed concurrently\n                yield __await(new Promise((resolve) => setTimeout(resolve, 16)));\n            }\n        });\n    }\n}\nexports.FalStream = FalStream;\n/**\n * Calls a fal app that supports streaming and provides a streaming-capable\n * object as a result, that can be used to get partial results through either\n * `AsyncIterator` or through an event listener.\n *\n * @param endpointId the endpoint id, e.g. `fal-ai/llavav15-13b`.\n * @param options the request options, including the input payload.\n * @returns the `FalStream` instance.\n */\nfunction stream(endpointId, options) {\n    return __awaiter(this, void 0, void 0, function* () {\n        const input = options.input && options.autoUpload !== false\n            ? yield storage_1.storageImpl.transformInput(options.input)\n            : options.input;\n        return new FalStream(endpointId, Object.assign(Object.assign({}, options), { input: input }));\n    });\n}\n//# sourceMappingURL=streaming.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@fal-ai/serverless-client/src/streaming.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@fal-ai/serverless-client/src/utils.js":
/*!*************************************************************!*\
  !*** ./node_modules/@fal-ai/serverless-client/src/utils.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ensureAppIdFormat = ensureAppIdFormat;\nexports.parseAppId = parseAppId;\nexports.isValidUrl = isValidUrl;\nexports.throttle = throttle;\nexports.isReact = isReact;\nexports.isPlainObject = isPlainObject;\nfunction ensureAppIdFormat(id) {\n    const parts = id.split(\"/\");\n    if (parts.length > 1) {\n        return id;\n    }\n    const [, appOwner, appId] = /^([0-9]+)-([a-zA-Z0-9-]+)$/.exec(id) || [];\n    if (appOwner && appId) {\n        return `${appOwner}/${appId}`;\n    }\n    throw new Error(`Invalid app id: ${id}. Must be in the format <appOwner>/<appId>`);\n}\nconst APP_NAMESPACES = [\"workflows\", \"comfy\"];\nfunction parseAppId(id) {\n    const normalizedId = ensureAppIdFormat(id);\n    const parts = normalizedId.split(\"/\");\n    if (APP_NAMESPACES.includes(parts[0])) {\n        return {\n            owner: parts[1],\n            alias: parts[2],\n            path: parts.slice(3).join(\"/\") || undefined,\n            namespace: parts[0],\n        };\n    }\n    return {\n        owner: parts[0],\n        alias: parts[1],\n        path: parts.slice(2).join(\"/\") || undefined,\n    };\n}\nfunction isValidUrl(url) {\n    try {\n        const { host } = new URL(url);\n        return /(fal\\.(ai|run))$/.test(host);\n    }\n    catch (_) {\n        return false;\n    }\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction throttle(func, limit, leading = false) {\n    let lastFunc;\n    let lastRan;\n    return (...args) => {\n        if (!lastRan && leading) {\n            func(...args);\n            lastRan = Date.now();\n        }\n        else {\n            if (lastFunc) {\n                clearTimeout(lastFunc);\n            }\n            lastFunc = setTimeout(() => {\n                if (Date.now() - lastRan >= limit) {\n                    func(...args);\n                    lastRan = Date.now();\n                }\n            }, limit - (Date.now() - lastRan));\n        }\n    };\n}\nlet isRunningInReact;\n/**\n * Not really the most optimal way to detect if we're running in React,\n * but the idea here is that we can support multiple rendering engines\n * (starting with React), with all their peculiarities, without having\n * to add a dependency or creating custom integrations (e.g. custom hooks).\n *\n * Yes, a bit of magic to make things works out-of-the-box.\n * @returns `true` if running in React, `false` otherwise.\n */\nfunction isReact() {\n    if (isRunningInReact === undefined) {\n        const stack = new Error().stack;\n        isRunningInReact =\n            !!stack &&\n                (stack.includes(\"node_modules/react-dom/\") ||\n                    stack.includes(\"node_modules/next/\"));\n    }\n    return isRunningInReact;\n}\n/**\n * Check if a value is a plain object.\n * @param value - The value to check.\n * @returns `true` if the value is a plain object, `false` otherwise.\n */\nfunction isPlainObject(value) {\n    return !!value && Object.getPrototypeOf(value) === Object.prototype;\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@fal-ai/serverless-client/src/utils.js\n");

/***/ })

};
;