/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/chat/page";
exports.ids = ["app/chat/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fchat%2Fpage&page=%2Fchat%2Fpage&appPaths=%2Fchat%2Fpage&pagePath=private-next-app-dir%2Fchat%2Fpage.tsx&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fchat%2Fpage&page=%2Fchat%2Fpage&appPaths=%2Fchat%2Fpage&pagePath=private-next-app-dir%2Fchat%2Fpage.tsx&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/chat/page.tsx */ \"(rsc)/./src/app/chat/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'chat',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/chat/page\",\n        pathname: \"/chat\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fchat%2Fpage&page=%2Fchat%2Fpage&appPaths=%2Fchat%2Fpage&pagePath=private-next-app-dir%2Fchat%2Fpage.tsx&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp%2Fchat%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp%2Fchat%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/chat/page.tsx */ \"(rsc)/./src/app/chat/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbGl1cGVuZyUyRmN1cnNvciUyRiVFOCVCRiU5QiVFOSU5OCVCNiUyRkZsdXglRTUlQUYlQjklRTglQUYlOUQlRTUlQkMlOEYlRTUlQkElOTQlRTclOTQlQTglMkZmbHV4LWNoYXQtYXBwJTJGc3JjJTJGYXBwJTJGY2hhdCUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwSkFBMkciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9saXVwZW5nL2N1cnNvci/ov5vpmLYvRmx1eOWvueivneW8j+W6lOeUqC9mbHV4LWNoYXQtYXBwL3NyYy9hcHAvY2hhdC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp%2Fchat%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/chat/page.tsx":
/*!*******************************!*\
  !*** ./src/app/chat/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvbGl1cGVuZy9jdXJzb3Iv6L+b6Zi2L0ZsdXjlr7nor53lvI/lupTnlKgvZmx1eC1jaGF0LWFwcC9zcmMvYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNzE5Y2IwZmMzZjYzXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"AI图像生成助手\",\n    description: \"使用AI优化提示词，生成高质量图像\",\n    icons: {\n        icon: '/favicon.svg'\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/layout.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/layout.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUlNQTtBQUtBQztBQVBpQjtBQVloQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLE9BQU87UUFDTEMsTUFBTTtJQUNSO0FBQ0YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUdaLDJMQUFrQixDQUFDLENBQUMsRUFBRUMsZ01BQWtCLENBQUMsWUFBWSxDQUFDO3NCQUVuRU87Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIi9Vc2Vycy9saXVwZW5nL2N1cnNvci/ov5vpmLYvRmx1eOWvueivneW8j+W6lOeUqC9mbHV4LWNoYXQtYXBwL3NyYy9hcHAvbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIjtcbmltcG9ydCB7IEdlaXN0LCBHZWlzdF9Nb25vIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcblxuY29uc3QgZ2Vpc3RTYW5zID0gR2Vpc3Qoe1xuICB2YXJpYWJsZTogXCItLWZvbnQtZ2Vpc3Qtc2Fuc1wiLFxuICBzdWJzZXRzOiBbXCJsYXRpblwiXSxcbn0pO1xuXG5jb25zdCBnZWlzdE1vbm8gPSBHZWlzdF9Nb25vKHtcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LW1vbm9cIixcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG59KTtcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6IFwiQUnlm77lg4/nlJ/miJDliqnmiYtcIixcbiAgZGVzY3JpcHRpb246IFwi5L2/55SoQUnkvJjljJbmj5DnpLror43vvIznlJ/miJDpq5jotKjph4/lm77lg49cIixcbiAgaWNvbnM6IHtcbiAgICBpY29uOiAnL2Zhdmljb24uc3ZnJyxcbiAgfSxcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cInpoLUNOXCI+XG4gICAgICA8Ym9keVxuICAgICAgICBjbGFzc05hbWU9e2Ake2dlaXN0U2Fucy52YXJpYWJsZX0gJHtnZWlzdE1vbm8udmFyaWFibGV9IGFudGlhbGlhc2VkYH1cbiAgICAgID5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJnZWlzdFNhbnMiLCJnZWlzdE1vbm8iLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJpY29ucyIsImljb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiLCJ2YXJpYWJsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp%2Fchat%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp%2Fchat%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/chat/page.tsx */ \"(ssr)/./src/app/chat/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbGl1cGVuZyUyRmN1cnNvciUyRiVFOCVCRiU5QiVFOSU5OCVCNiUyRkZsdXglRTUlQUYlQjklRTglQUYlOUQlRTUlQkMlOEYlRTUlQkElOTQlRTclOTQlQTglMkZmbHV4LWNoYXQtYXBwJTJGc3JjJTJGYXBwJTJGY2hhdCUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwSkFBMkciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9saXVwZW5nL2N1cnNvci/ov5vpmLYvRmx1eOWvueivneW8j+W6lOeUqC9mbHV4LWNoYXQtYXBwL3NyYy9hcHAvY2hhdC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp%2Fchat%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/chat/page.tsx":
/*!*******************************!*\
  !*** ./src/app/chat/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Chat_ImageDisplay__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Chat/ImageDisplay */ \"(ssr)/./src/components/Chat/ImageDisplay.tsx\");\n/* harmony import */ var _components_Chat_ImageGenerationPanel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Chat/ImageGenerationPanel */ \"(ssr)/./src/components/Chat/ImageGenerationPanel.tsx\");\n/* harmony import */ var _components_Chat_ImageUpload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Chat/ImageUpload */ \"(ssr)/./src/components/Chat/ImageUpload.tsx\");\n/* harmony import */ var _components_Chat_ImageEditResult__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Chat/ImageEditResult */ \"(ssr)/./src/components/Chat/ImageEditResult.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction ChatPage() {\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGeneratingImage, setIsGeneratingImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditingImage, setIsEditingImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [optimizedPrompt, setOptimizedPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [uploadedImages, setUploadedImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showImageUpload, setShowImageUpload] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const sendMessage = async ()=>{\n        if (!message.trim() && uploadedImages.length === 0) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            role: 'user',\n            content: message || '',\n            images: uploadedImages.length > 0 ? uploadedImages.map((img)=>({\n                    url: img.url,\n                    width: 400,\n                    height: 300,\n                    content_type: img.type\n                })) : undefined,\n            metadata: uploadedImages.length > 0 ? {\n                uploaded_images: uploadedImages,\n                image_count: uploadedImages.length\n            } : undefined\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setMessage('');\n        setIsLoading(true);\n        try {\n            const response = await fetch('/api/chat', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: userMessage.content,\n                    conversationHistory: messages.map((m)=>m.content)\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                const assistantMessage = {\n                    id: (Date.now() + 1).toString(),\n                    role: 'assistant',\n                    content: data.response\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        assistantMessage\n                    ]);\n                // 清空上传的图片，避免重复发送\n                if (uploadedImages.length > 0) {\n                    setUploadedImages([]);\n                }\n                // 检查是否需要自动生成提示词优化\n                if (!data.needsClarification && data.extractedRequirement) {\n                    await generateOptimizedPrompt();\n                }\n            } else {\n                setMessages((prev)=>[\n                        ...prev,\n                        {\n                            id: (Date.now() + 1).toString(),\n                            role: 'assistant',\n                            content: '抱歉，发生了错误。请稍后重试。'\n                        }\n                    ]);\n            }\n        } catch (error) {\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        id: (Date.now() + 1).toString(),\n                        role: 'assistant',\n                        content: '网络错误，请检查连接。'\n                    }\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // 生成优化提示词\n    const generateOptimizedPrompt = async ()=>{\n        try {\n            const conversationHistory = messages.map((m)=>m.content);\n            const requirement = conversationHistory.join(' ');\n            const response = await fetch('/api/optimize-prompt', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    requirement,\n                    conversationHistory,\n                    useAI: true\n                })\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.optimization?.optimizedPrompt) {\n                    setOptimizedPrompt(data.optimization.optimizedPrompt);\n                }\n            }\n        } catch (error) {\n            console.error('Failed to generate optimized prompt:', error);\n        }\n    };\n    // 处理图像生成\n    const handleImageGeneration = async (config, prompt)=>{\n        setIsGeneratingImage(true);\n        try {\n            const response = await fetch('/api/generate-image', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    prompt,\n                    ...config\n                })\n            });\n            const data = await response.json();\n            if (response.ok && data.success) {\n                // 添加图像消息\n                const imageMessage = {\n                    id: Date.now().toString(),\n                    role: 'assistant',\n                    content: `已为您生成图像！使用了 ${data.metadata.model_used} 模型。`,\n                    images: data.images,\n                    metadata: data.metadata\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        imageMessage\n                    ]);\n            } else {\n                // 处理错误\n                const errorMessage = {\n                    id: Date.now().toString(),\n                    role: 'assistant',\n                    content: data.demo_mode ? '图像生成功能需要配置 FAL API 密钥。请查看配置说明。' : `图像生成失败: ${data.error || '未知错误'}`\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        errorMessage\n                    ]);\n            }\n        } catch (error) {\n            const errorMessage = {\n                id: Date.now().toString(),\n                role: 'assistant',\n                content: '图像生成请求失败，请检查网络连接。'\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsGeneratingImage(false);\n        }\n    };\n    // 处理图片上传\n    const handleImageUpload = (images)=>{\n        setUploadedImages(images);\n    };\n    // 处理图片编辑\n    const handleImageEdit = async (image, instruction)=>{\n        setIsEditingImage(true);\n        try {\n            const formData = new FormData();\n            formData.append('image', image.file);\n            formData.append('instruction', instruction);\n            formData.append('model', 'instruct-pix2pix');\n            const response = await fetch('/api/edit-image', {\n                method: 'POST',\n                body: formData\n            });\n            const data = await response.json();\n            if (response.ok && data.success) {\n                // 添加编辑结果消息\n                const editMessage = {\n                    id: Date.now().toString(),\n                    role: 'assistant',\n                    content: `已完成图片编辑！编辑指令: \"${instruction}\"`,\n                    editResult: {\n                        originalImage: {\n                            url: image.url,\n                            name: image.name\n                        },\n                        editedImage: data.edited_image,\n                        metadata: data.metadata\n                    }\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        editMessage\n                    ]);\n            } else {\n                // 处理错误\n                const errorMessage = {\n                    id: Date.now().toString(),\n                    role: 'assistant',\n                    content: data.demo_mode ? '图片编辑功能需要配置 FAL API 密钥。请查看配置说明。' : `图片编辑失败: ${data.error || '未知错误'}`\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        errorMessage\n                    ]);\n            }\n        } catch (error) {\n            const errorMessage = {\n                id: Date.now().toString(),\n                role: 'assistant',\n                content: '图片编辑请求失败，请检查网络连接。'\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsEditingImage(false);\n        }\n    };\n    // 重新编辑图片\n    const handleReEdit = async (editResult, newInstruction)=>{\n        // 创建临时的UploadedImage对象\n        const tempImage = {\n            id: 'temp-' + Date.now(),\n            file: new File([], editResult.originalImage.name),\n            url: editResult.originalImage.url,\n            name: editResult.originalImage.name,\n            size: 0,\n            type: 'image/jpeg'\n        };\n        await handleImageEdit(tempImage, newInstruction);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-xl font-semibold text-gray-900\",\n                        children: \"AI图像生成助手\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: \"让我帮您优化图像生成提示词\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4\",\n                children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center text-gray-500 mt-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg font-medium\",\n                            children: \"欢迎使用AI图像生成助手\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm mt-2\",\n                            children: \"请描述您想要生成的图像，我会帮您优化提示词\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                    lineNumber: 281,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 max-w-4xl mx-auto\",\n                    children: [\n                        messages.map((msg)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `max-w-[80%] ${msg.role === 'user' ? '' : 'w-full'}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `rounded-lg px-4 py-2 ${msg.role === 'user' ? 'bg-blue-500 text-white' : 'bg-white text-gray-900 border border-gray-200'}`,\n                                            children: [\n                                                msg.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"whitespace-pre-wrap\",\n                                                    children: msg.content\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 37\n                                                }, this),\n                                                msg.role === 'user' && msg.metadata?.uploaded_images && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 grid grid-cols-2 gap-2\",\n                                                    children: msg.metadata.uploaded_images.map((img, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: img.url,\n                                                                    alt: `上传的图片 ${idx + 1}`,\n                                                                    className: \"w-full h-auto rounded-lg border border-white/20\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                                    lineNumber: 302,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            setSelectedImage(img);\n                                                                            setShowImageEditor(true);\n                                                                        },\n                                                                        className: \"px-2 py-1 bg-white/20 text-white text-xs rounded hover:bg-white/30 transition-colors\",\n                                                                        children: \"编辑此图片\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                                        lineNumber: 308,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                                    lineNumber: 307,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, idx, true, {\n                                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                    lineNumber: 299,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 19\n                                        }, this),\n                                        msg.images && msg.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Chat_ImageDisplay__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                images: msg.images,\n                                                metadata: msg.metadata,\n                                                onRegenerate: (seed)=>{\n                                                    if (msg.metadata?.prompt_used) {\n                                                        handleImageGeneration({\n                                                            model: msg.metadata.parameters?.model || 'flux-schnell',\n                                                            size: msg.metadata.parameters?.size || 'landscape',\n                                                            num_images: 1,\n                                                            seed\n                                                        }, msg.metadata.prompt_used);\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 21\n                                        }, this),\n                                        msg.editResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Chat_ImageEditResult__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                originalImage: msg.editResult.originalImage,\n                                                editedImage: msg.editResult.editedImage,\n                                                metadata: msg.editResult.metadata,\n                                                onReEdit: (newInstruction)=>{\n                                                    handleReEdit(msg.editResult, newInstruction);\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 17\n                                }, this)\n                            }, msg.id, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 15\n                            }, this)),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white border border-gray-200 rounded-lg px-4 py-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-500\",\n                                            children: \"AI正在思考...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                    lineNumber: 286,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                lineNumber: 279,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-t border-gray-200 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto space-y-3\",\n                    children: [\n                        showImageUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border border-gray-200 rounded-lg p-4 bg-gray-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: \"上传图片进行编辑\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowImageUpload(false),\n                                            className: \"text-gray-500 hover:text-gray-700\",\n                                            children: \"✕\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Chat_ImageUpload__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    onImageUpload: handleImageUpload,\n                                    onImageEdit: handleImageEdit,\n                                    maxFiles: 3,\n                                    maxSize: 10\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 13\n                        }, this),\n                        optimizedPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Chat_ImageGenerationPanel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                onGenerate: handleImageGeneration,\n                                isGenerating: isGeneratingImage,\n                                optimizedPrompt: optimizedPrompt\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 403,\n                            columnNumber: 13\n                        }, this),\n                        uploadedImages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border border-gray-200 rounded-lg p-3 bg-blue-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: [\n                                                \"准备发送的图片 (\",\n                                                uploadedImages.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setUploadedImages([]),\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"清空\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-2\",\n                                    children: uploadedImages.map((img, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: img.url,\n                                                    alt: `预览 ${idx + 1}`,\n                                                    className: \"w-full h-20 object-cover rounded border\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        setUploadedImages((prev)=>prev.filter((_, i)=>i !== idx));\n                                                    },\n                                                    className: \"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full text-xs flex items-center justify-center hover:bg-red-600\",\n                                                    children: \"\\xd7\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, idx, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowImageUpload(!showImageUpload),\n                                    disabled: isLoading || isGeneratingImage || isEditingImage,\n                                    className: \"px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center space-x-2\",\n                                    title: \"上传图片进行编辑\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"图片\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: message,\n                                    onChange: (e)=>setMessage(e.target.value),\n                                    onKeyPress: (e)=>e.key === 'Enter' && sendMessage(),\n                                    placeholder: \"描述您想要生成的图像，或上传图片进行编辑...\",\n                                    disabled: isLoading || isGeneratingImage || isEditingImage,\n                                    className: \"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 462,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: sendMessage,\n                                    disabled: !message.trim() && uploadedImages.length === 0 || isLoading || isGeneratingImage || isEditingImage,\n                                    className: \"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors\",\n                                    children: isLoading ? '发送中...' : '发送'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 449,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500 w-full text-center\",\n                                            children: \"图像生成示例:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 15\n                                        }, this),\n                                        [\n                                            '可爱的小猫',\n                                            '未来城市',\n                                            '油画风景',\n                                            '卡通人物',\n                                            '抽象艺术'\n                                        ].map((prompt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setMessage(prompt),\n                                                disabled: isLoading || isGeneratingImage || isEditingImage,\n                                                className: \"px-3 py-1 text-sm bg-blue-50 text-blue-600 rounded-full hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                children: prompt\n                                            }, prompt, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 482,\n                                    columnNumber: 13\n                                }, this),\n                                uploadedImages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500 w-full text-center\",\n                                            children: \"图片编辑示例:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 17\n                                        }, this),\n                                        [\n                                            '改为黑白风格',\n                                            '添加蓝天背景',\n                                            '移除背景',\n                                            '调整为夜晚场景',\n                                            '添加彩色滤镜'\n                                        ].map((prompt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    if (uploadedImages.length > 0) {\n                                                        handleImageEdit(uploadedImages[0], prompt);\n                                                    }\n                                                },\n                                                disabled: isLoading || isGeneratingImage || isEditingImage,\n                                                className: \"px-3 py-1 text-sm bg-green-50 text-green-600 rounded-full hover:bg-green-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                children: prompt\n                                            }, prompt, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 19\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                lineNumber: 378,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n        lineNumber: 271,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/chat/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Chat/ImageDisplay.tsx":
/*!**********************************************!*\
  !*** ./src/components/Chat/ImageDisplay.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ImageDisplay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,CpuChipIcon,HeartIcon,MagnifyingGlassIcon,PhotoIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,CpuChipIcon,HeartIcon,MagnifyingGlassIcon,PhotoIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,CpuChipIcon,HeartIcon,MagnifyingGlassIcon,PhotoIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,CpuChipIcon,HeartIcon,MagnifyingGlassIcon,PhotoIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,CpuChipIcon,HeartIcon,MagnifyingGlassIcon,PhotoIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,CpuChipIcon,HeartIcon,MagnifyingGlassIcon,PhotoIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,CpuChipIcon,HeartIcon,MagnifyingGlassIcon,PhotoIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ShareIcon.js\");\n/* harmony import */ var _barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/HeartIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ImageDisplay({ images, metadata, isLoading = false, onRegenerate, onVariation }) {\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [likedImages, setLikedImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // 下载图像\n    const downloadImage = async (imageUrl, index)=>{\n        try {\n            const response = await fetch(imageUrl);\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = `generated-image-${index + 1}.jpg`;\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n        } catch (error) {\n            console.error('Download failed:', error);\n        }\n    };\n    // 分享图像\n    const shareImage = async (imageUrl)=>{\n        if (navigator.share) {\n            try {\n                await navigator.share({\n                    title: 'AI生成的图像',\n                    text: metadata.prompt_used,\n                    url: imageUrl\n                });\n            } catch (error) {\n                console.error('Share failed:', error);\n            }\n        } else {\n            // 回退到复制链接\n            navigator.clipboard.writeText(imageUrl);\n            alert('图像链接已复制到剪贴板');\n        }\n    };\n    // 切换喜欢状态\n    const toggleLike = (index)=>{\n        const newLiked = new Set(likedImages);\n        if (newLiked.has(index)) {\n            newLiked.delete(index);\n        } else {\n            newLiked.add(index);\n        }\n        setLikedImages(newLiked);\n    };\n    // 格式化生成时间\n    const formatGenerationTime = (time)=>{\n        if (!time) return '未知';\n        if (time < 1000) return `${time}ms`;\n        return `${(time / 1000).toFixed(1)}s`;\n    };\n    // 获取模型显示名称\n    const getModelDisplayName = (model)=>{\n        const modelNames = {\n            'flux-schnell': 'FLUX Schnell',\n            'flux-dev': 'FLUX Dev',\n            'flux-pro': 'FLUX Pro',\n            'sd-xl': 'Stable Diffusion XL',\n            'sd-3': 'Stable Diffusion 3'\n        };\n        return modelNames[model] || model;\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg border border-gray-200 p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"正在生成图像...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: [\n                            \"使用 \",\n                            getModelDisplayName(metadata.model_used),\n                            \" 模型生成中，请稍候\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                lineNumber: 119,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n            lineNumber: 118,\n            columnNumber: 7\n        }, this);\n    }\n    if (!images || images.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-50 rounded-lg border border-gray-200 p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"暂无图像\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: \"生成的图像将在这里显示\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                lineNumber: 133,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n            lineNumber: 132,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg border border-gray-200 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `grid gap-4 ${images.length === 1 ? 'grid-cols-1' : images.length === 2 ? 'grid-cols-2' : 'grid-cols-2 md:grid-cols-3'}`,\n                    children: images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative group cursor-pointer\",\n                            onClick: ()=>setSelectedImage(index),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: image.url,\n                                    alt: `Generated image ${index + 1}`,\n                                    className: \"w-full h-auto rounded-lg shadow-sm hover:shadow-md transition-shadow\",\n                                    style: {\n                                        aspectRatio: `${image.width}/${image.height}`\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    setIsFullscreen(true);\n                                                },\n                                                className: \"p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors\",\n                                                title: \"全屏查看\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"w-4 h-4 text-gray-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    downloadImage(image.url, index);\n                                                },\n                                                className: \"p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors\",\n                                                title: \"下载图像\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-4 h-4 text-gray-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    toggleLike(index);\n                                                },\n                                                className: \"p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors\",\n                                                title: \"喜欢\",\n                                                children: likedImages.has(index) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-4 h-4 text-red-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-4 h-4 text-gray-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this),\n                                selectedImage === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 border-2 border-blue-500 rounded-lg pointer-events-none\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200 p-4 bg-gray-50\",\n                children: [\n                    metadata.demo_notice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-3 p-2 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-yellow-800\",\n                            children: [\n                                \"\\uD83C\\uDFAD \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"演示模式\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 18\n                                }, this),\n                                \": 这是示例图像。配置 FAL API 密钥以生成真实图像。\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/FAL_SETUP.md\",\n                                    className: \"text-yellow-900 underline ml-1\",\n                                    children: \"查看配置指南\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: getModelDisplayName(metadata.model_used)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 15\n                                            }, this),\n                                            metadata.demo_notice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs bg-yellow-100 text-yellow-800 px-1 rounded\",\n                                                children: \"演示\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this),\n                                    metadata.generation_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: formatGenerationTime(metadata.generation_time)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this),\n                                    metadata.seed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs\",\n                                        children: [\n                                            \"Seed: \",\n                                            metadata.seed\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>shareImage(images[selectedImage].url),\n                                        className: \"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"分享\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this),\n                                    onRegenerate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>onRegenerate(metadata.seed),\n                                        className: \"px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors\",\n                                        children: \"重新生成\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-gray-700\",\n                                children: \"提示词: \"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-600\",\n                                children: metadata.prompt_used\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, this),\n            isFullscreen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4\",\n                onClick: ()=>setIsFullscreen(false),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative max-w-full max-h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: images[selectedImage].url,\n                            alt: \"Fullscreen view\",\n                            className: \"max-w-full max-h-full object-contain\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsFullscreen(false),\n                            className: \"absolute top-4 right-4 text-white text-2xl hover:text-gray-300\",\n                            children: \"✕\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                lineNumber: 280,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Chat/ImageDisplay.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Chat/ImageEditResult.tsx":
/*!*************************************************!*\
  !*** ./src/components/Chat/ImageEditResult.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ImageEditResult)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowPathIcon_ArrowsRightLeftIcon_ClockIcon_CpuChipIcon_MagnifyingGlassIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowPathIcon,ArrowsRightLeftIcon,ClockIcon,CpuChipIcon,MagnifyingGlassIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowPathIcon_ArrowsRightLeftIcon_ClockIcon_CpuChipIcon_MagnifyingGlassIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowPathIcon,ArrowsRightLeftIcon,ClockIcon,CpuChipIcon,MagnifyingGlassIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowsRightLeftIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowPathIcon_ArrowsRightLeftIcon_ClockIcon_CpuChipIcon_MagnifyingGlassIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowPathIcon,ArrowsRightLeftIcon,ClockIcon,CpuChipIcon,MagnifyingGlassIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowPathIcon_ArrowsRightLeftIcon_ClockIcon_CpuChipIcon_MagnifyingGlassIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowPathIcon,ArrowsRightLeftIcon,ClockIcon,CpuChipIcon,MagnifyingGlassIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowPathIcon_ArrowsRightLeftIcon_ClockIcon_CpuChipIcon_MagnifyingGlassIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowPathIcon,ArrowsRightLeftIcon,ClockIcon,CpuChipIcon,MagnifyingGlassIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowPathIcon_ArrowsRightLeftIcon_ClockIcon_CpuChipIcon_MagnifyingGlassIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowPathIcon,ArrowsRightLeftIcon,ClockIcon,CpuChipIcon,MagnifyingGlassIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ShareIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ArrowPathIcon_ArrowsRightLeftIcon_ClockIcon_CpuChipIcon_MagnifyingGlassIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ArrowPathIcon,ArrowsRightLeftIcon,ClockIcon,CpuChipIcon,MagnifyingGlassIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ImageEditResult({ originalImage, editedImage, metadata, onReEdit, onDownload }) {\n    const [showComparison, setShowComparison] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [fullscreenImage, setFullscreenImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('edited');\n    // 下载编辑后的图片\n    const downloadEditedImage = async ()=>{\n        try {\n            const response = await fetch(editedImage.url);\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = `edited_${metadata.original_filename}`;\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n            if (onDownload) onDownload();\n        } catch (error) {\n            console.error('Download failed:', error);\n        }\n    };\n    // 分享图片\n    const shareImage = async ()=>{\n        if (navigator.share) {\n            try {\n                await navigator.share({\n                    title: '编辑后的图片',\n                    text: `使用AI编辑: ${metadata.instruction_used}`,\n                    url: editedImage.url\n                });\n            } catch (error) {\n                console.error('Share failed:', error);\n            }\n        } else {\n            // 回退到复制链接\n            navigator.clipboard.writeText(editedImage.url);\n            alert('图片链接已复制到剪贴板');\n        }\n    };\n    // 格式化处理时间\n    const formatProcessingTime = (time)=>{\n        if (time < 1000) return `${time}ms`;\n        return `${(time / 1000).toFixed(1)}s`;\n    };\n    // 获取模型显示名称\n    const getModelDisplayName = (model)=>{\n        const modelNames = {\n            'instruct-pix2pix': 'InstructPix2Pix',\n            'flux-fill': 'FLUX Fill',\n            'flux-redux': 'FLUX Redux',\n            'controlnet-inpaint': 'ControlNet Inpaint'\n        };\n        return modelNames[model] || model;\n    };\n    // 全屏查看\n    const openFullscreen = (imageType)=>{\n        setFullscreenImage(imageType);\n        setIsFullscreen(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg border border-gray-200 overflow-hidden\",\n        children: [\n            metadata.demo_notice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-3 bg-yellow-50 border-b border-yellow-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-yellow-800\",\n                    children: [\n                        \"\\uD83C\\uDFAD \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"演示模式\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 16\n                        }, this),\n                        \": 这是示例结果。配置 FAL API 密钥以进行真实图片编辑。\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                lineNumber: 119,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: [\n                    showComparison ? // 对比视图\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"原图\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: originalImage.url,\n                                                alt: \"原图\",\n                                                className: \"w-full h-auto rounded-lg shadow-sm cursor-pointer\",\n                                                onClick: ()=>openFullscreen('original')\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>openFullscreen('original'),\n                                                    className: \"p-2 bg-white rounded-full shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowPathIcon_ArrowsRightLeftIcon_ClockIcon_CpuChipIcon_MagnifyingGlassIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-700\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-sm font-medium text-gray-700\",\n                                        children: \"编辑后\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: editedImage.url,\n                                                alt: \"编辑后\",\n                                                className: \"w-full h-auto rounded-lg shadow-sm cursor-pointer\",\n                                                onClick: ()=>openFullscreen('edited')\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>openFullscreen('edited'),\n                                                    className: \"p-2 bg-white rounded-full shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowPathIcon_ArrowsRightLeftIcon_ClockIcon_CpuChipIcon_MagnifyingGlassIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        className: \"w-4 h-4 text-gray-700\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, this) : // 单图视图\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: editedImage.url,\n                            alt: \"编辑结果\",\n                            className: \"max-w-full h-auto rounded-lg shadow-sm mx-auto cursor-pointer\",\n                            onClick: ()=>openFullscreen('edited')\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                        lineNumber: 175,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowComparison(!showComparison),\n                            className: \"flex items-center space-x-2 px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowPathIcon_ArrowsRightLeftIcon_ClockIcon_CpuChipIcon_MagnifyingGlassIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: showComparison ? '单图查看' : '对比查看'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200 p-4 bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                className: \"text-sm font-medium text-gray-700 mb-1\",\n                                children: \"编辑指令\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600 bg-white p-2 rounded border\",\n                                children: metadata.instruction_used\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowPathIcon_ArrowsRightLeftIcon_ClockIcon_CpuChipIcon_MagnifyingGlassIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: getModelDisplayName(metadata.model_used)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowPathIcon_ArrowsRightLeftIcon_ClockIcon_CpuChipIcon_MagnifyingGlassIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: formatProcessingTime(metadata.processing_time)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this),\n                                metadata.strength_used && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs\",\n                                    children: [\n                                        \"强度: \",\n                                        metadata.strength_used\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: downloadEditedImage,\n                                className: \"flex items-center space-x-1 px-3 py-2 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowPathIcon_ArrowsRightLeftIcon_ClockIcon_CpuChipIcon_MagnifyingGlassIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"下载\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: shareImage,\n                                className: \"flex items-center space-x-1 px-3 py-2 bg-gray-100 text-gray-700 text-sm rounded hover:bg-gray-200 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowPathIcon_ArrowsRightLeftIcon_ClockIcon_CpuChipIcon_MagnifyingGlassIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"分享\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this),\n                            onReEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    const newInstruction = prompt('请输入新的编辑指令:', metadata.instruction_used);\n                                    if (newInstruction && newInstruction.trim()) {\n                                        onReEdit(newInstruction.trim());\n                                    }\n                                },\n                                className: \"flex items-center space-x-1 px-3 py-2 bg-green-100 text-green-700 text-sm rounded hover:bg-green-200 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ArrowPathIcon_ArrowsRightLeftIcon_ClockIcon_CpuChipIcon_MagnifyingGlassIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"重新编辑\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, this),\n            isFullscreen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4\",\n                onClick: ()=>setIsFullscreen(false),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative max-w-full max-h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: fullscreenImage === 'original' ? originalImage.url : editedImage.url,\n                            alt: fullscreenImage === 'original' ? '原图' : '编辑后',\n                            className: \"max-w-full max-h-full object-contain\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-4 left-4 flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        setFullscreenImage('original');\n                                    },\n                                    className: `px-3 py-1 text-sm rounded ${fullscreenImage === 'original' ? 'bg-white text-black' : 'bg-black bg-opacity-50 text-white'}`,\n                                    children: \"原图\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        setFullscreenImage('edited');\n                                    },\n                                    className: `px-3 py-1 text-sm rounded ${fullscreenImage === 'edited' ? 'bg-white text-black' : 'bg-black bg-opacity-50 text-white'}`,\n                                    children: \"编辑后\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsFullscreen(false),\n                            className: \"absolute top-4 right-4 text-white text-2xl hover:text-gray-300\",\n                            children: \"✕\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n                lineNumber: 265,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageEditResult.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Chat/ImageEditResult.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Chat/ImageGenerationPanel.tsx":
/*!******************************************************!*\
  !*** ./src/components/Chat/ImageGenerationPanel.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ImageGenerationPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_CogIcon_PhotoIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,CogIcon,PhotoIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_CogIcon_PhotoIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,CogIcon,PhotoIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_CogIcon_PhotoIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,CogIcon,PhotoIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_CogIcon_PhotoIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,CogIcon,PhotoIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/AdjustmentsHorizontalIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ImageGenerationPanel({ onGenerate, isGenerating, optimizedPrompt }) {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        model: 'flux-schnell',\n        size: 'landscape',\n        num_images: 1,\n        guidance_scale: 7.5\n    });\n    const [availableModels, setAvailableModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [customPrompt, setCustomPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // 加载可用模型和配置\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageGenerationPanel.useEffect\": ()=>{\n            const loadConfig = {\n                \"ImageGenerationPanel.useEffect.loadConfig\": async ()=>{\n                    try {\n                        const response = await fetch('/api/generate-image');\n                        if (response.ok) {\n                            const data = await response.json();\n                            setAvailableModels(data);\n                        }\n                    } catch (error) {\n                        console.error('Failed to load image generation config:', error);\n                    }\n                }\n            }[\"ImageGenerationPanel.useEffect.loadConfig\"];\n            loadConfig();\n        }\n    }[\"ImageGenerationPanel.useEffect\"], []);\n    // 当有优化提示词时自动填充\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageGenerationPanel.useEffect\": ()=>{\n            if (optimizedPrompt && !customPrompt) {\n                setCustomPrompt(optimizedPrompt);\n            }\n        }\n    }[\"ImageGenerationPanel.useEffect\"], [\n        optimizedPrompt,\n        customPrompt\n    ]);\n    const handleGenerate = ()=>{\n        const promptToUse = customPrompt || optimizedPrompt || '';\n        if (!promptToUse.trim()) {\n            alert('请输入提示词或先优化对话中的提示词');\n            return;\n        }\n        onGenerate(config, promptToUse);\n        setIsOpen(false);\n    };\n    const getModelInfo = (modelKey)=>{\n        return availableModels.models?.[modelKey] || {\n            name: modelKey,\n            description: '未知模型',\n            speed: 'medium',\n            quality: 'good'\n        };\n    };\n    const getSpeedColor = (speed)=>{\n        switch(speed){\n            case 'fast':\n                return 'text-green-600 bg-green-50';\n            case 'medium':\n                return 'text-yellow-600 bg-yellow-50';\n            case 'slow':\n                return 'text-red-600 bg-red-50';\n            default:\n                return 'text-gray-600 bg-gray-50';\n        }\n    };\n    const getQualityColor = (quality)=>{\n        switch(quality){\n            case 'excellent':\n                return 'text-purple-600 bg-purple-50';\n            case 'high':\n                return 'text-blue-600 bg-blue-50';\n            case 'good':\n                return 'text-green-600 bg-green-50';\n            default:\n                return 'text-gray-600 bg-gray-50';\n        }\n    };\n    if (!isOpen) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [\n                optimizedPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>onGenerate(config, optimizedPrompt),\n                    disabled: isGenerating,\n                    className: \"flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors\",\n                    children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"生成中...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_CogIcon_PhotoIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"生成图像\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setIsOpen(true),\n                    className: \"flex items-center space-x-2 px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors\",\n                    title: \"图像生成设置\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_CogIcon_PhotoIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"设置\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 z-40 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_CogIcon_PhotoIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-6 h-6 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"图像生成设置\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsOpen(false),\n                                className: \"text-gray-500 hover:text-gray-700\",\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"提示词\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: customPrompt,\n                                    onChange: (e)=>setCustomPrompt(e.target.value),\n                                    placeholder: optimizedPrompt || \"输入图像描述...\",\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none\",\n                                    rows: 3\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this),\n                                optimizedPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCustomPrompt(optimizedPrompt),\n                                    className: \"mt-2 text-sm text-blue-600 hover:text-blue-800\",\n                                    children: \"使用优化后的提示词\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"生成模型\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                                    children: Object.entries(availableModels.models || {}).map(([key, model])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: ()=>setConfig({\n                                                    ...config,\n                                                    model: key\n                                                }),\n                                            className: `p-3 border rounded-lg cursor-pointer transition-colors ${config.model === key ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: model.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: `px-2 py-1 text-xs rounded ${getSpeedColor(model.speed)}`,\n                                                                    children: model.speed\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                                    lineNumber: 199,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: `px-2 py-1 text-xs rounded ${getQualityColor(model.quality)}`,\n                                                                    children: model.quality\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                                    lineNumber: 202,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: model.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, key, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"图像尺寸\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-2\",\n                                    children: Object.entries(availableModels.sizes || {}).map(([key, size])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setConfig({\n                                                    ...config,\n                                                    size: key\n                                                }),\n                                            className: `p-2 text-sm border rounded transition-colors ${config.size === key ? 'border-blue-500 bg-blue-50 text-blue-700' : 'border-gray-200 hover:border-gray-300'}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium\",\n                                                    children: key\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: size\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, key, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: [\n                                        \"生成数量: \",\n                                        config.num_images\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"1\",\n                                    max: \"4\",\n                                    value: config.num_images,\n                                    onChange: (e)=>setConfig({\n                                            ...config,\n                                            num_images: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between text-xs text-gray-500 mt-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"1张\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"4张\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                            className: \"border border-gray-200 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                    className: \"p-3 cursor-pointer flex items-center space-x-2 hover:bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_CogIcon_PhotoIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"高级设置\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-t border-gray-200 space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: [\n                                                        \"引导强度: \",\n                                                        config.guidance_scale\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"range\",\n                                                    min: \"1\",\n                                                    max: \"20\",\n                                                    step: \"0.5\",\n                                                    value: config.guidance_scale,\n                                                    onChange: (e)=>setConfig({\n                                                            ...config,\n                                                            guidance_scale: parseFloat(e.target.value)\n                                                        }),\n                                                    className: \"w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-xs text-gray-500 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"创意\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"精确\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"推理步数 (可选)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: \"1\",\n                                                    max: \"100\",\n                                                    value: config.num_inference_steps || '',\n                                                    onChange: (e)=>setConfig({\n                                                            ...config,\n                                                            num_inference_steps: e.target.value ? parseInt(e.target.value) : undefined\n                                                        }),\n                                                    placeholder: \"使用默认值\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"随机种子 (可选)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    value: config.seed || '',\n                                                    onChange: (e)=>setConfig({\n                                                            ...config,\n                                                            seed: e.target.value ? parseInt(e.target.value) : undefined\n                                                        }),\n                                                    placeholder: \"随机生成\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-t border-gray-200 flex justify-end space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsOpen(false),\n                            className: \"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors\",\n                            children: \"取消\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleGenerate,\n                            disabled: isGenerating || !customPrompt.trim(),\n                            className: \"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center space-x-2\",\n                            children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"生成中...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_CogIcon_PhotoIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"开始生成\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n            lineNumber: 139,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Chat/ImageGenerationPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Chat/ImageUpload.tsx":
/*!*********************************************!*\
  !*** ./src/components/Chat/ImageUpload.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ImageUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CloudArrowUpIcon_EyeIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CloudArrowUpIcon,EyeIcon,PencilIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CloudArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CloudArrowUpIcon_EyeIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CloudArrowUpIcon,EyeIcon,PencilIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CloudArrowUpIcon_EyeIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CloudArrowUpIcon,EyeIcon,PencilIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CloudArrowUpIcon_EyeIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CloudArrowUpIcon,EyeIcon,PencilIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ImageUpload({ onImageUpload, onImageEdit, maxFiles = 5, maxSize = 10, acceptedTypes = [\n    'image/jpeg',\n    'image/png',\n    'image/webp',\n    'image/gif'\n] }) {\n    const [uploadedImages, setUploadedImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingImage, setEditingImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editInstruction, setEditInstruction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 处理文件选择\n    const handleFileSelect = (files)=>{\n        if (!files) return;\n        const validFiles = Array.from(files).filter((file)=>{\n            // 检查文件类型\n            if (!acceptedTypes.includes(file.type)) {\n                alert(`不支持的文件类型: ${file.type}`);\n                return false;\n            }\n            // 检查文件大小\n            if (file.size > maxSize * 1024 * 1024) {\n                alert(`文件过大: ${file.name} (最大${maxSize}MB)`);\n                return false;\n            }\n            return true;\n        });\n        if (uploadedImages.length + validFiles.length > maxFiles) {\n            alert(`最多只能上传${maxFiles}张图片`);\n            return;\n        }\n        setIsUploading(true);\n        const newImages = validFiles.map((file)=>({\n                id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n                file,\n                url: URL.createObjectURL(file),\n                name: file.name,\n                size: file.size,\n                type: file.type\n            }));\n        const updatedImages = [\n            ...uploadedImages,\n            ...newImages\n        ];\n        setUploadedImages(updatedImages);\n        onImageUpload(updatedImages);\n        setIsUploading(false);\n    };\n    // 拖拽处理\n    const handleDragOver = (e)=>{\n        e.preventDefault();\n        setIsDragging(true);\n    };\n    const handleDragLeave = (e)=>{\n        e.preventDefault();\n        setIsDragging(false);\n    };\n    const handleDrop = (e)=>{\n        e.preventDefault();\n        setIsDragging(false);\n        handleFileSelect(e.dataTransfer.files);\n    };\n    // 删除图片\n    const removeImage = (imageId)=>{\n        const imageToRemove = uploadedImages.find((img)=>img.id === imageId);\n        if (imageToRemove) {\n            URL.revokeObjectURL(imageToRemove.url);\n        }\n        const updatedImages = uploadedImages.filter((img)=>img.id !== imageId);\n        setUploadedImages(updatedImages);\n        onImageUpload(updatedImages);\n    };\n    // 开始编辑图片\n    const startImageEdit = (image)=>{\n        setEditingImage(image);\n        setEditInstruction('');\n    };\n    // 提交编辑指令\n    const submitEdit = ()=>{\n        if (editingImage && editInstruction.trim()) {\n            onImageEdit(editingImage, editInstruction.trim());\n            setEditingImage(null);\n            setEditInstruction('');\n        }\n    };\n    // 格式化文件大小\n    const formatFileSize = (bytes)=>{\n        if (bytes === 0) return '0 Bytes';\n        const k = 1024;\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(k));\n        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `border-2 border-dashed rounded-lg p-6 text-center transition-colors ${isDragging ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}`,\n                onDragOver: handleDragOver,\n                onDragLeave: handleDragLeave,\n                onDrop: handleDrop,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        ref: fileInputRef,\n                        type: \"file\",\n                        multiple: true,\n                        accept: acceptedTypes.join(','),\n                        onChange: (e)=>handleFileSelect(e.target.files),\n                        className: \"hidden\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowUpIcon_EyeIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-12 h-12 text-gray-400 mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>fileInputRef.current?.click(),\n                                        disabled: isUploading || uploadedImages.length >= maxFiles,\n                                        className: \"text-blue-600 hover:text-blue-800 font-medium disabled:text-gray-400\",\n                                        children: \"点击上传图片\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-500\",\n                                        children: \" 或拖拽图片到此处\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500\",\n                                children: [\n                                    \"支持 JPG, PNG, WebP, GIF 格式，最大 \",\n                                    maxSize,\n                                    \"MB，最多 \",\n                                    maxFiles,\n                                    \" 张\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this),\n            uploadedImages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        className: \"font-medium text-gray-900\",\n                        children: [\n                            \"已上传的图片 (\",\n                            uploadedImages.length,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: uploadedImages.map((image)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border border-gray-200 rounded-lg p-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: image.url,\n                                                alt: image.name,\n                                                className: \"w-16 h-16 object-cover rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-900 truncate\",\n                                                    children: image.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: [\n                                                        formatFileSize(image.size),\n                                                        \" • \",\n                                                        image.type\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-2 mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>startImageEdit(image),\n                                                            className: \"flex items-center space-x-1 px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowUpIcon_EyeIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                                                    lineNumber: 210,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"编辑\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                                                    lineNumber: 211,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>window.open(image.url, '_blank'),\n                                                            className: \"flex items-center space-x-1 px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowUpIcon_EyeIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                                                    lineNumber: 218,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"查看\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                                                    lineNumber: 219,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>removeImage(image.id),\n                                                            className: \"flex items-center space-x-1 px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowUpIcon_EyeIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                                                    lineNumber: 226,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"删除\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                                                    lineNumber: 227,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 17\n                                }, this)\n                            }, image.id, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                lineNumber: 180,\n                columnNumber: 9\n            }, this),\n            editingImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 border-b border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"编辑图片\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setEditingImage(null),\n                                        className: \"text-gray-500 hover:text-gray-700\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CloudArrowUpIcon_EyeIcon_PencilIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: editingImage.url,\n                                            alt: editingImage.name,\n                                            className: \"max-w-full max-h-64 object-contain mx-auto rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500 mt-2\",\n                                            children: editingImage.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"编辑指令\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: editInstruction,\n                                            onChange: (e)=>setEditInstruction(e.target.value),\n                                            placeholder: \"描述您想要对图片进行的编辑，例如： • 将背景改为蓝天白云 • 给人物添加帽子 • 调整图片为黑白风格 • 移除背景中的杂物\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none\",\n                                            rows: 4\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-50 p-3 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-sm font-medium text-blue-900 mb-2\",\n                                            children: \"\\uD83D\\uDCA1 编辑建议\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm text-blue-800 space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• 描述要具体明确，避免模糊表达\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• 可以指定颜色、风格、位置等细节\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• 支持添加、删除、修改图片元素\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"• 可以调整整体风格和色调\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 border-t border-gray-200 flex justify-end space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setEditingImage(null),\n                                    className: \"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors\",\n                                    children: \"取消\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: submitEdit,\n                                    disabled: !editInstruction.trim(),\n                                    className: \"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors\",\n                                    children: \"开始编辑\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n                lineNumber: 240,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageUpload.tsx\",\n        lineNumber: 139,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9DaGF0L0ltYWdlVXBsb2FkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBRWdEO0FBT1g7QUFtQnRCLFNBQVNPLFlBQVksRUFDbENDLGFBQWEsRUFDYkMsV0FBVyxFQUNYQyxXQUFXLENBQUMsRUFDWkMsVUFBVSxFQUFFLEVBQ1pDLGdCQUFnQjtJQUFDO0lBQWM7SUFBYTtJQUFjO0NBQVksRUFDckQ7SUFDakIsTUFBTSxDQUFDQyxnQkFBZ0JDLGtCQUFrQixHQUFHYiwrQ0FBUUEsQ0FBa0IsRUFBRTtJQUN4RSxNQUFNLENBQUNjLFlBQVlDLGNBQWMsR0FBR2YsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDZ0IsY0FBY0MsZ0JBQWdCLEdBQUdqQiwrQ0FBUUEsQ0FBdUI7SUFDdkUsTUFBTSxDQUFDa0IsaUJBQWlCQyxtQkFBbUIsR0FBR25CLCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU0sQ0FBQ29CLGFBQWFDLGVBQWUsR0FBR3JCLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU1zQixlQUFlckIsNkNBQU1BLENBQW1CO0lBRTlDLFNBQVM7SUFDVCxNQUFNc0IsbUJBQW1CLENBQUNDO1FBQ3hCLElBQUksQ0FBQ0EsT0FBTztRQUVaLE1BQU1DLGFBQWFDLE1BQU1DLElBQUksQ0FBQ0gsT0FBT0ksTUFBTSxDQUFDQyxDQUFBQTtZQUMxQyxTQUFTO1lBQ1QsSUFBSSxDQUFDbEIsY0FBY21CLFFBQVEsQ0FBQ0QsS0FBS0UsSUFBSSxHQUFHO2dCQUN0Q0MsTUFBTSxDQUFDLFVBQVUsRUFBRUgsS0FBS0UsSUFBSSxFQUFFO2dCQUM5QixPQUFPO1lBQ1Q7WUFFQSxTQUFTO1lBQ1QsSUFBSUYsS0FBS0ksSUFBSSxHQUFHdkIsVUFBVSxPQUFPLE1BQU07Z0JBQ3JDc0IsTUFBTSxDQUFDLE1BQU0sRUFBRUgsS0FBS0ssSUFBSSxDQUFDLElBQUksRUFBRXhCLFFBQVEsR0FBRyxDQUFDO2dCQUMzQyxPQUFPO1lBQ1Q7WUFFQSxPQUFPO1FBQ1Q7UUFFQSxJQUFJRSxlQUFldUIsTUFBTSxHQUFHVixXQUFXVSxNQUFNLEdBQUcxQixVQUFVO1lBQ3hEdUIsTUFBTSxDQUFDLE1BQU0sRUFBRXZCLFNBQVMsR0FBRyxDQUFDO1lBQzVCO1FBQ0Y7UUFFQVksZUFBZTtRQUVmLE1BQU1lLFlBQTZCWCxXQUFXWSxHQUFHLENBQUNSLENBQUFBLE9BQVM7Z0JBQ3pEUyxJQUFJQyxLQUFLQyxHQUFHLEdBQUdDLFFBQVEsS0FBS0MsS0FBS0MsTUFBTSxHQUFHRixRQUFRLENBQUMsSUFBSUcsTUFBTSxDQUFDLEdBQUc7Z0JBQ2pFZjtnQkFDQWdCLEtBQUtDLElBQUlDLGVBQWUsQ0FBQ2xCO2dCQUN6QkssTUFBTUwsS0FBS0ssSUFBSTtnQkFDZkQsTUFBTUosS0FBS0ksSUFBSTtnQkFDZkYsTUFBTUYsS0FBS0UsSUFBSTtZQUNqQjtRQUVBLE1BQU1pQixnQkFBZ0I7ZUFBSXBDO2VBQW1Cd0I7U0FBVTtRQUN2RHZCLGtCQUFrQm1DO1FBQ2xCekMsY0FBY3lDO1FBQ2QzQixlQUFlO0lBQ2pCO0lBRUEsT0FBTztJQUNQLE1BQU00QixpQkFBaUIsQ0FBQ0M7UUFDdEJBLEVBQUVDLGNBQWM7UUFDaEJwQyxjQUFjO0lBQ2hCO0lBRUEsTUFBTXFDLGtCQUFrQixDQUFDRjtRQUN2QkEsRUFBRUMsY0FBYztRQUNoQnBDLGNBQWM7SUFDaEI7SUFFQSxNQUFNc0MsYUFBYSxDQUFDSDtRQUNsQkEsRUFBRUMsY0FBYztRQUNoQnBDLGNBQWM7UUFDZFEsaUJBQWlCMkIsRUFBRUksWUFBWSxDQUFDOUIsS0FBSztJQUN2QztJQUVBLE9BQU87SUFDUCxNQUFNK0IsY0FBYyxDQUFDQztRQUNuQixNQUFNQyxnQkFBZ0I3QyxlQUFlOEMsSUFBSSxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJckIsRUFBRSxLQUFLa0I7UUFDNUQsSUFBSUMsZUFBZTtZQUNqQlgsSUFBSWMsZUFBZSxDQUFDSCxjQUFjWixHQUFHO1FBQ3ZDO1FBRUEsTUFBTUcsZ0JBQWdCcEMsZUFBZWdCLE1BQU0sQ0FBQytCLENBQUFBLE1BQU9BLElBQUlyQixFQUFFLEtBQUtrQjtRQUM5RDNDLGtCQUFrQm1DO1FBQ2xCekMsY0FBY3lDO0lBQ2hCO0lBRUEsU0FBUztJQUNULE1BQU1hLGlCQUFpQixDQUFDQztRQUN0QjdDLGdCQUFnQjZDO1FBQ2hCM0MsbUJBQW1CO0lBQ3JCO0lBRUEsU0FBUztJQUNULE1BQU00QyxhQUFhO1FBQ2pCLElBQUkvQyxnQkFBZ0JFLGdCQUFnQjhDLElBQUksSUFBSTtZQUMxQ3hELFlBQVlRLGNBQWNFLGdCQUFnQjhDLElBQUk7WUFDOUMvQyxnQkFBZ0I7WUFDaEJFLG1CQUFtQjtRQUNyQjtJQUNGO0lBRUEsVUFBVTtJQUNWLE1BQU04QyxpQkFBaUIsQ0FBQ0M7UUFDdEIsSUFBSUEsVUFBVSxHQUFHLE9BQU87UUFDeEIsTUFBTUMsSUFBSTtRQUNWLE1BQU1DLFFBQVE7WUFBQztZQUFTO1lBQU07WUFBTTtTQUFLO1FBQ3pDLE1BQU1DLElBQUkzQixLQUFLNEIsS0FBSyxDQUFDNUIsS0FBSzZCLEdBQUcsQ0FBQ0wsU0FBU3hCLEtBQUs2QixHQUFHLENBQUNKO1FBQ2hELE9BQU9LLFdBQVcsQ0FBQ04sUUFBUXhCLEtBQUsrQixHQUFHLENBQUNOLEdBQUdFLEVBQUMsRUFBR0ssT0FBTyxDQUFDLE1BQU0sTUFBTU4sS0FBSyxDQUFDQyxFQUFFO0lBQ3pFO0lBRUEscUJBQ0UsOERBQUNNO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFDQ0MsV0FBVyxDQUFDLG9FQUFvRSxFQUM5RTlELGFBQ0ksK0JBQ0EseUNBQ0o7Z0JBQ0YrRCxZQUFZNUI7Z0JBQ1o2QixhQUFhMUI7Z0JBQ2IyQixRQUFRMUI7O2tDQUVSLDhEQUFDMkI7d0JBQ0NDLEtBQUszRDt3QkFDTFMsTUFBSzt3QkFDTG1ELFFBQVE7d0JBQ1JDLFFBQVF4RSxjQUFjeUUsSUFBSSxDQUFDO3dCQUMzQkMsVUFBVSxDQUFDbkMsSUFBTTNCLGlCQUFpQjJCLEVBQUVvQyxNQUFNLENBQUM5RCxLQUFLO3dCQUNoRG9ELFdBQVU7Ozs7OztrQ0FHWiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDMUUsdUlBQWdCQTtnQ0FBQzBFLFdBQVU7Ozs7OzswQ0FDNUIsOERBQUNEOztrREFDQyw4REFBQ1k7d0NBQ0NDLFNBQVMsSUFBTWxFLGFBQWFtRSxPQUFPLEVBQUVDO3dDQUNyQ0MsVUFBVXZFLGVBQWVSLGVBQWV1QixNQUFNLElBQUkxQjt3Q0FDbERtRSxXQUFVO2tEQUNYOzs7Ozs7a0RBR0QsOERBQUNnQjt3Q0FBS2hCLFdBQVU7a0RBQWdCOzs7Ozs7Ozs7Ozs7MENBRWxDLDhEQUFDaUI7Z0NBQUVqQixXQUFVOztvQ0FBd0I7b0NBQ0xsRTtvQ0FBUTtvQ0FBT0Q7b0NBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFNM0RHLGVBQWV1QixNQUFNLEdBQUcsbUJBQ3ZCLDhEQUFDd0M7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDa0I7d0JBQUdsQixXQUFVOzs0QkFBNEI7NEJBQVNoRSxlQUFldUIsTUFBTTs0QkFBQzs7Ozs7OztrQ0FDekUsOERBQUN3Qzt3QkFBSUMsV0FBVTtrQ0FDWmhFLGVBQWV5QixHQUFHLENBQUMsQ0FBQ3lCLHNCQUNuQiw4REFBQ2E7Z0NBQW1CQyxXQUFVOzBDQUM1Qiw0RUFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUViLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ2pCO2dEQUNDb0MsS0FBS2pDLE1BQU1qQixHQUFHO2dEQUNkbUQsS0FBS2xDLE1BQU01QixJQUFJO2dEQUNmMEMsV0FBVTs7Ozs7Ozs7Ozs7c0RBS2QsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ2lCO29EQUFFakIsV0FBVTs4REFDVmQsTUFBTTVCLElBQUk7Ozs7Ozs4REFFYiw4REFBQzJEO29EQUFFakIsV0FBVTs7d0RBQ1ZYLGVBQWVILE1BQU03QixJQUFJO3dEQUFFO3dEQUFJNkIsTUFBTS9CLElBQUk7Ozs7Ozs7OERBSTVDLDhEQUFDNEM7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDVzs0REFDQ0MsU0FBUyxJQUFNM0IsZUFBZUM7NERBQzlCYyxXQUFVOzs4RUFFViw4REFBQ3hFLHVJQUFVQTtvRUFBQ3dFLFdBQVU7Ozs7Ozs4RUFDdEIsOERBQUNnQjs4RUFBSzs7Ozs7Ozs7Ozs7O3NFQUdSLDhEQUFDTDs0REFDQ0MsU0FBUyxJQUFNUyxPQUFPQyxJQUFJLENBQUNwQyxNQUFNakIsR0FBRyxFQUFFOzREQUN0QytCLFdBQVU7OzhFQUVWLDhEQUFDdkUsdUlBQU9BO29FQUFDdUUsV0FBVTs7Ozs7OzhFQUNuQiw4REFBQ2dCOzhFQUFLOzs7Ozs7Ozs7Ozs7c0VBR1IsOERBQUNMOzREQUNDQyxTQUFTLElBQU1qQyxZQUFZTyxNQUFNeEIsRUFBRTs0REFDbkNzQyxXQUFVOzs4RUFFViw4REFBQ3pFLHVJQUFTQTtvRUFBQ3lFLFdBQVU7Ozs7Ozs4RUFDckIsOERBQUNnQjs4RUFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OytCQTNDTjlCLE1BQU14QixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7O1lBdUR6QnRCLDhCQUNDLDhEQUFDMkQ7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FFYiw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ3VCO3dDQUFHdkIsV0FBVTtrREFBc0M7Ozs7OztrREFDcEQsOERBQUNXO3dDQUNDQyxTQUFTLElBQU12RSxnQkFBZ0I7d0NBQy9CMkQsV0FBVTtrREFFViw0RUFBQ3pFLHVJQUFTQTs0Q0FBQ3lFLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTTNCLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBRWIsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ2pCOzRDQUNDb0MsS0FBSy9FLGFBQWE2QixHQUFHOzRDQUNyQm1ELEtBQUtoRixhQUFha0IsSUFBSTs0Q0FDdEIwQyxXQUFVOzs7Ozs7c0RBRVosOERBQUNpQjs0Q0FBRWpCLFdBQVU7c0RBQThCNUQsYUFBYWtCLElBQUk7Ozs7Ozs7Ozs7Ozs4Q0FJOUQsOERBQUN5Qzs7c0RBQ0MsOERBQUN5Qjs0Q0FBTXhCLFdBQVU7c0RBQStDOzs7Ozs7c0RBR2hFLDhEQUFDeUI7NENBQ0NDLE9BQU9wRjs0Q0FDUG1FLFVBQVUsQ0FBQ25DLElBQU0vQixtQkFBbUIrQixFQUFFb0MsTUFBTSxDQUFDZ0IsS0FBSzs0Q0FDbERDLGFBQVk7NENBQ1ozQixXQUFVOzRDQUNWNEIsTUFBTTs7Ozs7Ozs7Ozs7OzhDQUtWLDhEQUFDN0I7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDa0I7NENBQUdsQixXQUFVO3NEQUF5Qzs7Ozs7O3NEQUN2RCw4REFBQzZCOzRDQUFHN0IsV0FBVTs7OERBQ1osOERBQUM4Qjs4REFBRzs7Ozs7OzhEQUNKLDhEQUFDQTs4REFBRzs7Ozs7OzhEQUNKLDhEQUFDQTs4REFBRzs7Ozs7OzhEQUNKLDhEQUFDQTs4REFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU1WLDhEQUFDL0I7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDVztvQ0FDQ0MsU0FBUyxJQUFNdkUsZ0JBQWdCO29DQUMvQjJELFdBQVU7OENBQ1g7Ozs7Ozs4Q0FHRCw4REFBQ1c7b0NBQ0NDLFNBQVN6QjtvQ0FDVDRCLFVBQVUsQ0FBQ3pFLGdCQUFnQjhDLElBQUk7b0NBQy9CWSxXQUFVOzhDQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVNmIiwic291cmNlcyI6WyIvVXNlcnMvbGl1cGVuZy9jdXJzb3Iv6L+b6Zi2L0ZsdXjlr7nor53lvI/lupTnlKgvZmx1eC1jaGF0LWFwcC9zcmMvY29tcG9uZW50cy9DaGF0L0ltYWdlVXBsb2FkLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgXG4gIFBob3RvSWNvbixcbiAgQ2xvdWRBcnJvd1VwSWNvbixcbiAgWE1hcmtJY29uLFxuICBQZW5jaWxJY29uLFxuICBFeWVJY29uXG59IGZyb20gJ0BoZXJvaWNvbnMvcmVhY3QvMjQvb3V0bGluZSc7XG5cbmludGVyZmFjZSBVcGxvYWRlZEltYWdlIHtcbiAgaWQ6IHN0cmluZztcbiAgZmlsZTogRmlsZTtcbiAgdXJsOiBzdHJpbmc7XG4gIG5hbWU6IHN0cmluZztcbiAgc2l6ZTogbnVtYmVyO1xuICB0eXBlOiBzdHJpbmc7XG59XG5cbmludGVyZmFjZSBJbWFnZVVwbG9hZFByb3BzIHtcbiAgb25JbWFnZVVwbG9hZDogKGltYWdlczogVXBsb2FkZWRJbWFnZVtdKSA9PiB2b2lkO1xuICBvbkltYWdlRWRpdDogKGltYWdlOiBVcGxvYWRlZEltYWdlLCBpbnN0cnVjdGlvbjogc3RyaW5nKSA9PiB2b2lkO1xuICBtYXhGaWxlcz86IG51bWJlcjtcbiAgbWF4U2l6ZT86IG51bWJlcjsgLy8gTUJcbiAgYWNjZXB0ZWRUeXBlcz86IHN0cmluZ1tdO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBJbWFnZVVwbG9hZCh7IFxuICBvbkltYWdlVXBsb2FkLCBcbiAgb25JbWFnZUVkaXQsXG4gIG1heEZpbGVzID0gNSxcbiAgbWF4U2l6ZSA9IDEwLFxuICBhY2NlcHRlZFR5cGVzID0gWydpbWFnZS9qcGVnJywgJ2ltYWdlL3BuZycsICdpbWFnZS93ZWJwJywgJ2ltYWdlL2dpZiddXG59OiBJbWFnZVVwbG9hZFByb3BzKSB7XG4gIGNvbnN0IFt1cGxvYWRlZEltYWdlcywgc2V0VXBsb2FkZWRJbWFnZXNdID0gdXNlU3RhdGU8VXBsb2FkZWRJbWFnZVtdPihbXSk7XG4gIGNvbnN0IFtpc0RyYWdnaW5nLCBzZXRJc0RyYWdnaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2VkaXRpbmdJbWFnZSwgc2V0RWRpdGluZ0ltYWdlXSA9IHVzZVN0YXRlPFVwbG9hZGVkSW1hZ2UgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2VkaXRJbnN0cnVjdGlvbiwgc2V0RWRpdEluc3RydWN0aW9uXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW2lzVXBsb2FkaW5nLCBzZXRJc1VwbG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IGZpbGVJbnB1dFJlZiA9IHVzZVJlZjxIVE1MSW5wdXRFbGVtZW50PihudWxsKTtcblxuICAvLyDlpITnkIbmlofku7bpgInmi6lcbiAgY29uc3QgaGFuZGxlRmlsZVNlbGVjdCA9IChmaWxlczogRmlsZUxpc3QgfCBudWxsKSA9PiB7XG4gICAgaWYgKCFmaWxlcykgcmV0dXJuO1xuICAgIFxuICAgIGNvbnN0IHZhbGlkRmlsZXMgPSBBcnJheS5mcm9tKGZpbGVzKS5maWx0ZXIoZmlsZSA9PiB7XG4gICAgICAvLyDmo4Dmn6Xmlofku7bnsbvlnotcbiAgICAgIGlmICghYWNjZXB0ZWRUeXBlcy5pbmNsdWRlcyhmaWxlLnR5cGUpKSB7XG4gICAgICAgIGFsZXJ0KGDkuI3mlK/mjIHnmoTmlofku7bnsbvlnos6ICR7ZmlsZS50eXBlfWApO1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICB9XG4gICAgICBcbiAgICAgIC8vIOajgOafpeaWh+S7tuWkp+Wwj1xuICAgICAgaWYgKGZpbGUuc2l6ZSA+IG1heFNpemUgKiAxMDI0ICogMTAyNCkge1xuICAgICAgICBhbGVydChg5paH5Lu26L+H5aSnOiAke2ZpbGUubmFtZX0gKOacgOWkpyR7bWF4U2l6ZX1NQilgKTtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgfVxuICAgICAgXG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9KTtcblxuICAgIGlmICh1cGxvYWRlZEltYWdlcy5sZW5ndGggKyB2YWxpZEZpbGVzLmxlbmd0aCA+IG1heEZpbGVzKSB7XG4gICAgICBhbGVydChg5pyA5aSa5Y+q6IO95LiK5LygJHttYXhGaWxlc33lvKDlm77niYdgKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBzZXRJc1VwbG9hZGluZyh0cnVlKTtcbiAgICBcbiAgICBjb25zdCBuZXdJbWFnZXM6IFVwbG9hZGVkSW1hZ2VbXSA9IHZhbGlkRmlsZXMubWFwKGZpbGUgPT4gKHtcbiAgICAgIGlkOiBEYXRlLm5vdygpLnRvU3RyaW5nKCkgKyBNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHIoMiwgOSksXG4gICAgICBmaWxlLFxuICAgICAgdXJsOiBVUkwuY3JlYXRlT2JqZWN0VVJMKGZpbGUpLFxuICAgICAgbmFtZTogZmlsZS5uYW1lLFxuICAgICAgc2l6ZTogZmlsZS5zaXplLFxuICAgICAgdHlwZTogZmlsZS50eXBlXG4gICAgfSkpO1xuXG4gICAgY29uc3QgdXBkYXRlZEltYWdlcyA9IFsuLi51cGxvYWRlZEltYWdlcywgLi4ubmV3SW1hZ2VzXTtcbiAgICBzZXRVcGxvYWRlZEltYWdlcyh1cGRhdGVkSW1hZ2VzKTtcbiAgICBvbkltYWdlVXBsb2FkKHVwZGF0ZWRJbWFnZXMpO1xuICAgIHNldElzVXBsb2FkaW5nKGZhbHNlKTtcbiAgfTtcblxuICAvLyDmi5bmi73lpITnkIZcbiAgY29uc3QgaGFuZGxlRHJhZ092ZXIgPSAoZTogUmVhY3QuRHJhZ0V2ZW50KSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIHNldElzRHJhZ2dpbmcodHJ1ZSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRHJhZ0xlYXZlID0gKGU6IFJlYWN0LkRyYWdFdmVudCkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICBzZXRJc0RyYWdnaW5nKGZhbHNlKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVEcm9wID0gKGU6IFJlYWN0LkRyYWdFdmVudCkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICBzZXRJc0RyYWdnaW5nKGZhbHNlKTtcbiAgICBoYW5kbGVGaWxlU2VsZWN0KGUuZGF0YVRyYW5zZmVyLmZpbGVzKTtcbiAgfTtcblxuICAvLyDliKDpmaTlm77niYdcbiAgY29uc3QgcmVtb3ZlSW1hZ2UgPSAoaW1hZ2VJZDogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VUb1JlbW92ZSA9IHVwbG9hZGVkSW1hZ2VzLmZpbmQoaW1nID0+IGltZy5pZCA9PT0gaW1hZ2VJZCk7XG4gICAgaWYgKGltYWdlVG9SZW1vdmUpIHtcbiAgICAgIFVSTC5yZXZva2VPYmplY3RVUkwoaW1hZ2VUb1JlbW92ZS51cmwpO1xuICAgIH1cbiAgICBcbiAgICBjb25zdCB1cGRhdGVkSW1hZ2VzID0gdXBsb2FkZWRJbWFnZXMuZmlsdGVyKGltZyA9PiBpbWcuaWQgIT09IGltYWdlSWQpO1xuICAgIHNldFVwbG9hZGVkSW1hZ2VzKHVwZGF0ZWRJbWFnZXMpO1xuICAgIG9uSW1hZ2VVcGxvYWQodXBkYXRlZEltYWdlcyk7XG4gIH07XG5cbiAgLy8g5byA5aeL57yW6L6R5Zu+54mHXG4gIGNvbnN0IHN0YXJ0SW1hZ2VFZGl0ID0gKGltYWdlOiBVcGxvYWRlZEltYWdlKSA9PiB7XG4gICAgc2V0RWRpdGluZ0ltYWdlKGltYWdlKTtcbiAgICBzZXRFZGl0SW5zdHJ1Y3Rpb24oJycpO1xuICB9O1xuXG4gIC8vIOaPkOS6pOe8lui+keaMh+S7pFxuICBjb25zdCBzdWJtaXRFZGl0ID0gKCkgPT4ge1xuICAgIGlmIChlZGl0aW5nSW1hZ2UgJiYgZWRpdEluc3RydWN0aW9uLnRyaW0oKSkge1xuICAgICAgb25JbWFnZUVkaXQoZWRpdGluZ0ltYWdlLCBlZGl0SW5zdHJ1Y3Rpb24udHJpbSgpKTtcbiAgICAgIHNldEVkaXRpbmdJbWFnZShudWxsKTtcbiAgICAgIHNldEVkaXRJbnN0cnVjdGlvbignJyk7XG4gICAgfVxuICB9O1xuXG4gIC8vIOagvOW8j+WMluaWh+S7tuWkp+Wwj1xuICBjb25zdCBmb3JtYXRGaWxlU2l6ZSA9IChieXRlczogbnVtYmVyKTogc3RyaW5nID0+IHtcbiAgICBpZiAoYnl0ZXMgPT09IDApIHJldHVybiAnMCBCeXRlcyc7XG4gICAgY29uc3QgayA9IDEwMjQ7XG4gICAgY29uc3Qgc2l6ZXMgPSBbJ0J5dGVzJywgJ0tCJywgJ01CJywgJ0dCJ107XG4gICAgY29uc3QgaSA9IE1hdGguZmxvb3IoTWF0aC5sb2coYnl0ZXMpIC8gTWF0aC5sb2coaykpO1xuICAgIHJldHVybiBwYXJzZUZsb2F0KChieXRlcyAvIE1hdGgucG93KGssIGkpKS50b0ZpeGVkKDIpKSArICcgJyArIHNpemVzW2ldO1xuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgIHsvKiDkuIrkvKDljLrln58gKi99XG4gICAgICA8ZGl2XG4gICAgICAgIGNsYXNzTmFtZT17YGJvcmRlci0yIGJvcmRlci1kYXNoZWQgcm91bmRlZC1sZyBwLTYgdGV4dC1jZW50ZXIgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICBpc0RyYWdnaW5nXG4gICAgICAgICAgICA/ICdib3JkZXItYmx1ZS01MDAgYmctYmx1ZS01MCdcbiAgICAgICAgICAgIDogJ2JvcmRlci1ncmF5LTMwMCBob3Zlcjpib3JkZXItZ3JheS00MDAnXG4gICAgICAgIH1gfVxuICAgICAgICBvbkRyYWdPdmVyPXtoYW5kbGVEcmFnT3Zlcn1cbiAgICAgICAgb25EcmFnTGVhdmU9e2hhbmRsZURyYWdMZWF2ZX1cbiAgICAgICAgb25Ecm9wPXtoYW5kbGVEcm9wfVxuICAgICAgPlxuICAgICAgICA8aW5wdXRcbiAgICAgICAgICByZWY9e2ZpbGVJbnB1dFJlZn1cbiAgICAgICAgICB0eXBlPVwiZmlsZVwiXG4gICAgICAgICAgbXVsdGlwbGVcbiAgICAgICAgICBhY2NlcHQ9e2FjY2VwdGVkVHlwZXMuam9pbignLCcpfVxuICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlRmlsZVNlbGVjdChlLnRhcmdldC5maWxlcyl9XG4gICAgICAgICAgY2xhc3NOYW1lPVwiaGlkZGVuXCJcbiAgICAgICAgLz5cbiAgICAgICAgXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgPENsb3VkQXJyb3dVcEljb24gY2xhc3NOYW1lPVwidy0xMiBoLTEyIHRleHQtZ3JheS00MDAgbXgtYXV0b1wiIC8+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gZmlsZUlucHV0UmVmLmN1cnJlbnQ/LmNsaWNrKCl9XG4gICAgICAgICAgICAgIGRpc2FibGVkPXtpc1VwbG9hZGluZyB8fCB1cGxvYWRlZEltYWdlcy5sZW5ndGggPj0gbWF4RmlsZXN9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtYmx1ZS02MDAgaG92ZXI6dGV4dC1ibHVlLTgwMCBmb250LW1lZGl1bSBkaXNhYmxlZDp0ZXh0LWdyYXktNDAwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAg54K55Ye75LiK5Lyg5Zu+54mHXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDBcIj4g5oiW5ouW5ou95Zu+54mH5Yiw5q2k5aSEPC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAg5pSv5oyBIEpQRywgUE5HLCBXZWJQLCBHSUYg5qC85byP77yM5pyA5aSnIHttYXhTaXplfU1C77yM5pyA5aSaIHttYXhGaWxlc30g5bygXG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7Lyog5bey5LiK5Lyg55qE5Zu+54mH5YiX6KGoICovfVxuICAgICAge3VwbG9hZGVkSW1hZ2VzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+5bey5LiK5Lyg55qE5Zu+54mHICh7dXBsb2FkZWRJbWFnZXMubGVuZ3RofSk8L2g0PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAge3VwbG9hZGVkSW1hZ2VzLm1hcCgoaW1hZ2UpID0+IChcbiAgICAgICAgICAgICAgPGRpdiBrZXk9e2ltYWdlLmlkfSBjbGFzc05hbWU9XCJib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQtbGcgcC0zXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICAgICAgey8qIOWbvueJh+mihOiniCAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICAgICAgc3JjPXtpbWFnZS51cmx9XG4gICAgICAgICAgICAgICAgICAgICAgYWx0PXtpbWFnZS5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctMTYgaC0xNiBvYmplY3QtY292ZXIgcm91bmRlZFwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgey8qIOWbvueJh+S/oeaBryAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG1pbi13LTBcIj5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIHRydW5jYXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2ltYWdlLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdEZpbGVTaXplKGltYWdlLnNpemUpfSDigKIge2ltYWdlLnR5cGV9XG4gICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAgIHsvKiDmk43kvZzmjInpkq4gKi99XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTIgbXQtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHN0YXJ0SW1hZ2VFZGl0KGltYWdlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMSBweC0yIHB5LTEgdGV4dC14cyBiZy1ibHVlLTEwMCB0ZXh0LWJsdWUtNzAwIHJvdW5kZWQgaG92ZXI6YmctYmx1ZS0yMDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxQZW5jaWxJY29uIGNsYXNzTmFtZT1cInctMyBoLTNcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+57yW6L6RPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHdpbmRvdy5vcGVuKGltYWdlLnVybCwgJ19ibGFuaycpfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xIHB4LTIgcHktMSB0ZXh0LXhzIGJnLWdyYXktMTAwIHRleHQtZ3JheS03MDAgcm91bmRlZCBob3ZlcjpiZy1ncmF5LTIwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEV5ZUljb24gY2xhc3NOYW1lPVwidy0zIGgtM1wiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj7mn6XnnIs8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcmVtb3ZlSW1hZ2UoaW1hZ2UuaWQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xIHB4LTIgcHktMSB0ZXh0LXhzIGJnLXJlZC0xMDAgdGV4dC1yZWQtNzAwIHJvdW5kZWQgaG92ZXI6YmctcmVkLTIwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFhNYXJrSWNvbiBjbGFzc05hbWU9XCJ3LTMgaC0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPuWIoOmZpDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuXG4gICAgICB7Lyog5Zu+54mH57yW6L6R5qih5oCB5qGGICovfVxuICAgICAge2VkaXRpbmdJbWFnZSAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ibGFjayBiZy1vcGFjaXR5LTUwIHotNTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcC00XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy14bCB3LWZ1bGwgbWF4LXctMnhsIG1heC1oLVs5MHZoXSBvdmVyZmxvdy15LWF1dG9cIj5cbiAgICAgICAgICAgIHsvKiDlpLTpg6ggKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNiBib3JkZXItYiBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj7nvJbovpHlm77niYc8L2gzPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEVkaXRpbmdJbWFnZShudWxsKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtZ3JheS01MDAgaG92ZXI6dGV4dC1ncmF5LTcwMFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPFhNYXJrSWNvbiBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIOWGheWuuSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02IHNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICB7Lyog5Zu+54mH6aKE6KeIICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgICAgc3JjPXtlZGl0aW5nSW1hZ2UudXJsfVxuICAgICAgICAgICAgICAgICAgYWx0PXtlZGl0aW5nSW1hZ2UubmFtZX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1heC13LWZ1bGwgbWF4LWgtNjQgb2JqZWN0LWNvbnRhaW4gbXgtYXV0byByb3VuZGVkXCJcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTUwMCBtdC0yXCI+e2VkaXRpbmdJbWFnZS5uYW1lfTwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIOe8lui+keaMh+S7pOi+k+WFpSAqL31cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgIOe8lui+keaMh+S7pFxuICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17ZWRpdEluc3RydWN0aW9ufVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRFZGl0SW5zdHJ1Y3Rpb24oZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLmj4/ov7Dmgqjmg7PopoHlr7nlm77niYfov5vooYznmoTnvJbovpHvvIzkvovlpoLvvJomIzEwO+KAoiDlsIbog4zmma/mlLnkuLrok53lpKnnmb3kupEmIzEwO+KAoiDnu5nkurrnianmt7vliqDluL3lrZAmIzEwO+KAoiDosIPmlbTlm77niYfkuLrpu5Hnmb3po47moLwmIzEwO+KAoiDnp7vpmaTog4zmma/kuK3nmoTmnYLnialcIlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIHJlc2l6ZS1ub25lXCJcbiAgICAgICAgICAgICAgICAgIHJvd3M9ezR9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIOe8lui+keW7uuiuriAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ibHVlLTUwIHAtMyByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ibHVlLTkwMCBtYi0yXCI+8J+SoSDnvJbovpHlu7rorq48L2g0PlxuICAgICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtYmx1ZS04MDAgc3BhY2UteS0xXCI+XG4gICAgICAgICAgICAgICAgICA8bGk+4oCiIOaPj+i/sOimgeWFt+S9k+aYjuehru+8jOmBv+WFjeaooeeziuihqOi+vjwvbGk+XG4gICAgICAgICAgICAgICAgICA8bGk+4oCiIOWPr+S7peaMh+WumuminOiJsuOAgemjjuagvOOAgeS9jee9ruetiee7huiKgjwvbGk+XG4gICAgICAgICAgICAgICAgICA8bGk+4oCiIOaUr+aMgea3u+WKoOOAgeWIoOmZpOOAgeS/ruaUueWbvueJh+WFg+e0oDwvbGk+XG4gICAgICAgICAgICAgICAgICA8bGk+4oCiIOWPr+S7peiwg+aVtOaVtOS9k+mjjuagvOWSjOiJsuiwgzwvbGk+XG4gICAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIOW6lemDqOaMiemSriAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02IGJvcmRlci10IGJvcmRlci1ncmF5LTIwMCBmbGV4IGp1c3RpZnktZW5kIHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0RWRpdGluZ0ltYWdlKG51bGwpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiB0ZXh0LWdyYXktNzAwIGJnLWdyYXktMTAwIHJvdW5kZWQtbGcgaG92ZXI6YmctZ3JheS0yMDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAg5Y+W5raIXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17c3VibWl0RWRpdH1cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17IWVkaXRJbnN0cnVjdGlvbi50cmltKCl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIGJnLWJsdWUtNTAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyBob3ZlcjpiZy1ibHVlLTYwMCBkaXNhYmxlZDpiZy1ncmF5LTQwMCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAg5byA5aeL57yW6L6RXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlUmVmIiwiQ2xvdWRBcnJvd1VwSWNvbiIsIlhNYXJrSWNvbiIsIlBlbmNpbEljb24iLCJFeWVJY29uIiwiSW1hZ2VVcGxvYWQiLCJvbkltYWdlVXBsb2FkIiwib25JbWFnZUVkaXQiLCJtYXhGaWxlcyIsIm1heFNpemUiLCJhY2NlcHRlZFR5cGVzIiwidXBsb2FkZWRJbWFnZXMiLCJzZXRVcGxvYWRlZEltYWdlcyIsImlzRHJhZ2dpbmciLCJzZXRJc0RyYWdnaW5nIiwiZWRpdGluZ0ltYWdlIiwic2V0RWRpdGluZ0ltYWdlIiwiZWRpdEluc3RydWN0aW9uIiwic2V0RWRpdEluc3RydWN0aW9uIiwiaXNVcGxvYWRpbmciLCJzZXRJc1VwbG9hZGluZyIsImZpbGVJbnB1dFJlZiIsImhhbmRsZUZpbGVTZWxlY3QiLCJmaWxlcyIsInZhbGlkRmlsZXMiLCJBcnJheSIsImZyb20iLCJmaWx0ZXIiLCJmaWxlIiwiaW5jbHVkZXMiLCJ0eXBlIiwiYWxlcnQiLCJzaXplIiwibmFtZSIsImxlbmd0aCIsIm5ld0ltYWdlcyIsIm1hcCIsImlkIiwiRGF0ZSIsIm5vdyIsInRvU3RyaW5nIiwiTWF0aCIsInJhbmRvbSIsInN1YnN0ciIsInVybCIsIlVSTCIsImNyZWF0ZU9iamVjdFVSTCIsInVwZGF0ZWRJbWFnZXMiLCJoYW5kbGVEcmFnT3ZlciIsImUiLCJwcmV2ZW50RGVmYXVsdCIsImhhbmRsZURyYWdMZWF2ZSIsImhhbmRsZURyb3AiLCJkYXRhVHJhbnNmZXIiLCJyZW1vdmVJbWFnZSIsImltYWdlSWQiLCJpbWFnZVRvUmVtb3ZlIiwiZmluZCIsImltZyIsInJldm9rZU9iamVjdFVSTCIsInN0YXJ0SW1hZ2VFZGl0IiwiaW1hZ2UiLCJzdWJtaXRFZGl0IiwidHJpbSIsImZvcm1hdEZpbGVTaXplIiwiYnl0ZXMiLCJrIiwic2l6ZXMiLCJpIiwiZmxvb3IiLCJsb2ciLCJwYXJzZUZsb2F0IiwicG93IiwidG9GaXhlZCIsImRpdiIsImNsYXNzTmFtZSIsIm9uRHJhZ092ZXIiLCJvbkRyYWdMZWF2ZSIsIm9uRHJvcCIsImlucHV0IiwicmVmIiwibXVsdGlwbGUiLCJhY2NlcHQiLCJqb2luIiwib25DaGFuZ2UiLCJ0YXJnZXQiLCJidXR0b24iLCJvbkNsaWNrIiwiY3VycmVudCIsImNsaWNrIiwiZGlzYWJsZWQiLCJzcGFuIiwicCIsImg0Iiwic3JjIiwiYWx0Iiwid2luZG93Iiwib3BlbiIsImgzIiwibGFiZWwiLCJ0ZXh0YXJlYSIsInZhbHVlIiwicGxhY2Vob2xkZXIiLCJyb3dzIiwidWwiLCJsaSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Chat/ImageUpload.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@heroicons"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fchat%2Fpage&page=%2Fchat%2Fpage&appPaths=%2Fchat%2Fpage&pagePath=private-next-app-dir%2Fchat%2Fpage.tsx&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();