/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/edit-image/route";
exports.ids = ["app/api/edit-image/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fedit-image%2Froute&page=%2Fapi%2Fedit-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fedit-image%2Froute.ts&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fedit-image%2Froute&page=%2Fapi%2Fedit-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fedit-image%2Froute.ts&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_liupeng_cursor_Flux_flux_chat_app_src_app_api_edit_image_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/edit-image/route.ts */ \"(rsc)/./src/app/api/edit-image/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/edit-image/route\",\n        pathname: \"/api/edit-image\",\n        filename: \"route\",\n        bundlePath: \"app/api/edit-image/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/api/edit-image/route.ts\",\n    nextConfigOutput,\n    userland: _Users_liupeng_cursor_Flux_flux_chat_app_src_app_api_edit_image_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fedit-image%2Froute&page=%2Fapi%2Fedit-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fedit-image%2Froute.ts&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/edit-image/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/edit-image/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _fal_ai_serverless_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @fal-ai/serverless-client */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/index.js\");\n\n\n// 配置fal.ai客户端\n_fal_ai_serverless_client__WEBPACK_IMPORTED_MODULE_1__.config({\n    credentials: process.env.FAL_KEY || process.env.FAL_API_KEY\n});\n// 支持的图片编辑模型\nconst IMAGE_EDIT_MODELS = {\n    'instruct-pix2pix': 'fal-ai/instruct-pix2pix',\n    'controlnet-inpaint': 'fal-ai/stable-diffusion-xl-controlnet-inpaint',\n    'flux-fill': 'fal-ai/flux/fill',\n    'flux-redux': 'fal-ai/flux/redux'\n};\nasync function POST(request) {\n    try {\n        const formData = await request.formData();\n        const image = formData.get('image');\n        const instruction = formData.get('instruction');\n        const model = formData.get('model') || 'instruct-pix2pix';\n        const strength = parseFloat(formData.get('strength') || '0.8');\n        // 验证必需参数\n        if (!image) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Image file is required'\n            }, {\n                status: 400\n            });\n        }\n        if (!instruction || typeof instruction !== 'string') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Instruction is required and must be a string'\n            }, {\n                status: 400\n            });\n        }\n        // 检查API密钥\n        const hasValidFalKey = process.env.FAL_KEY && process.env.FAL_KEY !== 'your_fal_api_key_here' || process.env.FAL_API_KEY && process.env.FAL_API_KEY !== 'your_fal_api_key_here';\n        if (!hasValidFalKey) {\n            // 演示模式 - 返回模拟的编辑结果\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                demo_mode: true,\n                edited_image: {\n                    url: 'https://picsum.photos/1024/768?random=' + Date.now(),\n                    width: 1024,\n                    height: 768,\n                    content_type: 'image/jpeg'\n                },\n                metadata: {\n                    model_used: model,\n                    instruction_used: instruction,\n                    processing_time: 5000 + Math.random() * 3000,\n                    original_filename: image.name,\n                    demo_notice: 'This is a demo result. Configure FAL_API_KEY for real image editing.'\n                }\n            });\n        }\n        // 验证文件类型\n        const allowedTypes = [\n            'image/jpeg',\n            'image/png',\n            'image/webp'\n        ];\n        if (!allowedTypes.includes(image.type)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unsupported image format. Please use JPEG, PNG, or WebP.'\n            }, {\n                status: 400\n            });\n        }\n        // 验证文件大小 (最大10MB)\n        const maxSize = 10 * 1024 * 1024;\n        if (image.size > maxSize) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Image file too large. Maximum size is 10MB.'\n            }, {\n                status: 400\n            });\n        }\n        console.log('Image editing request:', {\n            filename: image.name,\n            size: image.size,\n            type: image.type,\n            instruction: instruction.substring(0, 100) + '...',\n            model\n        });\n        // 将图片转换为base64或上传到临时存储\n        const imageBuffer = await image.arrayBuffer();\n        const imageBase64 = Buffer.from(imageBuffer).toString('base64');\n        const imageDataUrl = `data:${image.type};base64,${imageBase64}`;\n        const startTime = Date.now();\n        // 根据模型选择不同的编辑方式\n        let result;\n        const modelEndpoint = IMAGE_EDIT_MODELS[model] || IMAGE_EDIT_MODELS['instruct-pix2pix'];\n        if (model === 'instruct-pix2pix') {\n            // 使用InstructPix2Pix进行指令式编辑\n            result = await _fal_ai_serverless_client__WEBPACK_IMPORTED_MODULE_1__.subscribe(modelEndpoint, {\n                input: {\n                    image_url: imageDataUrl,\n                    prompt: instruction,\n                    num_inference_steps: 20,\n                    guidance_scale: 7.5,\n                    image_guidance_scale: 1.5,\n                    strength: strength\n                },\n                logs: true,\n                onQueueUpdate: (update)=>{\n                    console.log('Edit queue update:', update);\n                }\n            });\n        } else if (model === 'flux-fill') {\n            // 使用FLUX Fill进行填充编辑\n            result = await _fal_ai_serverless_client__WEBPACK_IMPORTED_MODULE_1__.subscribe(modelEndpoint, {\n                input: {\n                    image_url: imageDataUrl,\n                    prompt: instruction,\n                    strength: strength,\n                    num_inference_steps: 28,\n                    guidance_scale: 3.5\n                },\n                logs: true\n            });\n        } else {\n            // 默认使用InstructPix2Pix\n            result = await _fal_ai_serverless_client__WEBPACK_IMPORTED_MODULE_1__.subscribe(IMAGE_EDIT_MODELS['instruct-pix2pix'], {\n                input: {\n                    image_url: imageDataUrl,\n                    prompt: instruction,\n                    num_inference_steps: 20,\n                    guidance_scale: 7.5,\n                    image_guidance_scale: 1.5,\n                    strength: strength\n                },\n                logs: true\n            });\n        }\n        const processingTime = Date.now() - startTime;\n        if (result.data && result.data.images && result.data.images.length > 0) {\n            const editedImage = result.data.images[0];\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                edited_image: {\n                    url: editedImage.url,\n                    width: editedImage.width || 1024,\n                    height: editedImage.height || 768,\n                    content_type: editedImage.content_type || 'image/jpeg'\n                },\n                metadata: {\n                    model_used: model,\n                    instruction_used: instruction,\n                    processing_time: processingTime,\n                    original_filename: image.name,\n                    strength_used: strength,\n                    seed: result.data.seed\n                }\n            });\n        } else {\n            throw new Error('No edited image generated');\n        }\n    } catch (error) {\n        console.error('Image editing error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Image editing failed',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n// GET方法用于获取支持的编辑模型和配置\nasync function GET() {\n    try {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            models: {\n                'instruct-pix2pix': {\n                    name: 'InstructPix2Pix',\n                    description: '基于指令的图片编辑，适合大部分编辑任务',\n                    speed: 'medium',\n                    quality: 'high',\n                    best_for: [\n                        '风格转换',\n                        '对象修改',\n                        '场景变换'\n                    ]\n                },\n                'flux-fill': {\n                    name: 'FLUX Fill',\n                    description: 'FLUX模型的填充编辑功能',\n                    speed: 'fast',\n                    quality: 'excellent',\n                    best_for: [\n                        '局部编辑',\n                        '对象替换',\n                        '背景修改'\n                    ]\n                },\n                'flux-redux': {\n                    name: 'FLUX Redux',\n                    description: 'FLUX模型的重构编辑功能',\n                    speed: 'medium',\n                    quality: 'excellent',\n                    best_for: [\n                        '整体重构',\n                        '风格迁移',\n                        '质量提升'\n                    ]\n                }\n            },\n            supported_formats: [\n                'image/jpeg',\n                'image/png',\n                'image/webp'\n            ],\n            max_file_size: '10MB',\n            parameters: {\n                strength: {\n                    min: 0.1,\n                    max: 1.0,\n                    default: 0.8,\n                    description: '编辑强度，值越高变化越大'\n                }\n            },\n            example_instructions: [\n                '将背景改为蓝天白云',\n                '给人物添加一顶红色帽子',\n                '将图片转换为黑白风格',\n                '移除背景中的汽车',\n                '将白天场景改为夜晚',\n                '给建筑物添加彩色灯光',\n                '将夏天场景改为冬天雪景',\n                '调整人物的服装颜色为蓝色'\n            ]\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to get editing configuration'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/edit-image/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@msgpack","vendor-chunks/@fal-ai","vendor-chunks/eventsource-parser","vendor-chunks/robot3"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fedit-image%2Froute&page=%2Fapi%2Fedit-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fedit-image%2Froute.ts&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();