/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-image/route";
exports.ids = ["app/api/generate-image/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_liupeng_cursor_Flux_flux_chat_app_src_app_api_generate_image_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-image/route.ts */ \"(rsc)/./src/app/api/generate-image/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-image/route\",\n        pathname: \"/api/generate-image\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-image/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/api/generate-image/route.ts\",\n    nextConfigOutput,\n    userland: _Users_liupeng_cursor_Flux_flux_chat_app_src_app_api_generate_image_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-image/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/generate-image/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_imageGeneration__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/imageGeneration */ \"(rsc)/./src/lib/imageGeneration.ts\");\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { prompt, model = 'flux-schnell', size = 'landscape', num_images = 1, style_preset, guidance_scale, num_inference_steps, seed, negative_prompt, batch_mode = false } = body;\n        // 验证必需参数\n        if (!prompt || typeof prompt !== 'string') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Prompt is required and must be a string'\n            }, {\n                status: 400\n            });\n        }\n        // 检查API密钥\n        if (!process.env.FAL_KEY && !process.env.FAL_API_KEY) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'FAL API key not configured',\n                demo_mode: true,\n                message: 'Please configure FAL_API_KEY in environment variables'\n            }, {\n                status: 503\n            });\n        }\n        // 构建生成参数\n        let params = {\n            prompt,\n            model,\n            size,\n            num_images: Math.min(num_images, 4),\n            guidance_scale,\n            num_inference_steps,\n            seed,\n            negative_prompt\n        };\n        // 应用风格预设\n        if (style_preset && style_preset in _lib_imageGeneration__WEBPACK_IMPORTED_MODULE_1__.STYLE_PRESETS) {\n            params = (0,_lib_imageGeneration__WEBPACK_IMPORTED_MODULE_1__.applyStylePreset)(params, style_preset);\n        }\n        console.log('Image generation request:', {\n            prompt: prompt.substring(0, 100) + '...',\n            model,\n            size,\n            num_images: params.num_images\n        });\n        // 生成图像\n        const result = batch_mode && num_images > 1 ? await (0,_lib_imageGeneration__WEBPACK_IMPORTED_MODULE_1__.generateMultipleImages)(params, num_images) : await (0,_lib_imageGeneration__WEBPACK_IMPORTED_MODULE_1__.generateImage)(params);\n        if (result.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                images: result.images,\n                metadata: {\n                    model_used: result.model_used,\n                    generation_time: result.generation_time,\n                    seed: result.seed,\n                    prompt_used: prompt,\n                    parameters: params\n                }\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: result.error,\n                success: false,\n                model_used: result.model_used\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error('Image generation API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n// GET方法用于获取支持的模型和配置\nasync function GET() {\n    try {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            models: {\n                'flux-schnell': {\n                    name: 'FLUX Schnell',\n                    description: '快速生成，4步即可完成',\n                    speed: 'fast',\n                    quality: 'good'\n                },\n                'flux-dev': {\n                    name: 'FLUX Dev',\n                    description: '开发版本，平衡速度和质量',\n                    speed: 'medium',\n                    quality: 'high'\n                },\n                'flux-pro': {\n                    name: 'FLUX Pro',\n                    description: '专业版本，最高质量',\n                    speed: 'slow',\n                    quality: 'excellent'\n                },\n                'sd-xl': {\n                    name: 'Stable Diffusion XL',\n                    description: '经典的高质量模型',\n                    speed: 'medium',\n                    quality: 'high'\n                }\n            },\n            sizes: {\n                'square': '1024x1024',\n                'portrait': '768x1024',\n                'landscape': '1024x768',\n                'wide': '1344x768',\n                'tall': '768x1344'\n            },\n            style_presets: Object.keys(_lib_imageGeneration__WEBPACK_IMPORTED_MODULE_1__.STYLE_PRESETS),\n            limits: {\n                max_images_per_request: 4,\n                max_prompt_length: 1000\n            }\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to get configuration'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-image/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/imageGeneration.ts":
/*!************************************!*\
  !*** ./src/lib/imageGeneration.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IMAGE_MODELS: () => (/* binding */ IMAGE_MODELS),\n/* harmony export */   IMAGE_SIZES: () => (/* binding */ IMAGE_SIZES),\n/* harmony export */   STYLE_PRESETS: () => (/* binding */ STYLE_PRESETS),\n/* harmony export */   applyStylePreset: () => (/* binding */ applyStylePreset),\n/* harmony export */   checkServiceStatus: () => (/* binding */ checkServiceStatus),\n/* harmony export */   generateImage: () => (/* binding */ generateImage),\n/* harmony export */   generateMultipleImages: () => (/* binding */ generateMultipleImages)\n/* harmony export */ });\n/* harmony import */ var _fal_ai_serverless_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @fal-ai/serverless-client */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/index.js\");\n\n// 配置fal.ai客户端\n_fal_ai_serverless_client__WEBPACK_IMPORTED_MODULE_0__.config({\n    credentials: process.env.FAL_KEY || process.env.FAL_API_KEY\n});\n// 支持的图像生成模型\nconst IMAGE_MODELS = {\n    // FLUX模型 - 高质量图像生成\n    'flux-pro': 'fal-ai/flux-pro',\n    'flux-dev': 'fal-ai/flux/dev',\n    'flux-schnell': 'fal-ai/flux/schnell',\n    // Stable Diffusion模型\n    'sd-xl': 'fal-ai/stable-diffusion-xl',\n    'sd-3': 'fal-ai/stable-diffusion-v3-medium',\n    // 其他专业模型\n    'playground': 'fal-ai/playground-v2-5',\n    'kandinsky': 'fal-ai/kandinsky-3'\n};\n// 图像尺寸预设\nconst IMAGE_SIZES = {\n    'square': {\n        width: 1024,\n        height: 1024\n    },\n    'portrait': {\n        width: 768,\n        height: 1024\n    },\n    'landscape': {\n        width: 1024,\n        height: 768\n    },\n    'wide': {\n        width: 1344,\n        height: 768\n    },\n    'tall': {\n        width: 768,\n        height: 1344\n    },\n    'ultra-wide': {\n        width: 1536,\n        height: 640\n    }\n};\n// 主要图像生成函数\nasync function generateImage(params) {\n    try {\n        const model = params.model || 'flux-schnell';\n        const modelEndpoint = IMAGE_MODELS[model];\n        const size = IMAGE_SIZES[params.size || 'landscape'];\n        console.log(`Generating image with model: ${model} (${modelEndpoint})`);\n        // 构建请求参数\n        const requestParams = {\n            prompt: params.prompt,\n            image_size: params.size || 'landscape',\n            num_images: params.num_images || 1,\n            guidance_scale: params.guidance_scale || 7.5,\n            num_inference_steps: params.num_inference_steps || getDefaultSteps(model),\n            enable_safety_checker: true,\n            safety_tolerance: params.safety_tolerance || 2,\n            ...params.negative_prompt && {\n                negative_prompt: params.negative_prompt\n            },\n            ...params.seed && {\n                seed: params.seed\n            },\n            ...size\n        };\n        const startTime = Date.now();\n        // 调用fal.ai API\n        const result = await _fal_ai_serverless_client__WEBPACK_IMPORTED_MODULE_0__.subscribe(modelEndpoint, {\n            input: requestParams,\n            logs: true,\n            onQueueUpdate: (update)=>{\n                console.log('Queue update:', update);\n            }\n        });\n        const generationTime = Date.now() - startTime;\n        if (result.data && result.data.images && result.data.images.length > 0) {\n            return {\n                success: true,\n                images: result.data.images.map((img)=>({\n                        url: img.url,\n                        width: img.width || size.width,\n                        height: img.height || size.height,\n                        content_type: img.content_type || 'image/jpeg'\n                    })),\n                model_used: model,\n                generation_time: generationTime,\n                seed: result.data.seed\n            };\n        } else {\n            throw new Error('No images generated');\n        }\n    } catch (error) {\n        console.error('Image generation error:', error);\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : 'Unknown error occurred',\n            model_used: params.model || 'flux-schnell'\n        };\n    }\n}\n// 获取模型默认步数\nfunction getDefaultSteps(model) {\n    const stepMap = {\n        'flux-pro': 50,\n        'flux-dev': 50,\n        'flux-schnell': 4,\n        'sd-xl': 30,\n        'sd-3': 28,\n        'playground': 50,\n        'kandinsky': 100\n    };\n    return stepMap[model] || 30;\n}\n// 批量图像生成\nasync function generateMultipleImages(params, count = 4) {\n    try {\n        const batchParams = {\n            ...params,\n            num_images: Math.min(count, 4)\n        };\n        return await generateImage(batchParams);\n    } catch (error) {\n        console.error('Batch image generation error:', error);\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : 'Batch generation failed',\n            model_used: params.model || 'flux-schnell'\n        };\n    }\n}\n// 图像风格预设\nconst STYLE_PRESETS = {\n    'photorealistic': {\n        negative_prompt: 'cartoon, anime, painting, drawing, sketch, low quality, blurry',\n        guidance_scale: 7.5\n    },\n    'artistic': {\n        negative_prompt: 'low quality, blurry, distorted',\n        guidance_scale: 8.0\n    },\n    'anime': {\n        negative_prompt: 'realistic, photograph, low quality, blurry',\n        guidance_scale: 7.0\n    },\n    'cartoon': {\n        negative_prompt: 'realistic, photograph, dark, scary, low quality',\n        guidance_scale: 7.0\n    },\n    'cinematic': {\n        negative_prompt: 'low quality, amateur, snapshot, casual',\n        guidance_scale: 8.5\n    }\n};\n// 应用风格预设\nfunction applyStylePreset(params, style) {\n    const preset = STYLE_PRESETS[style];\n    return {\n        ...params,\n        negative_prompt: preset.negative_prompt,\n        guidance_scale: preset.guidance_scale\n    };\n}\n// 检查fal.ai服务状态\nasync function checkServiceStatus() {\n    try {\n        // 尝试调用一个简单的模型来检查服务状态\n        await _fal_ai_serverless_client__WEBPACK_IMPORTED_MODULE_0__.subscribe('fal-ai/flux/schnell', {\n            input: {\n                prompt: 'test',\n                image_size: 'square',\n                num_images: 1,\n                num_inference_steps: 1\n            },\n            timeout: 5000\n        });\n        return true;\n    } catch (error) {\n        console.error('Service status check failed:', error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/imageGeneration.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@msgpack","vendor-chunks/@fal-ai","vendor-chunks/eventsource-parser","vendor-chunks/robot3"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();