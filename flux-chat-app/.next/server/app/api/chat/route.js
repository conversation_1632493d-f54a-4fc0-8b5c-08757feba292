/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_liupeng_cursor_Flux_flux_chat_app_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/route.ts */ \"(rsc)/./src/app/api/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/api/chat/route.ts\",\n    nextConfigOutput,\n    userland: _Users_liupeng_cursor_Flux_flux_chat_app_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/chat/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/chat/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_openai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/openai */ \"(rsc)/./src/lib/openai.ts\");\n/* harmony import */ var _lib_promptOptimizer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/promptOptimizer */ \"(rsc)/./src/lib/promptOptimizer.ts\");\n\n\n\nasync function POST(request) {\n    try {\n        const { message, conversationHistory } = await request.json();\n        if (!message) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Message is required'\n            }, {\n                status: 400\n            });\n        }\n        // 分析用户输入中的关键词\n        const keywords = (0,_lib_promptOptimizer__WEBPACK_IMPORTED_MODULE_2__.analyzeKeywords)(message);\n        // 从对话历史中提取图像需求\n        const extractedRequirement = (0,_lib_promptOptimizer__WEBPACK_IMPORTED_MODULE_2__.extractImageRequirement)([\n            ...conversationHistory,\n            message\n        ]);\n        // 使用OpenAI分析需求并生成回复\n        const response = await (0,_lib_openai__WEBPACK_IMPORTED_MODULE_1__.analyzeRequirement)(message, conversationHistory);\n        // 检查是否需要进一步澄清\n        const needsClarification = checkIfNeedsClarification(extractedRequirement);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            response,\n            extractedRequirement,\n            keywords,\n            needsClarification,\n            metadata: {\n                type: needsClarification ? 'clarification' : 'analysis',\n                extractedElements: Object.keys(extractedRequirement)\n            }\n        });\n    } catch (error) {\n        console.error('Chat API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n// 检查是否需要进一步澄清\nfunction checkIfNeedsClarification(requirement) {\n    const requiredFields = [\n        'subject',\n        'style',\n        'mood'\n    ];\n    const missingFields = requiredFields.filter((field)=>!requirement[field]);\n    // 如果缺少超过一半的关键信息，需要澄清\n    return missingFields.length > requiredFields.length / 2;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/openai.ts":
/*!***************************!*\
  !*** ./src/lib/openai.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PROMPT_OPTIMIZATION_PROMPT: () => (/* binding */ PROMPT_OPTIMIZATION_PROMPT),\n/* harmony export */   REQUIREMENT_ANALYSIS_PROMPT: () => (/* binding */ REQUIREMENT_ANALYSIS_PROMPT),\n/* harmony export */   analyzeRequirement: () => (/* binding */ analyzeRequirement),\n/* harmony export */   openai: () => (/* binding */ openai),\n/* harmony export */   optimizePrompt: () => (/* binding */ optimizePrompt)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/openai/index.mjs\");\n\n// 初始化OpenAI客户端\nconst openai = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n    apiKey: process.env.OPENAI_API_KEY\n});\n// 系统提示词 - 用于需求理解和澄清\nconst REQUIREMENT_ANALYSIS_PROMPT = `你是一个专业的图像生成助手，专门帮助用户将模糊的图像需求转换为清晰、具体的描述。\n\n你的任务是：\n1. 理解用户的图像生成需求\n2. 识别缺失的关键信息\n3. 通过友好的对话获取更多细节\n4. 提取关键的视觉元素\n\n请关注以下方面：\n- 主题/主体 (subject)\n- 艺术风格 (style) \n- 情绪/氛围 (mood)\n- 色彩方案 (colors)\n- 构图方式 (composition)\n- 光照效果 (lighting)\n- 图像质量要求 (quality)\n- 其他重要元素 (additional elements)\n\n请用自然、友好的语气与用户对话，一次只询问1-2个关键问题，避免让用户感到被审问。`;\n// 系统提示词 - 用于提示词优化\nconst PROMPT_OPTIMIZATION_PROMPT = `你是一个专业的AI图像生成提示词优化专家。你需要将用户的需求转换为高质量的图像生成提示词。\n\n优化原则：\n1. 使用具体、描述性的词汇\n2. 按重要性排序关键词\n3. 包含艺术风格和技术参数\n4. 添加质量增强词汇\n5. 避免模糊或矛盾的描述\n\n提示词结构：\n[主体描述], [风格描述], [构图和视角], [光照和氛围], [色彩方案], [质量增强词汇]\n\n请返回JSON格式的结果，包含：\n- originalPrompt: 原始描述\n- optimizedPrompt: 优化后的提示词\n- improvements: 改进说明数组\n- confidence: 优化信心度(0-1)\n- suggestedParameters: 建议的生成参数`;\n// 分析用户需求并生成澄清问题\nasync function analyzeRequirement(userInput, conversationHistory) {\n    // 检查是否有有效的API密钥\n    if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === 'your_openai_api_key_here') {\n        return generateDemoResponse(userInput, conversationHistory);\n    }\n    try {\n        const response = await openai.chat.completions.create({\n            model: 'gpt-4',\n            messages: [\n                {\n                    role: 'system',\n                    content: REQUIREMENT_ANALYSIS_PROMPT\n                },\n                ...conversationHistory.map((msg, index)=>({\n                        role: index % 2 === 0 ? 'user' : 'assistant',\n                        content: msg\n                    })),\n                {\n                    role: 'user',\n                    content: userInput\n                }\n            ],\n            temperature: 0.7,\n            max_tokens: 500\n        });\n        return response.choices[0]?.message?.content || '';\n    } catch (error) {\n        console.error('Error analyzing requirement:', error);\n        // 回退到演示模式\n        return generateDemoResponse(userInput, conversationHistory);\n    }\n}\n// 演示模式响应生成器\nfunction generateDemoResponse(userInput, conversationHistory) {\n    const input = userInput.toLowerCase();\n    // 简单的关键词检测和响应\n    if (conversationHistory.length === 0) {\n        // 首次交互\n        if (input.includes('猫') || input.includes('cat')) {\n            return '听起来很有趣！您想要什么风格的小猫图像呢？比如：\\n\\n• 写实风格的摄影作品\\n• 可爱的卡通风格\\n• 油画艺术风格\\n• 水彩画风格\\n\\n另外，您希望小猫在什么环境中呢？';\n        } else if (input.includes('风景') || input.includes('landscape')) {\n            return '风景画很棒！请告诉我更多细节：\\n\\n• 您想要什么类型的风景？（山脉、海滩、森林、城市等）\\n• 希望是什么时间？（日出、日落、夜晚等）\\n• 偏好什么艺术风格？\\n• 想要什么样的色彩氛围？';\n        } else if (input.includes('人物') || input.includes('人')) {\n            return '人物画像需要考虑很多细节！请告诉我：\\n\\n• 是肖像特写还是全身像？\\n• 想要什么风格？（写实、卡通、艺术风格等）\\n• 人物的年龄和特征？\\n• 背景环境如何？\\n• 服装风格有什么要求？';\n        } else {\n            return '很好的创意！为了帮您生成最佳的提示词，我需要了解更多细节：\\n\\n• 主要的视觉元素是什么？\\n• 您偏好什么艺术风格？\\n• 希望营造什么样的氛围？\\n• 有特定的色彩偏好吗？\\n\\n请详细描述您的想法！';\n        }\n    } else {\n        // 后续交互\n        if (input.includes('写实') || input.includes('摄影')) {\n            return '写实风格很棒！我建议：\\n\\n• 使用专业摄影术语\\n• 强调细节和质感\\n• 考虑光照效果\\n\\n还有其他细节需要补充吗？比如构图方式或特定的光照效果？';\n        } else if (input.includes('卡通') || input.includes('可爱')) {\n            return '卡通风格会很有趣！建议：\\n\\n• 强调可爱和友好的特征\\n• 使用明亮的色彩\\n• 简化的造型设计\\n\\n您希望是什么样的卡通风格？日式动漫、迪士尼风格，还是其他？';\n        } else if (input.includes('颜色') || input.includes('色彩')) {\n            return '色彩选择很重要！基于您的描述，我建议：\\n\\n• 温暖的色调营造舒适感\\n• 对比色增加视觉冲击\\n• 柔和的色彩营造宁静氛围\\n\\n您有特定的颜色偏好吗？';\n        } else {\n            return '很好！基于我们的对话，我已经收集到足够的信息来优化您的提示词了。让我为您生成一个专业的图像生成提示词！';\n        }\n    }\n}\n// 优化提示词\nasync function optimizePrompt(requirement, conversationContext) {\n    // 检查是否有有效的API密钥\n    if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === 'your_openai_api_key_here') {\n        return generateDemoOptimization(requirement, conversationContext);\n    }\n    try {\n        const response = await openai.chat.completions.create({\n            model: 'gpt-4',\n            messages: [\n                {\n                    role: 'system',\n                    content: PROMPT_OPTIMIZATION_PROMPT\n                },\n                {\n                    role: 'user',\n                    content: `基于以下对话内容和需求，生成优化的图像生成提示词：\n\n对话上下文：\n${conversationContext}\n\n最终需求：\n${requirement}\n\n请返回JSON格式的优化结果。`\n                }\n            ],\n            temperature: 0.3,\n            max_tokens: 800\n        });\n        const content = response.choices[0]?.message?.content || '';\n        try {\n            return JSON.parse(content);\n        } catch  {\n            // 如果JSON解析失败，返回基本结构\n            return {\n                originalPrompt: requirement,\n                optimizedPrompt: content,\n                improvements: [\n                    'Generated optimized prompt'\n                ],\n                confidence: 0.8,\n                suggestedParameters: {}\n            };\n        }\n    } catch (error) {\n        console.error('Error optimizing prompt:', error);\n        // 回退到演示模式\n        return generateDemoOptimization(requirement, conversationContext);\n    }\n}\n// 演示模式的提示词优化\nfunction generateDemoOptimization(requirement, conversationContext) {\n    const context = conversationContext.toLowerCase();\n    const req = requirement.toLowerCase();\n    let optimizedPrompt = requirement;\n    const improvements = [];\n    // 基于内容添加优化\n    if (req.includes('猫') || req.includes('cat')) {\n        optimizedPrompt = `a cute cat, highly detailed, professional photography, soft lighting, warm colors, cozy atmosphere, sharp focus, 8k resolution`;\n        improvements.push('添加了专业摄影术语', '增强了可爱特征描述', '指定了光照和色彩');\n    } else if (req.includes('风景') || req.includes('landscape')) {\n        optimizedPrompt = `beautiful landscape, cinematic composition, golden hour lighting, vibrant colors, highly detailed, professional photography, award winning, masterpiece`;\n        improvements.push('添加了电影级构图', '指定了黄金时段光照', '增加了质量增强词汇');\n    } else if (req.includes('人物') || req.includes('portrait')) {\n        optimizedPrompt = `portrait photography, professional lighting, detailed facial features, high quality, sharp focus, artistic composition, studio lighting`;\n        improvements.push('添加了肖像摄影术语', '强调了面部细节', '指定了专业光照');\n    } else {\n        // 通用优化\n        optimizedPrompt = `${requirement}, highly detailed, professional quality, sharp focus, beautiful composition, award winning, masterpiece`;\n        improvements.push('添加了质量增强词汇', '改善了整体描述结构');\n    }\n    // 基于对话上下文进一步优化\n    if (context.includes('写实') || context.includes('摄影')) {\n        optimizedPrompt += ', photorealistic, hyperrealistic';\n        improvements.push('增强了写实风格描述');\n    } else if (context.includes('卡通') || context.includes('动漫')) {\n        optimizedPrompt += ', cartoon style, animated, stylized art';\n        improvements.push('添加了卡通风格描述');\n    } else if (context.includes('油画')) {\n        optimizedPrompt += ', oil painting, classical art, painterly style';\n        improvements.push('添加了油画风格描述');\n    }\n    return {\n        originalPrompt: requirement,\n        optimizedPrompt,\n        improvements,\n        confidence: 0.85,\n        suggestedParameters: {\n            aspectRatio: '16:9',\n            style: context.includes('写实') ? 'photorealistic' : 'artistic',\n            quality: 'high'\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/openai.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/promptOptimizer.ts":
/*!************************************!*\
  !*** ./src/lib/promptOptimizer.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyzeKeywords: () => (/* binding */ analyzeKeywords),\n/* harmony export */   extractImageRequirement: () => (/* binding */ extractImageRequirement),\n/* harmony export */   generateOptimizedPrompt: () => (/* binding */ generateOptimizedPrompt)\n/* harmony export */ });\n// 艺术风格映射\nconst STYLE_MAPPINGS = {\n    '写实': 'photorealistic, hyperrealistic, detailed',\n    '卡通': 'cartoon style, animated, stylized',\n    '油画': 'oil painting, classical art, painterly',\n    '水彩': 'watercolor, soft brushstrokes, flowing',\n    '素描': 'pencil sketch, charcoal drawing, monochrome',\n    '数字艺术': 'digital art, concept art, modern',\n    '像素艺术': 'pixel art, 8-bit, retro gaming style',\n    '抽象': 'abstract art, non-representational, artistic',\n    '极简': 'minimalist, clean, simple composition',\n    '赛博朋克': 'cyberpunk, neon lights, futuristic, dystopian'\n};\n// 情绪氛围映射\nconst MOOD_MAPPINGS = {\n    '温暖': 'warm, cozy, inviting atmosphere',\n    '冷酷': 'cold, stark, dramatic lighting',\n    '神秘': 'mysterious, enigmatic, atmospheric',\n    '浪漫': 'romantic, dreamy, soft lighting',\n    '史诗': 'epic, grand, cinematic',\n    '宁静': 'peaceful, serene, calm',\n    '活力': 'energetic, vibrant, dynamic',\n    '忧郁': 'melancholic, moody, contemplative',\n    '恐怖': 'horror, dark, ominous',\n    '欢乐': 'joyful, cheerful, bright'\n};\n// 质量增强词汇\nconst QUALITY_ENHANCERS = [\n    'highly detailed',\n    'sharp focus',\n    'professional photography',\n    '8k resolution',\n    'award winning',\n    'masterpiece',\n    'trending on artstation',\n    'perfect composition'\n];\n// 光照效果映射\nconst LIGHTING_MAPPINGS = {\n    '自然光': 'natural lighting, soft daylight',\n    '金色时光': 'golden hour, warm sunset lighting',\n    '蓝色时光': 'blue hour, twilight, cool tones',\n    '戏剧性': 'dramatic lighting, high contrast',\n    '柔和': 'soft lighting, diffused light',\n    '背光': 'backlighting, rim light, silhouette',\n    '霓虹': 'neon lighting, colorful glow',\n    '月光': 'moonlight, nocturnal, ethereal',\n    '工作室': 'studio lighting, professional setup',\n    '环境光': 'ambient lighting, atmospheric'\n};\n// 构图方式映射\nconst COMPOSITION_MAPPINGS = {\n    '特写': 'close-up, detailed portrait',\n    '全身': 'full body shot, complete figure',\n    '中景': 'medium shot, waist up',\n    '远景': 'wide shot, establishing shot',\n    '鸟瞰': 'aerial view, top-down perspective',\n    '低角度': 'low angle, dramatic perspective',\n    '对称': 'symmetrical composition, balanced',\n    '三分法': 'rule of thirds, dynamic composition',\n    '中心构图': 'centered composition, focal point',\n    '对角线': 'diagonal composition, dynamic lines'\n};\n// 从对话中提取图像需求\nfunction extractImageRequirement(conversationHistory) {\n    const fullText = conversationHistory.join(' ').toLowerCase();\n    const requirement = {};\n    // 提取风格\n    for (const [key, value] of Object.entries(STYLE_MAPPINGS)){\n        if (fullText.includes(key.toLowerCase()) || fullText.includes(value.toLowerCase())) {\n            requirement.style = key;\n            break;\n        }\n    }\n    // 提取情绪\n    for (const [key, value] of Object.entries(MOOD_MAPPINGS)){\n        if (fullText.includes(key.toLowerCase())) {\n            requirement.mood = key;\n            break;\n        }\n    }\n    // 提取颜色\n    const colors = [\n        '红',\n        '蓝',\n        '绿',\n        '黄',\n        '紫',\n        '橙',\n        '粉',\n        '黑',\n        '白',\n        '灰'\n    ];\n    const foundColors = colors.filter((color)=>fullText.includes(color));\n    if (foundColors.length > 0) {\n        requirement.colors = foundColors;\n    }\n    return requirement;\n}\n// 生成优化的提示词\nfunction generateOptimizedPrompt(requirement, originalPrompt) {\n    const promptParts = [];\n    const improvements = [];\n    // 添加主体描述\n    if (requirement.subject) {\n        promptParts.push(requirement.subject);\n    }\n    // 添加风格描述\n    if (requirement.style && STYLE_MAPPINGS[requirement.style]) {\n        const styleDesc = STYLE_MAPPINGS[requirement.style];\n        promptParts.push(styleDesc);\n        improvements.push(`添加了${requirement.style}风格的专业描述`);\n    }\n    // 添加构图描述\n    if (requirement.composition && COMPOSITION_MAPPINGS[requirement.composition]) {\n        const compDesc = COMPOSITION_MAPPINGS[requirement.composition];\n        promptParts.push(compDesc);\n        improvements.push(`优化了构图描述`);\n    }\n    // 添加光照效果\n    if (requirement.lighting && LIGHTING_MAPPINGS[requirement.lighting]) {\n        const lightDesc = LIGHTING_MAPPINGS[requirement.lighting];\n        promptParts.push(lightDesc);\n        improvements.push(`增强了光照效果描述`);\n    }\n    // 添加情绪氛围\n    if (requirement.mood && MOOD_MAPPINGS[requirement.mood]) {\n        const moodDesc = MOOD_MAPPINGS[requirement.mood];\n        promptParts.push(moodDesc);\n        improvements.push(`添加了${requirement.mood}氛围描述`);\n    }\n    // 添加颜色方案\n    if (requirement.colors && requirement.colors.length > 0) {\n        const colorDesc = `${requirement.colors.join(', ')} color scheme`;\n        promptParts.push(colorDesc);\n        improvements.push(`指定了颜色方案`);\n    }\n    // 添加质量增强词汇\n    const qualityEnhancers = QUALITY_ENHANCERS.slice(0, 3).join(', ');\n    promptParts.push(qualityEnhancers);\n    improvements.push('添加了质量增强词汇');\n    const optimizedPrompt = promptParts.join(', ');\n    // 计算信心度\n    const confidence = Math.min(0.9, 0.5 + improvements.length * 0.1);\n    return {\n        originalPrompt,\n        optimizedPrompt,\n        improvements,\n        confidence,\n        suggestedParameters: {\n            aspectRatio: '16:9',\n            style: requirement.style || 'auto',\n            quality: 'high'\n        }\n    };\n}\n// 分析用户输入中的关键词\nfunction analyzeKeywords(input) {\n    const text = input.toLowerCase();\n    const subjects = [];\n    const styles = [];\n    const moods = [];\n    const colors = [];\n    // 检测风格关键词\n    Object.keys(STYLE_MAPPINGS).forEach((style)=>{\n        if (text.includes(style.toLowerCase())) {\n            styles.push(style);\n        }\n    });\n    // 检测情绪关键词\n    Object.keys(MOOD_MAPPINGS).forEach((mood)=>{\n        if (text.includes(mood.toLowerCase())) {\n            moods.push(mood);\n        }\n    });\n    // 检测颜色关键词\n    const colorKeywords = [\n        '红',\n        '蓝',\n        '绿',\n        '黄',\n        '紫',\n        '橙',\n        '粉',\n        '黑',\n        '白',\n        '灰'\n    ];\n    colorKeywords.forEach((color)=>{\n        if (text.includes(color)) {\n            colors.push(color);\n        }\n    });\n    return {\n        subjects,\n        styles,\n        moods,\n        colors\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3Byb21wdE9wdGltaXplci50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFFQSxTQUFTO0FBQ1QsTUFBTUEsaUJBQWlCO0lBQ3JCLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sUUFBUTtJQUNSLFFBQVE7SUFDUixNQUFNO0lBQ04sTUFBTTtJQUNOLFFBQVE7QUFDVjtBQUVBLFNBQVM7QUFDVCxNQUFNQyxnQkFBZ0I7SUFDcEIsTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtBQUNSO0FBRUEsU0FBUztBQUNULE1BQU1DLG9CQUFvQjtJQUN4QjtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0NBQ0Q7QUFFRCxTQUFTO0FBQ1QsTUFBTUMsb0JBQW9CO0lBQ3hCLE9BQU87SUFDUCxRQUFRO0lBQ1IsUUFBUTtJQUNSLE9BQU87SUFDUCxNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sT0FBTztJQUNQLE9BQU87QUFDVDtBQUVBLFNBQVM7QUFDVCxNQUFNQyx1QkFBdUI7SUFDM0IsTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixPQUFPO0lBQ1AsTUFBTTtJQUNOLE9BQU87SUFDUCxRQUFRO0lBQ1IsT0FBTztBQUNUO0FBRUEsYUFBYTtBQUNOLFNBQVNDLHdCQUF3QkMsbUJBQTZCO0lBQ25FLE1BQU1DLFdBQVdELG9CQUFvQkUsSUFBSSxDQUFDLEtBQUtDLFdBQVc7SUFFMUQsTUFBTUMsY0FBeUMsQ0FBQztJQUVoRCxPQUFPO0lBQ1AsS0FBSyxNQUFNLENBQUNDLEtBQUtDLE1BQU0sSUFBSUMsT0FBT0MsT0FBTyxDQUFDZCxnQkFBaUI7UUFDekQsSUFBSU8sU0FBU1EsUUFBUSxDQUFDSixJQUFJRixXQUFXLE9BQU9GLFNBQVNRLFFBQVEsQ0FBQ0gsTUFBTUgsV0FBVyxLQUFLO1lBQ2xGQyxZQUFZTSxLQUFLLEdBQUdMO1lBQ3BCO1FBQ0Y7SUFDRjtJQUVBLE9BQU87SUFDUCxLQUFLLE1BQU0sQ0FBQ0EsS0FBS0MsTUFBTSxJQUFJQyxPQUFPQyxPQUFPLENBQUNiLGVBQWdCO1FBQ3hELElBQUlNLFNBQVNRLFFBQVEsQ0FBQ0osSUFBSUYsV0FBVyxLQUFLO1lBQ3hDQyxZQUFZTyxJQUFJLEdBQUdOO1lBQ25CO1FBQ0Y7SUFDRjtJQUVBLE9BQU87SUFDUCxNQUFNTyxTQUFTO1FBQUM7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7S0FBSTtJQUNqRSxNQUFNQyxjQUFjRCxPQUFPRSxNQUFNLENBQUNDLENBQUFBLFFBQVNkLFNBQVNRLFFBQVEsQ0FBQ007SUFDN0QsSUFBSUYsWUFBWUcsTUFBTSxHQUFHLEdBQUc7UUFDMUJaLFlBQVlRLE1BQU0sR0FBR0M7SUFDdkI7SUFFQSxPQUFPVDtBQUNUO0FBRUEsV0FBVztBQUNKLFNBQVNhLHdCQUNkYixXQUFzQyxFQUN0Q2MsY0FBc0I7SUFFdEIsTUFBTUMsY0FBd0IsRUFBRTtJQUNoQyxNQUFNQyxlQUF5QixFQUFFO0lBRWpDLFNBQVM7SUFDVCxJQUFJaEIsWUFBWWlCLE9BQU8sRUFBRTtRQUN2QkYsWUFBWUcsSUFBSSxDQUFDbEIsWUFBWWlCLE9BQU87SUFDdEM7SUFFQSxTQUFTO0lBQ1QsSUFBSWpCLFlBQVlNLEtBQUssSUFBSWhCLGNBQWMsQ0FBQ1UsWUFBWU0sS0FBSyxDQUFnQyxFQUFFO1FBQ3pGLE1BQU1hLFlBQVk3QixjQUFjLENBQUNVLFlBQVlNLEtBQUssQ0FBZ0M7UUFDbEZTLFlBQVlHLElBQUksQ0FBQ0M7UUFDakJILGFBQWFFLElBQUksQ0FBQyxDQUFDLEdBQUcsRUFBRWxCLFlBQVlNLEtBQUssQ0FBQyxPQUFPLENBQUM7SUFDcEQ7SUFFQSxTQUFTO0lBQ1QsSUFBSU4sWUFBWW9CLFdBQVcsSUFBSTFCLG9CQUFvQixDQUFDTSxZQUFZb0IsV0FBVyxDQUFzQyxFQUFFO1FBQ2pILE1BQU1DLFdBQVczQixvQkFBb0IsQ0FBQ00sWUFBWW9CLFdBQVcsQ0FBc0M7UUFDbkdMLFlBQVlHLElBQUksQ0FBQ0c7UUFDakJMLGFBQWFFLElBQUksQ0FBQyxDQUFDLE9BQU8sQ0FBQztJQUM3QjtJQUVBLFNBQVM7SUFDVCxJQUFJbEIsWUFBWXNCLFFBQVEsSUFBSTdCLGlCQUFpQixDQUFDTyxZQUFZc0IsUUFBUSxDQUFtQyxFQUFFO1FBQ3JHLE1BQU1DLFlBQVk5QixpQkFBaUIsQ0FBQ08sWUFBWXNCLFFBQVEsQ0FBbUM7UUFDM0ZQLFlBQVlHLElBQUksQ0FBQ0s7UUFDakJQLGFBQWFFLElBQUksQ0FBQyxDQUFDLFNBQVMsQ0FBQztJQUMvQjtJQUVBLFNBQVM7SUFDVCxJQUFJbEIsWUFBWU8sSUFBSSxJQUFJaEIsYUFBYSxDQUFDUyxZQUFZTyxJQUFJLENBQStCLEVBQUU7UUFDckYsTUFBTWlCLFdBQVdqQyxhQUFhLENBQUNTLFlBQVlPLElBQUksQ0FBK0I7UUFDOUVRLFlBQVlHLElBQUksQ0FBQ007UUFDakJSLGFBQWFFLElBQUksQ0FBQyxDQUFDLEdBQUcsRUFBRWxCLFlBQVlPLElBQUksQ0FBQyxJQUFJLENBQUM7SUFDaEQ7SUFFQSxTQUFTO0lBQ1QsSUFBSVAsWUFBWVEsTUFBTSxJQUFJUixZQUFZUSxNQUFNLENBQUNJLE1BQU0sR0FBRyxHQUFHO1FBQ3ZELE1BQU1hLFlBQVksR0FBR3pCLFlBQVlRLE1BQU0sQ0FBQ1YsSUFBSSxDQUFDLE1BQU0sYUFBYSxDQUFDO1FBQ2pFaUIsWUFBWUcsSUFBSSxDQUFDTztRQUNqQlQsYUFBYUUsSUFBSSxDQUFDLENBQUMsT0FBTyxDQUFDO0lBQzdCO0lBRUEsV0FBVztJQUNYLE1BQU1RLG1CQUFtQmxDLGtCQUFrQm1DLEtBQUssQ0FBQyxHQUFHLEdBQUc3QixJQUFJLENBQUM7SUFDNURpQixZQUFZRyxJQUFJLENBQUNRO0lBQ2pCVixhQUFhRSxJQUFJLENBQUM7SUFFbEIsTUFBTVUsa0JBQWtCYixZQUFZakIsSUFBSSxDQUFDO0lBRXpDLFFBQVE7SUFDUixNQUFNK0IsYUFBYUMsS0FBS0MsR0FBRyxDQUFDLEtBQUssTUFBT2YsYUFBYUosTUFBTSxHQUFHO0lBRTlELE9BQU87UUFDTEU7UUFDQWM7UUFDQVo7UUFDQWE7UUFDQUcscUJBQXFCO1lBQ25CQyxhQUFhO1lBQ2IzQixPQUFPTixZQUFZTSxLQUFLLElBQUk7WUFDNUI0QixTQUFTO1FBQ1g7SUFDRjtBQUNGO0FBRUEsY0FBYztBQUNQLFNBQVNDLGdCQUFnQkMsS0FBYTtJQU0zQyxNQUFNQyxPQUFPRCxNQUFNckMsV0FBVztJQUU5QixNQUFNdUMsV0FBcUIsRUFBRTtJQUM3QixNQUFNQyxTQUFtQixFQUFFO0lBQzNCLE1BQU1DLFFBQWtCLEVBQUU7SUFDMUIsTUFBTWhDLFNBQW1CLEVBQUU7SUFFM0IsVUFBVTtJQUNWTCxPQUFPc0MsSUFBSSxDQUFDbkQsZ0JBQWdCb0QsT0FBTyxDQUFDcEMsQ0FBQUE7UUFDbEMsSUFBSStCLEtBQUtoQyxRQUFRLENBQUNDLE1BQU1QLFdBQVcsS0FBSztZQUN0Q3dDLE9BQU9yQixJQUFJLENBQUNaO1FBQ2Q7SUFDRjtJQUVBLFVBQVU7SUFDVkgsT0FBT3NDLElBQUksQ0FBQ2xELGVBQWVtRCxPQUFPLENBQUNuQyxDQUFBQTtRQUNqQyxJQUFJOEIsS0FBS2hDLFFBQVEsQ0FBQ0UsS0FBS1IsV0FBVyxLQUFLO1lBQ3JDeUMsTUFBTXRCLElBQUksQ0FBQ1g7UUFDYjtJQUNGO0lBRUEsVUFBVTtJQUNWLE1BQU1vQyxnQkFBZ0I7UUFBQztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztLQUFJO0lBQ3hFQSxjQUFjRCxPQUFPLENBQUMvQixDQUFBQTtRQUNwQixJQUFJMEIsS0FBS2hDLFFBQVEsQ0FBQ00sUUFBUTtZQUN4QkgsT0FBT1UsSUFBSSxDQUFDUDtRQUNkO0lBQ0Y7SUFFQSxPQUFPO1FBQUUyQjtRQUFVQztRQUFRQztRQUFPaEM7SUFBTztBQUMzQyIsInNvdXJjZXMiOlsiL1VzZXJzL2xpdXBlbmcvY3Vyc29yL+i/m+mYti9GbHV45a+56K+d5byP5bqU55SoL2ZsdXgtY2hhdC1hcHAvc3JjL2xpYi9wcm9tcHRPcHRpbWl6ZXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSW1hZ2VSZXF1aXJlbWVudCwgUHJvbXB0T3B0aW1pemF0aW9uIH0gZnJvbSAnLi90eXBlcyc7XG5cbi8vIOiJuuacr+mjjuagvOaYoOWwhFxuY29uc3QgU1RZTEVfTUFQUElOR1MgPSB7XG4gICflhpnlrp4nOiAncGhvdG9yZWFsaXN0aWMsIGh5cGVycmVhbGlzdGljLCBkZXRhaWxlZCcsXG4gICfljaHpgJonOiAnY2FydG9vbiBzdHlsZSwgYW5pbWF0ZWQsIHN0eWxpemVkJyxcbiAgJ+ayueeUuyc6ICdvaWwgcGFpbnRpbmcsIGNsYXNzaWNhbCBhcnQsIHBhaW50ZXJseScsXG4gICfmsLTlvaknOiAnd2F0ZXJjb2xvciwgc29mdCBicnVzaHN0cm9rZXMsIGZsb3dpbmcnLFxuICAn57Sg5o+PJzogJ3BlbmNpbCBza2V0Y2gsIGNoYXJjb2FsIGRyYXdpbmcsIG1vbm9jaHJvbWUnLFxuICAn5pWw5a2X6Im65pyvJzogJ2RpZ2l0YWwgYXJ0LCBjb25jZXB0IGFydCwgbW9kZXJuJyxcbiAgJ+WDj+e0oOiJuuacryc6ICdwaXhlbCBhcnQsIDgtYml0LCByZXRybyBnYW1pbmcgc3R5bGUnLFxuICAn5oq96LGhJzogJ2Fic3RyYWN0IGFydCwgbm9uLXJlcHJlc2VudGF0aW9uYWwsIGFydGlzdGljJyxcbiAgJ+aegeeugCc6ICdtaW5pbWFsaXN0LCBjbGVhbiwgc2ltcGxlIGNvbXBvc2l0aW9uJyxcbiAgJ+i1m+WNmuaci+WFiyc6ICdjeWJlcnB1bmssIG5lb24gbGlnaHRzLCBmdXR1cmlzdGljLCBkeXN0b3BpYW4nXG59O1xuXG4vLyDmg4Xnu6rmsJvlm7TmmKDlsIRcbmNvbnN0IE1PT0RfTUFQUElOR1MgPSB7XG4gICfmuKnmmpYnOiAnd2FybSwgY296eSwgaW52aXRpbmcgYXRtb3NwaGVyZScsXG4gICflhrfphbcnOiAnY29sZCwgc3RhcmssIGRyYW1hdGljIGxpZ2h0aW5nJyxcbiAgJ+elnuenmCc6ICdteXN0ZXJpb3VzLCBlbmlnbWF0aWMsIGF0bW9zcGhlcmljJyxcbiAgJ+a1qua8qyc6ICdyb21hbnRpYywgZHJlYW15LCBzb2Z0IGxpZ2h0aW5nJyxcbiAgJ+WPsuivlyc6ICdlcGljLCBncmFuZCwgY2luZW1hdGljJyxcbiAgJ+WugemdmSc6ICdwZWFjZWZ1bCwgc2VyZW5lLCBjYWxtJyxcbiAgJ+a0u+WKmyc6ICdlbmVyZ2V0aWMsIHZpYnJhbnQsIGR5bmFtaWMnLFxuICAn5b+n6YOBJzogJ21lbGFuY2hvbGljLCBtb29keSwgY29udGVtcGxhdGl2ZScsXG4gICfmgZDmgJYnOiAnaG9ycm9yLCBkYXJrLCBvbWlub3VzJyxcbiAgJ+asouS5kCc6ICdqb3lmdWwsIGNoZWVyZnVsLCBicmlnaHQnXG59O1xuXG4vLyDotKjph4/lop7lvLror43msYdcbmNvbnN0IFFVQUxJVFlfRU5IQU5DRVJTID0gW1xuICAnaGlnaGx5IGRldGFpbGVkJyxcbiAgJ3NoYXJwIGZvY3VzJyxcbiAgJ3Byb2Zlc3Npb25hbCBwaG90b2dyYXBoeScsXG4gICc4ayByZXNvbHV0aW9uJyxcbiAgJ2F3YXJkIHdpbm5pbmcnLFxuICAnbWFzdGVycGllY2UnLFxuICAndHJlbmRpbmcgb24gYXJ0c3RhdGlvbicsXG4gICdwZXJmZWN0IGNvbXBvc2l0aW9uJ1xuXTtcblxuLy8g5YWJ54Wn5pWI5p6c5pig5bCEXG5jb25zdCBMSUdIVElOR19NQVBQSU5HUyA9IHtcbiAgJ+iHqueEtuWFiSc6ICduYXR1cmFsIGxpZ2h0aW5nLCBzb2Z0IGRheWxpZ2h0JyxcbiAgJ+mHkeiJsuaXtuWFiSc6ICdnb2xkZW4gaG91ciwgd2FybSBzdW5zZXQgbGlnaHRpbmcnLFxuICAn6JOd6Imy5pe25YWJJzogJ2JsdWUgaG91ciwgdHdpbGlnaHQsIGNvb2wgdG9uZXMnLFxuICAn5oiP5Ymn5oCnJzogJ2RyYW1hdGljIGxpZ2h0aW5nLCBoaWdoIGNvbnRyYXN0JyxcbiAgJ+aflOWSjCc6ICdzb2Z0IGxpZ2h0aW5nLCBkaWZmdXNlZCBsaWdodCcsXG4gICfog4zlhYknOiAnYmFja2xpZ2h0aW5nLCByaW0gbGlnaHQsIHNpbGhvdWV0dGUnLFxuICAn6ZyT6Jm5JzogJ25lb24gbGlnaHRpbmcsIGNvbG9yZnVsIGdsb3cnLFxuICAn5pyI5YWJJzogJ21vb25saWdodCwgbm9jdHVybmFsLCBldGhlcmVhbCcsXG4gICflt6XkvZzlrqQnOiAnc3R1ZGlvIGxpZ2h0aW5nLCBwcm9mZXNzaW9uYWwgc2V0dXAnLFxuICAn546v5aKD5YWJJzogJ2FtYmllbnQgbGlnaHRpbmcsIGF0bW9zcGhlcmljJ1xufTtcblxuLy8g5p6E5Zu+5pa55byP5pig5bCEXG5jb25zdCBDT01QT1NJVElPTl9NQVBQSU5HUyA9IHtcbiAgJ+eJueWGmSc6ICdjbG9zZS11cCwgZGV0YWlsZWQgcG9ydHJhaXQnLFxuICAn5YWo6LqrJzogJ2Z1bGwgYm9keSBzaG90LCBjb21wbGV0ZSBmaWd1cmUnLFxuICAn5Lit5pmvJzogJ21lZGl1bSBzaG90LCB3YWlzdCB1cCcsXG4gICfov5zmma8nOiAnd2lkZSBzaG90LCBlc3RhYmxpc2hpbmcgc2hvdCcsXG4gICfpuJ/nnrAnOiAnYWVyaWFsIHZpZXcsIHRvcC1kb3duIHBlcnNwZWN0aXZlJyxcbiAgJ+S9juinkuW6pic6ICdsb3cgYW5nbGUsIGRyYW1hdGljIHBlcnNwZWN0aXZlJyxcbiAgJ+WvueensCc6ICdzeW1tZXRyaWNhbCBjb21wb3NpdGlvbiwgYmFsYW5jZWQnLFxuICAn5LiJ5YiG5rOVJzogJ3J1bGUgb2YgdGhpcmRzLCBkeW5hbWljIGNvbXBvc2l0aW9uJyxcbiAgJ+S4reW/g+aehOWbvic6ICdjZW50ZXJlZCBjb21wb3NpdGlvbiwgZm9jYWwgcG9pbnQnLFxuICAn5a+56KeS57q/JzogJ2RpYWdvbmFsIGNvbXBvc2l0aW9uLCBkeW5hbWljIGxpbmVzJ1xufTtcblxuLy8g5LuO5a+56K+d5Lit5o+Q5Y+W5Zu+5YOP6ZyA5rGCXG5leHBvcnQgZnVuY3Rpb24gZXh0cmFjdEltYWdlUmVxdWlyZW1lbnQoY29udmVyc2F0aW9uSGlzdG9yeTogc3RyaW5nW10pOiBQYXJ0aWFsPEltYWdlUmVxdWlyZW1lbnQ+IHtcbiAgY29uc3QgZnVsbFRleHQgPSBjb252ZXJzYXRpb25IaXN0b3J5LmpvaW4oJyAnKS50b0xvd2VyQ2FzZSgpO1xuICBcbiAgY29uc3QgcmVxdWlyZW1lbnQ6IFBhcnRpYWw8SW1hZ2VSZXF1aXJlbWVudD4gPSB7fTtcbiAgXG4gIC8vIOaPkOWPlumjjuagvFxuICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBPYmplY3QuZW50cmllcyhTVFlMRV9NQVBQSU5HUykpIHtcbiAgICBpZiAoZnVsbFRleHQuaW5jbHVkZXMoa2V5LnRvTG93ZXJDYXNlKCkpIHx8IGZ1bGxUZXh0LmluY2x1ZGVzKHZhbHVlLnRvTG93ZXJDYXNlKCkpKSB7XG4gICAgICByZXF1aXJlbWVudC5zdHlsZSA9IGtleTtcbiAgICAgIGJyZWFrO1xuICAgIH1cbiAgfVxuICBcbiAgLy8g5o+Q5Y+W5oOF57uqXG4gIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKE1PT0RfTUFQUElOR1MpKSB7XG4gICAgaWYgKGZ1bGxUZXh0LmluY2x1ZGVzKGtleS50b0xvd2VyQ2FzZSgpKSkge1xuICAgICAgcmVxdWlyZW1lbnQubW9vZCA9IGtleTtcbiAgICAgIGJyZWFrO1xuICAgIH1cbiAgfVxuICBcbiAgLy8g5o+Q5Y+W6aKc6ImyXG4gIGNvbnN0IGNvbG9ycyA9IFsn57qiJywgJ+iTnScsICfnu78nLCAn6buEJywgJ+e0qycsICfmqZknLCAn57KJJywgJ+m7kScsICfnmb0nLCAn54GwJ107XG4gIGNvbnN0IGZvdW5kQ29sb3JzID0gY29sb3JzLmZpbHRlcihjb2xvciA9PiBmdWxsVGV4dC5pbmNsdWRlcyhjb2xvcikpO1xuICBpZiAoZm91bmRDb2xvcnMubGVuZ3RoID4gMCkge1xuICAgIHJlcXVpcmVtZW50LmNvbG9ycyA9IGZvdW5kQ29sb3JzO1xuICB9XG4gIFxuICByZXR1cm4gcmVxdWlyZW1lbnQ7XG59XG5cbi8vIOeUn+aIkOS8mOWMlueahOaPkOekuuivjVxuZXhwb3J0IGZ1bmN0aW9uIGdlbmVyYXRlT3B0aW1pemVkUHJvbXB0KFxuICByZXF1aXJlbWVudDogUGFydGlhbDxJbWFnZVJlcXVpcmVtZW50PixcbiAgb3JpZ2luYWxQcm9tcHQ6IHN0cmluZ1xuKTogUHJvbXB0T3B0aW1pemF0aW9uIHtcbiAgY29uc3QgcHJvbXB0UGFydHM6IHN0cmluZ1tdID0gW107XG4gIGNvbnN0IGltcHJvdmVtZW50czogc3RyaW5nW10gPSBbXTtcbiAgXG4gIC8vIOa3u+WKoOS4u+S9k+aPj+i/sFxuICBpZiAocmVxdWlyZW1lbnQuc3ViamVjdCkge1xuICAgIHByb21wdFBhcnRzLnB1c2gocmVxdWlyZW1lbnQuc3ViamVjdCk7XG4gIH1cbiAgXG4gIC8vIOa3u+WKoOmjjuagvOaPj+i/sFxuICBpZiAocmVxdWlyZW1lbnQuc3R5bGUgJiYgU1RZTEVfTUFQUElOR1NbcmVxdWlyZW1lbnQuc3R5bGUgYXMga2V5b2YgdHlwZW9mIFNUWUxFX01BUFBJTkdTXSkge1xuICAgIGNvbnN0IHN0eWxlRGVzYyA9IFNUWUxFX01BUFBJTkdTW3JlcXVpcmVtZW50LnN0eWxlIGFzIGtleW9mIHR5cGVvZiBTVFlMRV9NQVBQSU5HU107XG4gICAgcHJvbXB0UGFydHMucHVzaChzdHlsZURlc2MpO1xuICAgIGltcHJvdmVtZW50cy5wdXNoKGDmt7vliqDkuoYke3JlcXVpcmVtZW50LnN0eWxlfemjjuagvOeahOS4k+S4muaPj+i/sGApO1xuICB9XG4gIFxuICAvLyDmt7vliqDmnoTlm77mj4/ov7BcbiAgaWYgKHJlcXVpcmVtZW50LmNvbXBvc2l0aW9uICYmIENPTVBPU0lUSU9OX01BUFBJTkdTW3JlcXVpcmVtZW50LmNvbXBvc2l0aW9uIGFzIGtleW9mIHR5cGVvZiBDT01QT1NJVElPTl9NQVBQSU5HU10pIHtcbiAgICBjb25zdCBjb21wRGVzYyA9IENPTVBPU0lUSU9OX01BUFBJTkdTW3JlcXVpcmVtZW50LmNvbXBvc2l0aW9uIGFzIGtleW9mIHR5cGVvZiBDT01QT1NJVElPTl9NQVBQSU5HU107XG4gICAgcHJvbXB0UGFydHMucHVzaChjb21wRGVzYyk7XG4gICAgaW1wcm92ZW1lbnRzLnB1c2goYOS8mOWMluS6huaehOWbvuaPj+i/sGApO1xuICB9XG4gIFxuICAvLyDmt7vliqDlhYnnhafmlYjmnpxcbiAgaWYgKHJlcXVpcmVtZW50LmxpZ2h0aW5nICYmIExJR0hUSU5HX01BUFBJTkdTW3JlcXVpcmVtZW50LmxpZ2h0aW5nIGFzIGtleW9mIHR5cGVvZiBMSUdIVElOR19NQVBQSU5HU10pIHtcbiAgICBjb25zdCBsaWdodERlc2MgPSBMSUdIVElOR19NQVBQSU5HU1tyZXF1aXJlbWVudC5saWdodGluZyBhcyBrZXlvZiB0eXBlb2YgTElHSFRJTkdfTUFQUElOR1NdO1xuICAgIHByb21wdFBhcnRzLnB1c2gobGlnaHREZXNjKTtcbiAgICBpbXByb3ZlbWVudHMucHVzaChg5aKe5by65LqG5YWJ54Wn5pWI5p6c5o+P6L+wYCk7XG4gIH1cbiAgXG4gIC8vIOa3u+WKoOaDhee7quawm+WbtFxuICBpZiAocmVxdWlyZW1lbnQubW9vZCAmJiBNT09EX01BUFBJTkdTW3JlcXVpcmVtZW50Lm1vb2QgYXMga2V5b2YgdHlwZW9mIE1PT0RfTUFQUElOR1NdKSB7XG4gICAgY29uc3QgbW9vZERlc2MgPSBNT09EX01BUFBJTkdTW3JlcXVpcmVtZW50Lm1vb2QgYXMga2V5b2YgdHlwZW9mIE1PT0RfTUFQUElOR1NdO1xuICAgIHByb21wdFBhcnRzLnB1c2gobW9vZERlc2MpO1xuICAgIGltcHJvdmVtZW50cy5wdXNoKGDmt7vliqDkuoYke3JlcXVpcmVtZW50Lm1vb2R95rCb5Zu05o+P6L+wYCk7XG4gIH1cbiAgXG4gIC8vIOa3u+WKoOminOiJsuaWueahiFxuICBpZiAocmVxdWlyZW1lbnQuY29sb3JzICYmIHJlcXVpcmVtZW50LmNvbG9ycy5sZW5ndGggPiAwKSB7XG4gICAgY29uc3QgY29sb3JEZXNjID0gYCR7cmVxdWlyZW1lbnQuY29sb3JzLmpvaW4oJywgJyl9IGNvbG9yIHNjaGVtZWA7XG4gICAgcHJvbXB0UGFydHMucHVzaChjb2xvckRlc2MpO1xuICAgIGltcHJvdmVtZW50cy5wdXNoKGDmjIflrprkuobpopzoibLmlrnmoYhgKTtcbiAgfVxuICBcbiAgLy8g5re75Yqg6LSo6YeP5aKe5by66K+N5rGHXG4gIGNvbnN0IHF1YWxpdHlFbmhhbmNlcnMgPSBRVUFMSVRZX0VOSEFOQ0VSUy5zbGljZSgwLCAzKS5qb2luKCcsICcpO1xuICBwcm9tcHRQYXJ0cy5wdXNoKHF1YWxpdHlFbmhhbmNlcnMpO1xuICBpbXByb3ZlbWVudHMucHVzaCgn5re75Yqg5LqG6LSo6YeP5aKe5by66K+N5rGHJyk7XG4gIFxuICBjb25zdCBvcHRpbWl6ZWRQcm9tcHQgPSBwcm9tcHRQYXJ0cy5qb2luKCcsICcpO1xuICBcbiAgLy8g6K6h566X5L+h5b+D5bqmXG4gIGNvbnN0IGNvbmZpZGVuY2UgPSBNYXRoLm1pbigwLjksIDAuNSArIChpbXByb3ZlbWVudHMubGVuZ3RoICogMC4xKSk7XG4gIFxuICByZXR1cm4ge1xuICAgIG9yaWdpbmFsUHJvbXB0LFxuICAgIG9wdGltaXplZFByb21wdCxcbiAgICBpbXByb3ZlbWVudHMsXG4gICAgY29uZmlkZW5jZSxcbiAgICBzdWdnZXN0ZWRQYXJhbWV0ZXJzOiB7XG4gICAgICBhc3BlY3RSYXRpbzogJzE2OjknLFxuICAgICAgc3R5bGU6IHJlcXVpcmVtZW50LnN0eWxlIHx8ICdhdXRvJyxcbiAgICAgIHF1YWxpdHk6ICdoaWdoJ1xuICAgIH1cbiAgfTtcbn1cblxuLy8g5YiG5p6Q55So5oi36L6T5YWl5Lit55qE5YWz6ZSu6K+NXG5leHBvcnQgZnVuY3Rpb24gYW5hbHl6ZUtleXdvcmRzKGlucHV0OiBzdHJpbmcpOiB7XG4gIHN1YmplY3RzOiBzdHJpbmdbXTtcbiAgc3R5bGVzOiBzdHJpbmdbXTtcbiAgbW9vZHM6IHN0cmluZ1tdO1xuICBjb2xvcnM6IHN0cmluZ1tdO1xufSB7XG4gIGNvbnN0IHRleHQgPSBpbnB1dC50b0xvd2VyQ2FzZSgpO1xuICBcbiAgY29uc3Qgc3ViamVjdHM6IHN0cmluZ1tdID0gW107XG4gIGNvbnN0IHN0eWxlczogc3RyaW5nW10gPSBbXTtcbiAgY29uc3QgbW9vZHM6IHN0cmluZ1tdID0gW107XG4gIGNvbnN0IGNvbG9yczogc3RyaW5nW10gPSBbXTtcbiAgXG4gIC8vIOajgOa1i+mjjuagvOWFs+mUruivjVxuICBPYmplY3Qua2V5cyhTVFlMRV9NQVBQSU5HUykuZm9yRWFjaChzdHlsZSA9PiB7XG4gICAgaWYgKHRleHQuaW5jbHVkZXMoc3R5bGUudG9Mb3dlckNhc2UoKSkpIHtcbiAgICAgIHN0eWxlcy5wdXNoKHN0eWxlKTtcbiAgICB9XG4gIH0pO1xuICBcbiAgLy8g5qOA5rWL5oOF57uq5YWz6ZSu6K+NXG4gIE9iamVjdC5rZXlzKE1PT0RfTUFQUElOR1MpLmZvckVhY2gobW9vZCA9PiB7XG4gICAgaWYgKHRleHQuaW5jbHVkZXMobW9vZC50b0xvd2VyQ2FzZSgpKSkge1xuICAgICAgbW9vZHMucHVzaChtb29kKTtcbiAgICB9XG4gIH0pO1xuICBcbiAgLy8g5qOA5rWL6aKc6Imy5YWz6ZSu6K+NXG4gIGNvbnN0IGNvbG9yS2V5d29yZHMgPSBbJ+e6oicsICfok50nLCAn57u/JywgJ+m7hCcsICfntKsnLCAn5qmZJywgJ+eyiScsICfpu5EnLCAn55m9JywgJ+eBsCddO1xuICBjb2xvcktleXdvcmRzLmZvckVhY2goY29sb3IgPT4ge1xuICAgIGlmICh0ZXh0LmluY2x1ZGVzKGNvbG9yKSkge1xuICAgICAgY29sb3JzLnB1c2goY29sb3IpO1xuICAgIH1cbiAgfSk7XG4gIFxuICByZXR1cm4geyBzdWJqZWN0cywgc3R5bGVzLCBtb29kcywgY29sb3JzIH07XG59XG4iXSwibmFtZXMiOlsiU1RZTEVfTUFQUElOR1MiLCJNT09EX01BUFBJTkdTIiwiUVVBTElUWV9FTkhBTkNFUlMiLCJMSUdIVElOR19NQVBQSU5HUyIsIkNPTVBPU0lUSU9OX01BUFBJTkdTIiwiZXh0cmFjdEltYWdlUmVxdWlyZW1lbnQiLCJjb252ZXJzYXRpb25IaXN0b3J5IiwiZnVsbFRleHQiLCJqb2luIiwidG9Mb3dlckNhc2UiLCJyZXF1aXJlbWVudCIsImtleSIsInZhbHVlIiwiT2JqZWN0IiwiZW50cmllcyIsImluY2x1ZGVzIiwic3R5bGUiLCJtb29kIiwiY29sb3JzIiwiZm91bmRDb2xvcnMiLCJmaWx0ZXIiLCJjb2xvciIsImxlbmd0aCIsImdlbmVyYXRlT3B0aW1pemVkUHJvbXB0Iiwib3JpZ2luYWxQcm9tcHQiLCJwcm9tcHRQYXJ0cyIsImltcHJvdmVtZW50cyIsInN1YmplY3QiLCJwdXNoIiwic3R5bGVEZXNjIiwiY29tcG9zaXRpb24iLCJjb21wRGVzYyIsImxpZ2h0aW5nIiwibGlnaHREZXNjIiwibW9vZERlc2MiLCJjb2xvckRlc2MiLCJxdWFsaXR5RW5oYW5jZXJzIiwic2xpY2UiLCJvcHRpbWl6ZWRQcm9tcHQiLCJjb25maWRlbmNlIiwiTWF0aCIsIm1pbiIsInN1Z2dlc3RlZFBhcmFtZXRlcnMiLCJhc3BlY3RSYXRpbyIsInF1YWxpdHkiLCJhbmFseXplS2V5d29yZHMiLCJpbnB1dCIsInRleHQiLCJzdWJqZWN0cyIsInN0eWxlcyIsIm1vb2RzIiwia2V5cyIsImZvckVhY2giLCJjb2xvcktleXdvcmRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/promptOptimizer.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/openai"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();