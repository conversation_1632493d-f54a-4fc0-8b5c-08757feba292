/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_liupeng_cursor_Flux_flux_chat_app_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/route.ts */ \"(rsc)/./src/app/api/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/api/chat/route.ts\",\n    nextConfigOutput,\n    userland: _Users_liupeng_cursor_Flux_flux_chat_app_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZjaGF0JTJGcm91dGUmcGFnZT0lMkZhcGklMkZjaGF0JTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGY2hhdCUyRnJvdXRlLnRzJmFwcERpcj0lMkZVc2VycyUyRmxpdXBlbmclMkZjdXJzb3IlMkYlRTglQkYlOUIlRTklOTglQjYlMkZGbHV4JUU1JUFGJUI5JUU4JUFGJTlEJUU1JUJDJThGJUU1JUJBJTk0JUU3JTk0JUE4JTJGZmx1eC1jaGF0LWFwcCUyRnNyYyUyRmFwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9JTJGVXNlcnMlMkZsaXVwZW5nJTJGY3Vyc29yJTJGJUU4JUJGJTlCJUU5JTk4JUI2JTJGRmx1eCVFNSVBRiVCOSVFOCVBRiU5RCVFNSVCQyU4RiVFNSVCQSU5NCVFNyU5NCVBOCUyRmZsdXgtY2hhdC1hcHAmaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9JnByZWZlcnJlZFJlZ2lvbj0mbWlkZGxld2FyZUNvbmZpZz1lMzAlM0QhIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQStGO0FBQ3ZDO0FBQ3FCO0FBQzBCO0FBQ3ZHO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qix5R0FBbUI7QUFDM0M7QUFDQSxjQUFjLGtFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsc0RBQXNEO0FBQzlEO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQzBGOztBQUUxRiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCIvVXNlcnMvbGl1cGVuZy9jdXJzb3Iv6L+b6Zi2L0ZsdXjlr7nor53lvI/lupTnlKgvZmx1eC1jaGF0LWFwcC9zcmMvYXBwL2FwaS9jaGF0L3JvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcIlwiXG5jb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBSb3V0ZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUk9VVEUsXG4gICAgICAgIHBhZ2U6IFwiL2FwaS9jaGF0L3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvY2hhdFwiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvY2hhdC9yb3V0ZVwiXG4gICAgfSxcbiAgICByZXNvbHZlZFBhZ2VQYXRoOiBcIi9Vc2Vycy9saXVwZW5nL2N1cnNvci/ov5vpmLYvRmx1eOWvueivneW8j+W6lOeUqC9mbHV4LWNoYXQtYXBwL3NyYy9hcHAvYXBpL2NoYXQvcm91dGUudHNcIixcbiAgICBuZXh0Q29uZmlnT3V0cHV0LFxuICAgIHVzZXJsYW5kXG59KTtcbi8vIFB1bGwgb3V0IHRoZSBleHBvcnRzIHRoYXQgd2UgbmVlZCB0byBleHBvc2UgZnJvbSB0aGUgbW9kdWxlLiBUaGlzIHNob3VsZFxuLy8gYmUgZWxpbWluYXRlZCB3aGVuIHdlJ3ZlIG1vdmVkIHRoZSBvdGhlciByb3V0ZXMgdG8gdGhlIG5ldyBmb3JtYXQuIFRoZXNlXG4vLyBhcmUgdXNlZCB0byBob29rIGludG8gdGhlIHJvdXRlLlxuY29uc3QgeyB3b3JrQXN5bmNTdG9yYWdlLCB3b3JrVW5pdEFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MgfSA9IHJvdXRlTW9kdWxlO1xuZnVuY3Rpb24gcGF0Y2hGZXRjaCgpIHtcbiAgICByZXR1cm4gX3BhdGNoRmV0Y2goe1xuICAgICAgICB3b3JrQXN5bmNTdG9yYWdlLFxuICAgICAgICB3b3JrVW5pdEFzeW5jU3RvcmFnZVxuICAgIH0pO1xufVxuZXhwb3J0IHsgcm91dGVNb2R1bGUsIHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/chat/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/chat/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_openai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/openai */ \"(rsc)/./src/lib/openai.ts\");\n/* harmony import */ var _lib_promptOptimizer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/promptOptimizer */ \"(rsc)/./src/lib/promptOptimizer.ts\");\n\n\n\nasync function POST(request) {\n    try {\n        const { message, conversationHistory } = await request.json();\n        if (!message) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Message is required'\n            }, {\n                status: 400\n            });\n        }\n        // 分析用户输入中的关键词\n        const keywords = (0,_lib_promptOptimizer__WEBPACK_IMPORTED_MODULE_2__.analyzeKeywords)(message);\n        // 从对话历史中提取图像需求\n        const extractedRequirement = (0,_lib_promptOptimizer__WEBPACK_IMPORTED_MODULE_2__.extractImageRequirement)([\n            ...conversationHistory,\n            message\n        ]);\n        // 使用OpenAI分析需求并生成回复\n        const response = await (0,_lib_openai__WEBPACK_IMPORTED_MODULE_1__.analyzeRequirement)(message, conversationHistory);\n        // 检查是否需要进一步澄清\n        const needsClarification = checkIfNeedsClarification(extractedRequirement);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            response,\n            extractedRequirement,\n            keywords,\n            needsClarification,\n            metadata: {\n                type: needsClarification ? 'clarification' : 'analysis',\n                extractedElements: Object.keys(extractedRequirement)\n            }\n        });\n    } catch (error) {\n        console.error('Chat API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n// 检查是否需要进一步澄清\nfunction checkIfNeedsClarification(requirement) {\n    const requiredFields = [\n        'subject',\n        'style',\n        'mood'\n    ];\n    const missingFields = requiredFields.filter((field)=>!requirement[field]);\n    // 如果缺少超过一半的关键信息，需要澄清\n    return missingFields.length > requiredFields.length / 2;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9jaGF0L3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBd0Q7QUFDTjtBQUMrQjtBQUUxRSxlQUFlSSxLQUFLQyxPQUFvQjtJQUM3QyxJQUFJO1FBQ0YsTUFBTSxFQUFFQyxPQUFPLEVBQUVDLG1CQUFtQixFQUFFLEdBQUcsTUFBTUYsUUFBUUcsSUFBSTtRQUUzRCxJQUFJLENBQUNGLFNBQVM7WUFDWixPQUFPTixxREFBWUEsQ0FBQ1EsSUFBSSxDQUN0QjtnQkFBRUMsT0FBTztZQUFzQixHQUMvQjtnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsY0FBYztRQUNkLE1BQU1DLFdBQVdSLHFFQUFlQSxDQUFDRztRQUVqQyxlQUFlO1FBQ2YsTUFBTU0sdUJBQXVCViw2RUFBdUJBLENBQUM7ZUFBSUs7WUFBcUJEO1NBQVE7UUFFdEYsb0JBQW9CO1FBQ3BCLE1BQU1PLFdBQVcsTUFBTVosK0RBQWtCQSxDQUFDSyxTQUFTQztRQUVuRCxjQUFjO1FBQ2QsTUFBTU8scUJBQXFCQywwQkFBMEJIO1FBRXJELE9BQU9aLHFEQUFZQSxDQUFDUSxJQUFJLENBQUM7WUFDdkJLO1lBQ0FEO1lBQ0FEO1lBQ0FHO1lBQ0FFLFVBQVU7Z0JBQ1JDLE1BQU1ILHFCQUFxQixrQkFBa0I7Z0JBQzdDSSxtQkFBbUJDLE9BQU9DLElBQUksQ0FBQ1I7WUFDakM7UUFDRjtJQUVGLEVBQUUsT0FBT0gsT0FBTztRQUNkWSxRQUFRWixLQUFLLENBQUMsbUJBQW1CQTtRQUNqQyxPQUFPVCxxREFBWUEsQ0FBQ1EsSUFBSSxDQUN0QjtZQUFFQyxPQUFPO1FBQXdCLEdBQ2pDO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGO0FBRUEsY0FBYztBQUNkLFNBQVNLLDBCQUEwQk8sV0FBZ0I7SUFDakQsTUFBTUMsaUJBQWlCO1FBQUM7UUFBVztRQUFTO0tBQU87SUFDbkQsTUFBTUMsZ0JBQWdCRCxlQUFlRSxNQUFNLENBQUNDLENBQUFBLFFBQVMsQ0FBQ0osV0FBVyxDQUFDSSxNQUFNO0lBRXhFLHFCQUFxQjtJQUNyQixPQUFPRixjQUFjRyxNQUFNLEdBQUdKLGVBQWVJLE1BQU0sR0FBRztBQUN4RCIsInNvdXJjZXMiOlsiL1VzZXJzL2xpdXBlbmcvY3Vyc29yL+i/m+mYti9GbHV45a+56K+d5byP5bqU55SoL2ZsdXgtY2hhdC1hcHAvc3JjL2FwcC9hcGkvY2hhdC9yb3V0ZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHsgYW5hbHl6ZVJlcXVpcmVtZW50IH0gZnJvbSAnQC9saWIvb3BlbmFpJztcbmltcG9ydCB7IGV4dHJhY3RJbWFnZVJlcXVpcmVtZW50LCBhbmFseXplS2V5d29yZHMgfSBmcm9tICdAL2xpYi9wcm9tcHRPcHRpbWl6ZXInO1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUE9TVChyZXF1ZXN0OiBOZXh0UmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnN0IHsgbWVzc2FnZSwgY29udmVyc2F0aW9uSGlzdG9yeSB9ID0gYXdhaXQgcmVxdWVzdC5qc29uKCk7XG5cbiAgICBpZiAoIW1lc3NhZ2UpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogJ01lc3NhZ2UgaXMgcmVxdWlyZWQnIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICAvLyDliIbmnpDnlKjmiLfovpPlhaXkuK3nmoTlhbPplK7or41cbiAgICBjb25zdCBrZXl3b3JkcyA9IGFuYWx5emVLZXl3b3JkcyhtZXNzYWdlKTtcbiAgICBcbiAgICAvLyDku47lr7nor53ljoblj7LkuK3mj5Dlj5blm77lg4/pnIDmsYJcbiAgICBjb25zdCBleHRyYWN0ZWRSZXF1aXJlbWVudCA9IGV4dHJhY3RJbWFnZVJlcXVpcmVtZW50KFsuLi5jb252ZXJzYXRpb25IaXN0b3J5LCBtZXNzYWdlXSk7XG4gICAgXG4gICAgLy8g5L2/55SoT3BlbkFJ5YiG5p6Q6ZyA5rGC5bm255Sf5oiQ5Zue5aSNXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhbmFseXplUmVxdWlyZW1lbnQobWVzc2FnZSwgY29udmVyc2F0aW9uSGlzdG9yeSk7XG4gICAgXG4gICAgLy8g5qOA5p+l5piv5ZCm6ZyA6KaB6L+b5LiA5q2l5r6E5riFXG4gICAgY29uc3QgbmVlZHNDbGFyaWZpY2F0aW9uID0gY2hlY2tJZk5lZWRzQ2xhcmlmaWNhdGlvbihleHRyYWN0ZWRSZXF1aXJlbWVudCk7XG4gICAgXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHJlc3BvbnNlLFxuICAgICAgZXh0cmFjdGVkUmVxdWlyZW1lbnQsXG4gICAgICBrZXl3b3JkcyxcbiAgICAgIG5lZWRzQ2xhcmlmaWNhdGlvbixcbiAgICAgIG1ldGFkYXRhOiB7XG4gICAgICAgIHR5cGU6IG5lZWRzQ2xhcmlmaWNhdGlvbiA/ICdjbGFyaWZpY2F0aW9uJyA6ICdhbmFseXNpcycsXG4gICAgICAgIGV4dHJhY3RlZEVsZW1lbnRzOiBPYmplY3Qua2V5cyhleHRyYWN0ZWRSZXF1aXJlbWVudCksXG4gICAgICB9XG4gICAgfSk7XG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdDaGF0IEFQSSBlcnJvcjonLCBlcnJvcik7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBlcnJvcjogJ0ludGVybmFsIHNlcnZlciBlcnJvcicgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICk7XG4gIH1cbn1cblxuLy8g5qOA5p+l5piv5ZCm6ZyA6KaB6L+b5LiA5q2l5r6E5riFXG5mdW5jdGlvbiBjaGVja0lmTmVlZHNDbGFyaWZpY2F0aW9uKHJlcXVpcmVtZW50OiBhbnkpOiBib29sZWFuIHtcbiAgY29uc3QgcmVxdWlyZWRGaWVsZHMgPSBbJ3N1YmplY3QnLCAnc3R5bGUnLCAnbW9vZCddO1xuICBjb25zdCBtaXNzaW5nRmllbGRzID0gcmVxdWlyZWRGaWVsZHMuZmlsdGVyKGZpZWxkID0+ICFyZXF1aXJlbWVudFtmaWVsZF0pO1xuICBcbiAgLy8g5aaC5p6c57y65bCR6LaF6L+H5LiA5Y2K55qE5YWz6ZSu5L+h5oGv77yM6ZyA6KaB5r6E5riFXG4gIHJldHVybiBtaXNzaW5nRmllbGRzLmxlbmd0aCA+IHJlcXVpcmVkRmllbGRzLmxlbmd0aCAvIDI7XG59XG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiYW5hbHl6ZVJlcXVpcmVtZW50IiwiZXh0cmFjdEltYWdlUmVxdWlyZW1lbnQiLCJhbmFseXplS2V5d29yZHMiLCJQT1NUIiwicmVxdWVzdCIsIm1lc3NhZ2UiLCJjb252ZXJzYXRpb25IaXN0b3J5IiwianNvbiIsImVycm9yIiwic3RhdHVzIiwia2V5d29yZHMiLCJleHRyYWN0ZWRSZXF1aXJlbWVudCIsInJlc3BvbnNlIiwibmVlZHNDbGFyaWZpY2F0aW9uIiwiY2hlY2tJZk5lZWRzQ2xhcmlmaWNhdGlvbiIsIm1ldGFkYXRhIiwidHlwZSIsImV4dHJhY3RlZEVsZW1lbnRzIiwiT2JqZWN0Iiwia2V5cyIsImNvbnNvbGUiLCJyZXF1aXJlbWVudCIsInJlcXVpcmVkRmllbGRzIiwibWlzc2luZ0ZpZWxkcyIsImZpbHRlciIsImZpZWxkIiwibGVuZ3RoIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/openai.ts":
/*!***************************!*\
  !*** ./src/lib/openai.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PROMPT_OPTIMIZATION_PROMPT: () => (/* binding */ PROMPT_OPTIMIZATION_PROMPT),\n/* harmony export */   REQUIREMENT_ANALYSIS_PROMPT: () => (/* binding */ REQUIREMENT_ANALYSIS_PROMPT),\n/* harmony export */   aiClient: () => (/* binding */ aiClient),\n/* harmony export */   analyzeRequirement: () => (/* binding */ analyzeRequirement),\n/* harmony export */   getModel: () => (/* binding */ getModel),\n/* harmony export */   getProvider: () => (/* binding */ getProvider),\n/* harmony export */   optimizePrompt: () => (/* binding */ optimizePrompt)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/openai/index.mjs\");\n\n// 初始化AI客户端 - 支持OpenAI和DeepSeek\nconst aiClient = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n    apiKey: process.env.DEEPSEEK_API_KEY || process.env.OPENAI_API_KEY,\n    baseURL: process.env.DEEPSEEK_API_KEY ? 'https://api.deepseek.com' : undefined\n});\n// 获取当前使用的模型\nconst getModel = ()=>{\n    if (process.env.DEEPSEEK_API_KEY && process.env.DEEPSEEK_API_KEY !== 'your_deepseek_api_key_here') {\n        return 'deepseek-chat';\n    }\n    return 'gpt-4';\n};\n// 获取当前AI提供商\nconst getProvider = ()=>{\n    if (process.env.DEEPSEEK_API_KEY && process.env.DEEPSEEK_API_KEY !== 'your_deepseek_api_key_here') {\n        return 'deepseek';\n    }\n    return 'openai';\n};\n// 系统提示词 - 用于需求理解和澄清\nconst REQUIREMENT_ANALYSIS_PROMPT = `你是一个专业的AI图像生成助手，专门帮助用户将模糊的图像需求转换为清晰、具体的描述。\n\n你的核心任务：\n1. 深度理解用户的图像生成需求和意图\n2. 智能识别描述中缺失的关键视觉信息\n3. 通过自然对话逐步获取完整的创作细节\n4. 提取并整理关键的视觉元素\n\n重点关注的视觉要素：\n- 主题内容：主体对象、场景、人物等\n- 艺术风格：写实、卡通、油画、水彩、数字艺术等\n- 情绪氛围：温暖、冷酷、神秘、浪漫、史诗等\n- 色彩搭配：主色调、配色方案、色彩情感\n- 构图布局：视角、景别、对称性、焦点\n- 光影效果：光源、明暗、氛围光、特殊效果\n- 质量要求：分辨率、细节程度、专业水准\n- 特殊元素：背景、道具、特效、风格化处理\n\n对话原则：\n- 使用温和、专业且富有启发性的语气\n- 每次只询问1-2个最关键的问题\n- 根据用户回答智能调整后续问题\n- 避免机械化的问答，保持对话的自然流畅\n- 适时提供创意建议和专业指导`;\n// 系统提示词 - 用于提示词优化\nconst PROMPT_OPTIMIZATION_PROMPT = `你是一位顶级的AI图像生成提示词优化专家，拥有丰富的视觉艺术和AI绘画经验。你的任务是将用户的创意需求转换为高效、精准的图像生成提示词。\n\n核心优化策略：\n1. 精确性：使用具体、明确的描述性词汇，避免模糊表达\n2. 层次性：按视觉重要性和影响力排序关键词\n3. 专业性：融入艺术风格、技术参数和行业术语\n4. 完整性：确保涵盖主体、风格、构图、光影、色彩等要素\n5. 兼容性：优化后的提示词应适用于主流AI绘画模型\n\n标准提示词架构：\n[核心主体] + [详细描述] + [艺术风格] + [构图视角] + [光影效果] + [色彩方案] + [质量增强] + [技术参数]\n\n输出要求：\n请严格按照JSON格式返回优化结果：\n{\n  \"originalPrompt\": \"用户原始描述\",\n  \"optimizedPrompt\": \"专业优化后的完整提示词\",\n  \"improvements\": [\"具体改进点1\", \"具体改进点2\", \"...\"],\n  \"confidence\": 0.85,\n  \"suggestedParameters\": {\n    \"aspectRatio\": \"推荐宽高比\",\n    \"style\": \"推荐风格设置\",\n    \"quality\": \"质量等级\"\n  }\n}\n\n优化重点：\n- 主体描述要具体生动，包含关键特征\n- 风格描述要专业准确，符合艺术分类\n- 构图要素要明确，包含视角和布局\n- 光影描述要富有表现力和技术性\n- 色彩搭配要和谐且有视觉冲击力\n- 质量词汇要权威，提升生成效果`;\n// 分析用户需求并生成澄清问题\nasync function analyzeRequirement(userInput, conversationHistory) {\n    // 检查是否有有效的API密钥\n    const hasValidKey = process.env.DEEPSEEK_API_KEY && process.env.DEEPSEEK_API_KEY !== 'your_deepseek_api_key_here' || process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'your_openai_api_key_here';\n    if (!hasValidKey) {\n        return generateDemoResponse(userInput, conversationHistory);\n    }\n    try {\n        const model = getModel();\n        const provider = getProvider();\n        console.log(`Using ${provider} with model: ${model}`);\n        const response = await aiClient.chat.completions.create({\n            model: model,\n            messages: [\n                {\n                    role: 'system',\n                    content: REQUIREMENT_ANALYSIS_PROMPT\n                },\n                ...conversationHistory.map((msg, index)=>({\n                        role: index % 2 === 0 ? 'user' : 'assistant',\n                        content: msg\n                    })),\n                {\n                    role: 'user',\n                    content: userInput\n                }\n            ],\n            temperature: 0.7,\n            max_tokens: 800,\n            // DeepSeek特定参数\n            ...provider === 'deepseek' && {\n                top_p: 0.9,\n                frequency_penalty: 0.1,\n                presence_penalty: 0.1\n            }\n        });\n        return response.choices[0]?.message?.content || '';\n    } catch (error) {\n        console.error(`Error analyzing requirement with ${getProvider()}:`, error);\n        // 回退到演示模式\n        return generateDemoResponse(userInput, conversationHistory);\n    }\n}\n// 演示模式响应生成器\nfunction generateDemoResponse(userInput, conversationHistory) {\n    const input = userInput.toLowerCase();\n    // 简单的关键词检测和响应\n    if (conversationHistory.length === 0) {\n        // 首次交互\n        if (input.includes('猫') || input.includes('cat')) {\n            return '听起来很有趣！您想要什么风格的小猫图像呢？比如：\\n\\n• 写实风格的摄影作品\\n• 可爱的卡通风格\\n• 油画艺术风格\\n• 水彩画风格\\n\\n另外，您希望小猫在什么环境中呢？';\n        } else if (input.includes('风景') || input.includes('landscape')) {\n            return '风景画很棒！请告诉我更多细节：\\n\\n• 您想要什么类型的风景？（山脉、海滩、森林、城市等）\\n• 希望是什么时间？（日出、日落、夜晚等）\\n• 偏好什么艺术风格？\\n• 想要什么样的色彩氛围？';\n        } else if (input.includes('人物') || input.includes('人')) {\n            return '人物画像需要考虑很多细节！请告诉我：\\n\\n• 是肖像特写还是全身像？\\n• 想要什么风格？（写实、卡通、艺术风格等）\\n• 人物的年龄和特征？\\n• 背景环境如何？\\n• 服装风格有什么要求？';\n        } else {\n            return '很好的创意！为了帮您生成最佳的提示词，我需要了解更多细节：\\n\\n• 主要的视觉元素是什么？\\n• 您偏好什么艺术风格？\\n• 希望营造什么样的氛围？\\n• 有特定的色彩偏好吗？\\n\\n请详细描述您的想法！';\n        }\n    } else {\n        // 后续交互\n        if (input.includes('写实') || input.includes('摄影')) {\n            return '写实风格很棒！我建议：\\n\\n• 使用专业摄影术语\\n• 强调细节和质感\\n• 考虑光照效果\\n\\n还有其他细节需要补充吗？比如构图方式或特定的光照效果？';\n        } else if (input.includes('卡通') || input.includes('可爱')) {\n            return '卡通风格会很有趣！建议：\\n\\n• 强调可爱和友好的特征\\n• 使用明亮的色彩\\n• 简化的造型设计\\n\\n您希望是什么样的卡通风格？日式动漫、迪士尼风格，还是其他？';\n        } else if (input.includes('颜色') || input.includes('色彩')) {\n            return '色彩选择很重要！基于您的描述，我建议：\\n\\n• 温暖的色调营造舒适感\\n• 对比色增加视觉冲击\\n• 柔和的色彩营造宁静氛围\\n\\n您有特定的颜色偏好吗？';\n        } else {\n            return '很好！基于我们的对话，我已经收集到足够的信息来优化您的提示词了。让我为您生成一个专业的图像生成提示词！';\n        }\n    }\n}\n// 优化提示词\nasync function optimizePrompt(requirement, conversationContext) {\n    // 检查是否有有效的API密钥\n    const hasValidKey = process.env.DEEPSEEK_API_KEY && process.env.DEEPSEEK_API_KEY !== 'your_deepseek_api_key_here' || process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'your_openai_api_key_here';\n    if (!hasValidKey) {\n        return generateDemoOptimization(requirement, conversationContext);\n    }\n    try {\n        const model = getModel();\n        const provider = getProvider();\n        console.log(`Optimizing prompt using ${provider} with model: ${model}`);\n        const response = await aiClient.chat.completions.create({\n            model: model,\n            messages: [\n                {\n                    role: 'system',\n                    content: PROMPT_OPTIMIZATION_PROMPT\n                },\n                {\n                    role: 'user',\n                    content: `请基于以下对话内容和用户需求，生成专业优化的图像生成提示词：\n\n## 对话上下文：\n${conversationContext}\n\n## 用户最终需求：\n${requirement}\n\n## 输出要求：\n请严格按照JSON格式返回优化结果，确保JSON格式正确且完整。`\n                }\n            ],\n            temperature: 0.2,\n            max_tokens: 1200,\n            // DeepSeek特定参数\n            ...provider === 'deepseek' && {\n                top_p: 0.8,\n                frequency_penalty: 0.0,\n                presence_penalty: 0.0\n            }\n        });\n        const content = response.choices[0]?.message?.content || '';\n        try {\n            // 尝试提取JSON内容\n            const jsonMatch = content.match(/\\{[\\s\\S]*\\}/);\n            const jsonContent = jsonMatch ? jsonMatch[0] : content;\n            const parsed = JSON.parse(jsonContent);\n            // 验证必要字段\n            if (!parsed.optimizedPrompt) {\n                throw new Error('Missing optimizedPrompt field');\n            }\n            return {\n                originalPrompt: requirement,\n                optimizedPrompt: parsed.optimizedPrompt,\n                improvements: parsed.improvements || [\n                    'AI优化提示词结构和专业术语'\n                ],\n                confidence: parsed.confidence || 0.85,\n                suggestedParameters: parsed.suggestedParameters || {\n                    aspectRatio: '16:9',\n                    style: 'auto',\n                    quality: 'high'\n                }\n            };\n        } catch (parseError) {\n            console.warn('JSON parsing failed, using fallback:', parseError);\n            // 如果JSON解析失败，返回基本结构\n            return {\n                originalPrompt: requirement,\n                optimizedPrompt: content.replace(/```json|```/g, '').trim(),\n                improvements: [\n                    `使用${provider}优化提示词`,\n                    '增强专业术语和结构'\n                ],\n                confidence: 0.8,\n                suggestedParameters: {\n                    aspectRatio: '16:9',\n                    style: 'auto',\n                    quality: 'high'\n                }\n            };\n        }\n    } catch (error) {\n        console.error(`Error optimizing prompt with ${getProvider()}:`, error);\n        // 回退到演示模式\n        return generateDemoOptimization(requirement, conversationContext);\n    }\n}\n// 演示模式的提示词优化 - 模拟DeepSeek风格\nfunction generateDemoOptimization(requirement, conversationContext) {\n    const context = conversationContext.toLowerCase();\n    const req = requirement.toLowerCase();\n    let optimizedPrompt = requirement;\n    const improvements = [];\n    // 基于内容添加专业优化 - DeepSeek风格\n    if (req.includes('猫') || req.includes('cat')) {\n        optimizedPrompt = `adorable cat, expressive eyes, fluffy fur texture, sitting pose, natural lighting, warm color palette, cozy indoor environment, professional pet photography, high resolution, detailed whiskers, soft focus background, 8k quality, award-winning composition`;\n        improvements.push('DeepSeek优化：增强了宠物摄影专业术语', '细化了毛发质感和眼神描述', '优化了环境氛围和光照设置', '添加了构图和质量增强词汇');\n    } else if (req.includes('风景') || req.includes('landscape')) {\n        optimizedPrompt = `breathtaking landscape vista, dramatic sky formation, golden hour illumination, vibrant natural colors, panoramic composition, depth of field, atmospheric perspective, professional landscape photography, ultra-high definition, cinematic quality, nature's masterpiece`;\n        improvements.push('DeepSeek优化：采用了电影级风景摄影术语', '强化了天空和光线的戏剧性', '增加了景深和透视效果', '提升了整体视觉冲击力');\n    } else if (req.includes('人物') || req.includes('portrait')) {\n        optimizedPrompt = `professional portrait photography, detailed facial features, expressive eyes, natural skin texture, studio lighting setup, shallow depth of field, artistic composition, high-end retouching, commercial quality, fashion photography style`;\n        improvements.push('DeepSeek优化：专业人像摄影技术术语', '强调了面部细节和皮肤质感', '优化了灯光和景深设置', '提升了商业摄影品质');\n    } else if (req.includes('未来') || req.includes('科幻')) {\n        optimizedPrompt = `futuristic concept art, cyberpunk aesthetic, neon lighting effects, high-tech architecture, digital art style, sci-fi atmosphere, advanced technology, holographic elements, cinematic composition, ultra-modern design, 4k digital artwork`;\n        improvements.push('DeepSeek优化：科幻概念艺术专业术语', '增强了赛博朋克视觉元素', '优化了未来科技感表达', '提升了数字艺术品质');\n    } else {\n        // 通用DeepSeek风格优化\n        optimizedPrompt = `${requirement}, masterful composition, professional quality, intricate details, optimal lighting, harmonious color scheme, artistic excellence, high-definition clarity, visual impact, creative interpretation`;\n        improvements.push('DeepSeek优化：提升了整体艺术表达', '增强了专业术语和技术参数', '优化了视觉效果描述', '改善了创意解释能力');\n    }\n    // 基于对话上下文的深度优化\n    if (context.includes('写实') || context.includes('摄影')) {\n        optimizedPrompt += ', photorealistic rendering, hyperrealistic details, camera-like precision, authentic textures';\n        improvements.push('DeepSeek深度优化：增强写实主义表现力');\n    } else if (context.includes('卡通') || context.includes('动漫')) {\n        optimizedPrompt += ', stylized animation, vibrant cartoon aesthetics, character design excellence, animated art style';\n        improvements.push('DeepSeek深度优化：强化动画艺术风格');\n    } else if (context.includes('油画')) {\n        optimizedPrompt += ', traditional oil painting technique, classical art mastery, painterly brushstrokes, fine art quality';\n        improvements.push('DeepSeek深度优化：提升古典绘画技法表达');\n    } else if (context.includes('水彩')) {\n        optimizedPrompt += ', watercolor transparency, fluid brushwork, artistic spontaneity, delicate color blending';\n        improvements.push('DeepSeek深度优化：增强水彩画艺术特色');\n    }\n    return {\n        originalPrompt: requirement,\n        optimizedPrompt,\n        improvements,\n        confidence: 0.92,\n        suggestedParameters: {\n            aspectRatio: context.includes('人物') ? '3:4' : '16:9',\n            style: context.includes('写实') ? 'photorealistic' : 'artistic',\n            quality: 'ultra-high',\n            model: 'deepseek-optimized'\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/openai.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/promptOptimizer.ts":
/*!************************************!*\
  !*** ./src/lib/promptOptimizer.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyzeKeywords: () => (/* binding */ analyzeKeywords),\n/* harmony export */   extractImageRequirement: () => (/* binding */ extractImageRequirement),\n/* harmony export */   generateOptimizedPrompt: () => (/* binding */ generateOptimizedPrompt)\n/* harmony export */ });\n// 艺术风格映射\nconst STYLE_MAPPINGS = {\n    '写实': 'photorealistic, hyperrealistic, detailed',\n    '卡通': 'cartoon style, animated, stylized',\n    '油画': 'oil painting, classical art, painterly',\n    '水彩': 'watercolor, soft brushstrokes, flowing',\n    '素描': 'pencil sketch, charcoal drawing, monochrome',\n    '数字艺术': 'digital art, concept art, modern',\n    '像素艺术': 'pixel art, 8-bit, retro gaming style',\n    '抽象': 'abstract art, non-representational, artistic',\n    '极简': 'minimalist, clean, simple composition',\n    '赛博朋克': 'cyberpunk, neon lights, futuristic, dystopian'\n};\n// 情绪氛围映射\nconst MOOD_MAPPINGS = {\n    '温暖': 'warm, cozy, inviting atmosphere',\n    '冷酷': 'cold, stark, dramatic lighting',\n    '神秘': 'mysterious, enigmatic, atmospheric',\n    '浪漫': 'romantic, dreamy, soft lighting',\n    '史诗': 'epic, grand, cinematic',\n    '宁静': 'peaceful, serene, calm',\n    '活力': 'energetic, vibrant, dynamic',\n    '忧郁': 'melancholic, moody, contemplative',\n    '恐怖': 'horror, dark, ominous',\n    '欢乐': 'joyful, cheerful, bright'\n};\n// 质量增强词汇\nconst QUALITY_ENHANCERS = [\n    'highly detailed',\n    'sharp focus',\n    'professional photography',\n    '8k resolution',\n    'award winning',\n    'masterpiece',\n    'trending on artstation',\n    'perfect composition'\n];\n// 光照效果映射\nconst LIGHTING_MAPPINGS = {\n    '自然光': 'natural lighting, soft daylight',\n    '金色时光': 'golden hour, warm sunset lighting',\n    '蓝色时光': 'blue hour, twilight, cool tones',\n    '戏剧性': 'dramatic lighting, high contrast',\n    '柔和': 'soft lighting, diffused light',\n    '背光': 'backlighting, rim light, silhouette',\n    '霓虹': 'neon lighting, colorful glow',\n    '月光': 'moonlight, nocturnal, ethereal',\n    '工作室': 'studio lighting, professional setup',\n    '环境光': 'ambient lighting, atmospheric'\n};\n// 构图方式映射\nconst COMPOSITION_MAPPINGS = {\n    '特写': 'close-up, detailed portrait',\n    '全身': 'full body shot, complete figure',\n    '中景': 'medium shot, waist up',\n    '远景': 'wide shot, establishing shot',\n    '鸟瞰': 'aerial view, top-down perspective',\n    '低角度': 'low angle, dramatic perspective',\n    '对称': 'symmetrical composition, balanced',\n    '三分法': 'rule of thirds, dynamic composition',\n    '中心构图': 'centered composition, focal point',\n    '对角线': 'diagonal composition, dynamic lines'\n};\n// 从对话中提取图像需求\nfunction extractImageRequirement(conversationHistory) {\n    const fullText = conversationHistory.join(' ').toLowerCase();\n    const requirement = {};\n    // 提取风格\n    for (const [key, value] of Object.entries(STYLE_MAPPINGS)){\n        if (fullText.includes(key.toLowerCase()) || fullText.includes(value.toLowerCase())) {\n            requirement.style = key;\n            break;\n        }\n    }\n    // 提取情绪\n    for (const [key, value] of Object.entries(MOOD_MAPPINGS)){\n        if (fullText.includes(key.toLowerCase())) {\n            requirement.mood = key;\n            break;\n        }\n    }\n    // 提取颜色\n    const colors = [\n        '红',\n        '蓝',\n        '绿',\n        '黄',\n        '紫',\n        '橙',\n        '粉',\n        '黑',\n        '白',\n        '灰'\n    ];\n    const foundColors = colors.filter((color)=>fullText.includes(color));\n    if (foundColors.length > 0) {\n        requirement.colors = foundColors;\n    }\n    return requirement;\n}\n// 生成优化的提示词\nfunction generateOptimizedPrompt(requirement, originalPrompt) {\n    const promptParts = [];\n    const improvements = [];\n    // 添加主体描述\n    if (requirement.subject) {\n        promptParts.push(requirement.subject);\n    }\n    // 添加风格描述\n    if (requirement.style && STYLE_MAPPINGS[requirement.style]) {\n        const styleDesc = STYLE_MAPPINGS[requirement.style];\n        promptParts.push(styleDesc);\n        improvements.push(`添加了${requirement.style}风格的专业描述`);\n    }\n    // 添加构图描述\n    if (requirement.composition && COMPOSITION_MAPPINGS[requirement.composition]) {\n        const compDesc = COMPOSITION_MAPPINGS[requirement.composition];\n        promptParts.push(compDesc);\n        improvements.push(`优化了构图描述`);\n    }\n    // 添加光照效果\n    if (requirement.lighting && LIGHTING_MAPPINGS[requirement.lighting]) {\n        const lightDesc = LIGHTING_MAPPINGS[requirement.lighting];\n        promptParts.push(lightDesc);\n        improvements.push(`增强了光照效果描述`);\n    }\n    // 添加情绪氛围\n    if (requirement.mood && MOOD_MAPPINGS[requirement.mood]) {\n        const moodDesc = MOOD_MAPPINGS[requirement.mood];\n        promptParts.push(moodDesc);\n        improvements.push(`添加了${requirement.mood}氛围描述`);\n    }\n    // 添加颜色方案\n    if (requirement.colors && requirement.colors.length > 0) {\n        const colorDesc = `${requirement.colors.join(', ')} color scheme`;\n        promptParts.push(colorDesc);\n        improvements.push(`指定了颜色方案`);\n    }\n    // 添加质量增强词汇\n    const qualityEnhancers = QUALITY_ENHANCERS.slice(0, 3).join(', ');\n    promptParts.push(qualityEnhancers);\n    improvements.push('添加了质量增强词汇');\n    const optimizedPrompt = promptParts.join(', ');\n    // 计算信心度\n    const confidence = Math.min(0.9, 0.5 + improvements.length * 0.1);\n    return {\n        originalPrompt,\n        optimizedPrompt,\n        improvements,\n        confidence,\n        suggestedParameters: {\n            aspectRatio: '16:9',\n            style: requirement.style || 'auto',\n            quality: 'high'\n        }\n    };\n}\n// 分析用户输入中的关键词\nfunction analyzeKeywords(input) {\n    const text = input.toLowerCase();\n    const subjects = [];\n    const styles = [];\n    const moods = [];\n    const colors = [];\n    // 检测风格关键词\n    Object.keys(STYLE_MAPPINGS).forEach((style)=>{\n        if (text.includes(style.toLowerCase())) {\n            styles.push(style);\n        }\n    });\n    // 检测情绪关键词\n    Object.keys(MOOD_MAPPINGS).forEach((mood)=>{\n        if (text.includes(mood.toLowerCase())) {\n            moods.push(mood);\n        }\n    });\n    // 检测颜色关键词\n    const colorKeywords = [\n        '红',\n        '蓝',\n        '绿',\n        '黄',\n        '紫',\n        '橙',\n        '粉',\n        '黑',\n        '白',\n        '灰'\n    ];\n    colorKeywords.forEach((color)=>{\n        if (text.includes(color)) {\n            colors.push(color);\n        }\n    });\n    return {\n        subjects,\n        styles,\n        moods,\n        colors\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3Byb21wdE9wdGltaXplci50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFFQSxTQUFTO0FBQ1QsTUFBTUEsaUJBQWlCO0lBQ3JCLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sUUFBUTtJQUNSLFFBQVE7SUFDUixNQUFNO0lBQ04sTUFBTTtJQUNOLFFBQVE7QUFDVjtBQUVBLFNBQVM7QUFDVCxNQUFNQyxnQkFBZ0I7SUFDcEIsTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtBQUNSO0FBRUEsU0FBUztBQUNULE1BQU1DLG9CQUFvQjtJQUN4QjtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0NBQ0Q7QUFFRCxTQUFTO0FBQ1QsTUFBTUMsb0JBQW9CO0lBQ3hCLE9BQU87SUFDUCxRQUFRO0lBQ1IsUUFBUTtJQUNSLE9BQU87SUFDUCxNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sT0FBTztJQUNQLE9BQU87QUFDVDtBQUVBLFNBQVM7QUFDVCxNQUFNQyx1QkFBdUI7SUFDM0IsTUFBTTtJQUNOLE1BQU07SUFDTixNQUFNO0lBQ04sTUFBTTtJQUNOLE1BQU07SUFDTixPQUFPO0lBQ1AsTUFBTTtJQUNOLE9BQU87SUFDUCxRQUFRO0lBQ1IsT0FBTztBQUNUO0FBRUEsYUFBYTtBQUNOLFNBQVNDLHdCQUF3QkMsbUJBQTZCO0lBQ25FLE1BQU1DLFdBQVdELG9CQUFvQkUsSUFBSSxDQUFDLEtBQUtDLFdBQVc7SUFFMUQsTUFBTUMsY0FBeUMsQ0FBQztJQUVoRCxPQUFPO0lBQ1AsS0FBSyxNQUFNLENBQUNDLEtBQUtDLE1BQU0sSUFBSUMsT0FBT0MsT0FBTyxDQUFDZCxnQkFBaUI7UUFDekQsSUFBSU8sU0FBU1EsUUFBUSxDQUFDSixJQUFJRixXQUFXLE9BQU9GLFNBQVNRLFFBQVEsQ0FBQ0gsTUFBTUgsV0FBVyxLQUFLO1lBQ2xGQyxZQUFZTSxLQUFLLEdBQUdMO1lBQ3BCO1FBQ0Y7SUFDRjtJQUVBLE9BQU87SUFDUCxLQUFLLE1BQU0sQ0FBQ0EsS0FBS0MsTUFBTSxJQUFJQyxPQUFPQyxPQUFPLENBQUNiLGVBQWdCO1FBQ3hELElBQUlNLFNBQVNRLFFBQVEsQ0FBQ0osSUFBSUYsV0FBVyxLQUFLO1lBQ3hDQyxZQUFZTyxJQUFJLEdBQUdOO1lBQ25CO1FBQ0Y7SUFDRjtJQUVBLE9BQU87SUFDUCxNQUFNTyxTQUFTO1FBQUM7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7S0FBSTtJQUNqRSxNQUFNQyxjQUFjRCxPQUFPRSxNQUFNLENBQUNDLENBQUFBLFFBQVNkLFNBQVNRLFFBQVEsQ0FBQ007SUFDN0QsSUFBSUYsWUFBWUcsTUFBTSxHQUFHLEdBQUc7UUFDMUJaLFlBQVlRLE1BQU0sR0FBR0M7SUFDdkI7SUFFQSxPQUFPVDtBQUNUO0FBRUEsV0FBVztBQUNKLFNBQVNhLHdCQUNkYixXQUFzQyxFQUN0Q2MsY0FBc0I7SUFFdEIsTUFBTUMsY0FBd0IsRUFBRTtJQUNoQyxNQUFNQyxlQUF5QixFQUFFO0lBRWpDLFNBQVM7SUFDVCxJQUFJaEIsWUFBWWlCLE9BQU8sRUFBRTtRQUN2QkYsWUFBWUcsSUFBSSxDQUFDbEIsWUFBWWlCLE9BQU87SUFDdEM7SUFFQSxTQUFTO0lBQ1QsSUFBSWpCLFlBQVlNLEtBQUssSUFBSWhCLGNBQWMsQ0FBQ1UsWUFBWU0sS0FBSyxDQUFnQyxFQUFFO1FBQ3pGLE1BQU1hLFlBQVk3QixjQUFjLENBQUNVLFlBQVlNLEtBQUssQ0FBZ0M7UUFDbEZTLFlBQVlHLElBQUksQ0FBQ0M7UUFDakJILGFBQWFFLElBQUksQ0FBQyxDQUFDLEdBQUcsRUFBRWxCLFlBQVlNLEtBQUssQ0FBQyxPQUFPLENBQUM7SUFDcEQ7SUFFQSxTQUFTO0lBQ1QsSUFBSU4sWUFBWW9CLFdBQVcsSUFBSTFCLG9CQUFvQixDQUFDTSxZQUFZb0IsV0FBVyxDQUFzQyxFQUFFO1FBQ2pILE1BQU1DLFdBQVczQixvQkFBb0IsQ0FBQ00sWUFBWW9CLFdBQVcsQ0FBc0M7UUFDbkdMLFlBQVlHLElBQUksQ0FBQ0c7UUFDakJMLGFBQWFFLElBQUksQ0FBQyxDQUFDLE9BQU8sQ0FBQztJQUM3QjtJQUVBLFNBQVM7SUFDVCxJQUFJbEIsWUFBWXNCLFFBQVEsSUFBSTdCLGlCQUFpQixDQUFDTyxZQUFZc0IsUUFBUSxDQUFtQyxFQUFFO1FBQ3JHLE1BQU1DLFlBQVk5QixpQkFBaUIsQ0FBQ08sWUFBWXNCLFFBQVEsQ0FBbUM7UUFDM0ZQLFlBQVlHLElBQUksQ0FBQ0s7UUFDakJQLGFBQWFFLElBQUksQ0FBQyxDQUFDLFNBQVMsQ0FBQztJQUMvQjtJQUVBLFNBQVM7SUFDVCxJQUFJbEIsWUFBWU8sSUFBSSxJQUFJaEIsYUFBYSxDQUFDUyxZQUFZTyxJQUFJLENBQStCLEVBQUU7UUFDckYsTUFBTWlCLFdBQVdqQyxhQUFhLENBQUNTLFlBQVlPLElBQUksQ0FBK0I7UUFDOUVRLFlBQVlHLElBQUksQ0FBQ007UUFDakJSLGFBQWFFLElBQUksQ0FBQyxDQUFDLEdBQUcsRUFBRWxCLFlBQVlPLElBQUksQ0FBQyxJQUFJLENBQUM7SUFDaEQ7SUFFQSxTQUFTO0lBQ1QsSUFBSVAsWUFBWVEsTUFBTSxJQUFJUixZQUFZUSxNQUFNLENBQUNJLE1BQU0sR0FBRyxHQUFHO1FBQ3ZELE1BQU1hLFlBQVksR0FBR3pCLFlBQVlRLE1BQU0sQ0FBQ1YsSUFBSSxDQUFDLE1BQU0sYUFBYSxDQUFDO1FBQ2pFaUIsWUFBWUcsSUFBSSxDQUFDTztRQUNqQlQsYUFBYUUsSUFBSSxDQUFDLENBQUMsT0FBTyxDQUFDO0lBQzdCO0lBRUEsV0FBVztJQUNYLE1BQU1RLG1CQUFtQmxDLGtCQUFrQm1DLEtBQUssQ0FBQyxHQUFHLEdBQUc3QixJQUFJLENBQUM7SUFDNURpQixZQUFZRyxJQUFJLENBQUNRO0lBQ2pCVixhQUFhRSxJQUFJLENBQUM7SUFFbEIsTUFBTVUsa0JBQWtCYixZQUFZakIsSUFBSSxDQUFDO0lBRXpDLFFBQVE7SUFDUixNQUFNK0IsYUFBYUMsS0FBS0MsR0FBRyxDQUFDLEtBQUssTUFBT2YsYUFBYUosTUFBTSxHQUFHO0lBRTlELE9BQU87UUFDTEU7UUFDQWM7UUFDQVo7UUFDQWE7UUFDQUcscUJBQXFCO1lBQ25CQyxhQUFhO1lBQ2IzQixPQUFPTixZQUFZTSxLQUFLLElBQUk7WUFDNUI0QixTQUFTO1FBQ1g7SUFDRjtBQUNGO0FBRUEsY0FBYztBQUNQLFNBQVNDLGdCQUFnQkMsS0FBYTtJQU0zQyxNQUFNQyxPQUFPRCxNQUFNckMsV0FBVztJQUU5QixNQUFNdUMsV0FBcUIsRUFBRTtJQUM3QixNQUFNQyxTQUFtQixFQUFFO0lBQzNCLE1BQU1DLFFBQWtCLEVBQUU7SUFDMUIsTUFBTWhDLFNBQW1CLEVBQUU7SUFFM0IsVUFBVTtJQUNWTCxPQUFPc0MsSUFBSSxDQUFDbkQsZ0JBQWdCb0QsT0FBTyxDQUFDcEMsQ0FBQUE7UUFDbEMsSUFBSStCLEtBQUtoQyxRQUFRLENBQUNDLE1BQU1QLFdBQVcsS0FBSztZQUN0Q3dDLE9BQU9yQixJQUFJLENBQUNaO1FBQ2Q7SUFDRjtJQUVBLFVBQVU7SUFDVkgsT0FBT3NDLElBQUksQ0FBQ2xELGVBQWVtRCxPQUFPLENBQUNuQyxDQUFBQTtRQUNqQyxJQUFJOEIsS0FBS2hDLFFBQVEsQ0FBQ0UsS0FBS1IsV0FBVyxLQUFLO1lBQ3JDeUMsTUFBTXRCLElBQUksQ0FBQ1g7UUFDYjtJQUNGO0lBRUEsVUFBVTtJQUNWLE1BQU1vQyxnQkFBZ0I7UUFBQztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztLQUFJO0lBQ3hFQSxjQUFjRCxPQUFPLENBQUMvQixDQUFBQTtRQUNwQixJQUFJMEIsS0FBS2hDLFFBQVEsQ0FBQ00sUUFBUTtZQUN4QkgsT0FBT1UsSUFBSSxDQUFDUDtRQUNkO0lBQ0Y7SUFFQSxPQUFPO1FBQUUyQjtRQUFVQztRQUFRQztRQUFPaEM7SUFBTztBQUMzQyIsInNvdXJjZXMiOlsiL1VzZXJzL2xpdXBlbmcvY3Vyc29yL+i/m+mYti9GbHV45a+56K+d5byP5bqU55SoL2ZsdXgtY2hhdC1hcHAvc3JjL2xpYi9wcm9tcHRPcHRpbWl6ZXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSW1hZ2VSZXF1aXJlbWVudCwgUHJvbXB0T3B0aW1pemF0aW9uIH0gZnJvbSAnLi90eXBlcyc7XG5cbi8vIOiJuuacr+mjjuagvOaYoOWwhFxuY29uc3QgU1RZTEVfTUFQUElOR1MgPSB7XG4gICflhpnlrp4nOiAncGhvdG9yZWFsaXN0aWMsIGh5cGVycmVhbGlzdGljLCBkZXRhaWxlZCcsXG4gICfljaHpgJonOiAnY2FydG9vbiBzdHlsZSwgYW5pbWF0ZWQsIHN0eWxpemVkJyxcbiAgJ+ayueeUuyc6ICdvaWwgcGFpbnRpbmcsIGNsYXNzaWNhbCBhcnQsIHBhaW50ZXJseScsXG4gICfmsLTlvaknOiAnd2F0ZXJjb2xvciwgc29mdCBicnVzaHN0cm9rZXMsIGZsb3dpbmcnLFxuICAn57Sg5o+PJzogJ3BlbmNpbCBza2V0Y2gsIGNoYXJjb2FsIGRyYXdpbmcsIG1vbm9jaHJvbWUnLFxuICAn5pWw5a2X6Im65pyvJzogJ2RpZ2l0YWwgYXJ0LCBjb25jZXB0IGFydCwgbW9kZXJuJyxcbiAgJ+WDj+e0oOiJuuacryc6ICdwaXhlbCBhcnQsIDgtYml0LCByZXRybyBnYW1pbmcgc3R5bGUnLFxuICAn5oq96LGhJzogJ2Fic3RyYWN0IGFydCwgbm9uLXJlcHJlc2VudGF0aW9uYWwsIGFydGlzdGljJyxcbiAgJ+aegeeugCc6ICdtaW5pbWFsaXN0LCBjbGVhbiwgc2ltcGxlIGNvbXBvc2l0aW9uJyxcbiAgJ+i1m+WNmuaci+WFiyc6ICdjeWJlcnB1bmssIG5lb24gbGlnaHRzLCBmdXR1cmlzdGljLCBkeXN0b3BpYW4nXG59O1xuXG4vLyDmg4Xnu6rmsJvlm7TmmKDlsIRcbmNvbnN0IE1PT0RfTUFQUElOR1MgPSB7XG4gICfmuKnmmpYnOiAnd2FybSwgY296eSwgaW52aXRpbmcgYXRtb3NwaGVyZScsXG4gICflhrfphbcnOiAnY29sZCwgc3RhcmssIGRyYW1hdGljIGxpZ2h0aW5nJyxcbiAgJ+elnuenmCc6ICdteXN0ZXJpb3VzLCBlbmlnbWF0aWMsIGF0bW9zcGhlcmljJyxcbiAgJ+a1qua8qyc6ICdyb21hbnRpYywgZHJlYW15LCBzb2Z0IGxpZ2h0aW5nJyxcbiAgJ+WPsuivlyc6ICdlcGljLCBncmFuZCwgY2luZW1hdGljJyxcbiAgJ+WugemdmSc6ICdwZWFjZWZ1bCwgc2VyZW5lLCBjYWxtJyxcbiAgJ+a0u+WKmyc6ICdlbmVyZ2V0aWMsIHZpYnJhbnQsIGR5bmFtaWMnLFxuICAn5b+n6YOBJzogJ21lbGFuY2hvbGljLCBtb29keSwgY29udGVtcGxhdGl2ZScsXG4gICfmgZDmgJYnOiAnaG9ycm9yLCBkYXJrLCBvbWlub3VzJyxcbiAgJ+asouS5kCc6ICdqb3lmdWwsIGNoZWVyZnVsLCBicmlnaHQnXG59O1xuXG4vLyDotKjph4/lop7lvLror43msYdcbmNvbnN0IFFVQUxJVFlfRU5IQU5DRVJTID0gW1xuICAnaGlnaGx5IGRldGFpbGVkJyxcbiAgJ3NoYXJwIGZvY3VzJyxcbiAgJ3Byb2Zlc3Npb25hbCBwaG90b2dyYXBoeScsXG4gICc4ayByZXNvbHV0aW9uJyxcbiAgJ2F3YXJkIHdpbm5pbmcnLFxuICAnbWFzdGVycGllY2UnLFxuICAndHJlbmRpbmcgb24gYXJ0c3RhdGlvbicsXG4gICdwZXJmZWN0IGNvbXBvc2l0aW9uJ1xuXTtcblxuLy8g5YWJ54Wn5pWI5p6c5pig5bCEXG5jb25zdCBMSUdIVElOR19NQVBQSU5HUyA9IHtcbiAgJ+iHqueEtuWFiSc6ICduYXR1cmFsIGxpZ2h0aW5nLCBzb2Z0IGRheWxpZ2h0JyxcbiAgJ+mHkeiJsuaXtuWFiSc6ICdnb2xkZW4gaG91ciwgd2FybSBzdW5zZXQgbGlnaHRpbmcnLFxuICAn6JOd6Imy5pe25YWJJzogJ2JsdWUgaG91ciwgdHdpbGlnaHQsIGNvb2wgdG9uZXMnLFxuICAn5oiP5Ymn5oCnJzogJ2RyYW1hdGljIGxpZ2h0aW5nLCBoaWdoIGNvbnRyYXN0JyxcbiAgJ+aflOWSjCc6ICdzb2Z0IGxpZ2h0aW5nLCBkaWZmdXNlZCBsaWdodCcsXG4gICfog4zlhYknOiAnYmFja2xpZ2h0aW5nLCByaW0gbGlnaHQsIHNpbGhvdWV0dGUnLFxuICAn6ZyT6Jm5JzogJ25lb24gbGlnaHRpbmcsIGNvbG9yZnVsIGdsb3cnLFxuICAn5pyI5YWJJzogJ21vb25saWdodCwgbm9jdHVybmFsLCBldGhlcmVhbCcsXG4gICflt6XkvZzlrqQnOiAnc3R1ZGlvIGxpZ2h0aW5nLCBwcm9mZXNzaW9uYWwgc2V0dXAnLFxuICAn546v5aKD5YWJJzogJ2FtYmllbnQgbGlnaHRpbmcsIGF0bW9zcGhlcmljJ1xufTtcblxuLy8g5p6E5Zu+5pa55byP5pig5bCEXG5jb25zdCBDT01QT1NJVElPTl9NQVBQSU5HUyA9IHtcbiAgJ+eJueWGmSc6ICdjbG9zZS11cCwgZGV0YWlsZWQgcG9ydHJhaXQnLFxuICAn5YWo6LqrJzogJ2Z1bGwgYm9keSBzaG90LCBjb21wbGV0ZSBmaWd1cmUnLFxuICAn5Lit5pmvJzogJ21lZGl1bSBzaG90LCB3YWlzdCB1cCcsXG4gICfov5zmma8nOiAnd2lkZSBzaG90LCBlc3RhYmxpc2hpbmcgc2hvdCcsXG4gICfpuJ/nnrAnOiAnYWVyaWFsIHZpZXcsIHRvcC1kb3duIHBlcnNwZWN0aXZlJyxcbiAgJ+S9juinkuW6pic6ICdsb3cgYW5nbGUsIGRyYW1hdGljIHBlcnNwZWN0aXZlJyxcbiAgJ+WvueensCc6ICdzeW1tZXRyaWNhbCBjb21wb3NpdGlvbiwgYmFsYW5jZWQnLFxuICAn5LiJ5YiG5rOVJzogJ3J1bGUgb2YgdGhpcmRzLCBkeW5hbWljIGNvbXBvc2l0aW9uJyxcbiAgJ+S4reW/g+aehOWbvic6ICdjZW50ZXJlZCBjb21wb3NpdGlvbiwgZm9jYWwgcG9pbnQnLFxuICAn5a+56KeS57q/JzogJ2RpYWdvbmFsIGNvbXBvc2l0aW9uLCBkeW5hbWljIGxpbmVzJ1xufTtcblxuLy8g5LuO5a+56K+d5Lit5o+Q5Y+W5Zu+5YOP6ZyA5rGCXG5leHBvcnQgZnVuY3Rpb24gZXh0cmFjdEltYWdlUmVxdWlyZW1lbnQoY29udmVyc2F0aW9uSGlzdG9yeTogc3RyaW5nW10pOiBQYXJ0aWFsPEltYWdlUmVxdWlyZW1lbnQ+IHtcbiAgY29uc3QgZnVsbFRleHQgPSBjb252ZXJzYXRpb25IaXN0b3J5LmpvaW4oJyAnKS50b0xvd2VyQ2FzZSgpO1xuICBcbiAgY29uc3QgcmVxdWlyZW1lbnQ6IFBhcnRpYWw8SW1hZ2VSZXF1aXJlbWVudD4gPSB7fTtcbiAgXG4gIC8vIOaPkOWPlumjjuagvFxuICBmb3IgKGNvbnN0IFtrZXksIHZhbHVlXSBvZiBPYmplY3QuZW50cmllcyhTVFlMRV9NQVBQSU5HUykpIHtcbiAgICBpZiAoZnVsbFRleHQuaW5jbHVkZXMoa2V5LnRvTG93ZXJDYXNlKCkpIHx8IGZ1bGxUZXh0LmluY2x1ZGVzKHZhbHVlLnRvTG93ZXJDYXNlKCkpKSB7XG4gICAgICByZXF1aXJlbWVudC5zdHlsZSA9IGtleTtcbiAgICAgIGJyZWFrO1xuICAgIH1cbiAgfVxuICBcbiAgLy8g5o+Q5Y+W5oOF57uqXG4gIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIE9iamVjdC5lbnRyaWVzKE1PT0RfTUFQUElOR1MpKSB7XG4gICAgaWYgKGZ1bGxUZXh0LmluY2x1ZGVzKGtleS50b0xvd2VyQ2FzZSgpKSkge1xuICAgICAgcmVxdWlyZW1lbnQubW9vZCA9IGtleTtcbiAgICAgIGJyZWFrO1xuICAgIH1cbiAgfVxuICBcbiAgLy8g5o+Q5Y+W6aKc6ImyXG4gIGNvbnN0IGNvbG9ycyA9IFsn57qiJywgJ+iTnScsICfnu78nLCAn6buEJywgJ+e0qycsICfmqZknLCAn57KJJywgJ+m7kScsICfnmb0nLCAn54GwJ107XG4gIGNvbnN0IGZvdW5kQ29sb3JzID0gY29sb3JzLmZpbHRlcihjb2xvciA9PiBmdWxsVGV4dC5pbmNsdWRlcyhjb2xvcikpO1xuICBpZiAoZm91bmRDb2xvcnMubGVuZ3RoID4gMCkge1xuICAgIHJlcXVpcmVtZW50LmNvbG9ycyA9IGZvdW5kQ29sb3JzO1xuICB9XG4gIFxuICByZXR1cm4gcmVxdWlyZW1lbnQ7XG59XG5cbi8vIOeUn+aIkOS8mOWMlueahOaPkOekuuivjVxuZXhwb3J0IGZ1bmN0aW9uIGdlbmVyYXRlT3B0aW1pemVkUHJvbXB0KFxuICByZXF1aXJlbWVudDogUGFydGlhbDxJbWFnZVJlcXVpcmVtZW50PixcbiAgb3JpZ2luYWxQcm9tcHQ6IHN0cmluZ1xuKTogUHJvbXB0T3B0aW1pemF0aW9uIHtcbiAgY29uc3QgcHJvbXB0UGFydHM6IHN0cmluZ1tdID0gW107XG4gIGNvbnN0IGltcHJvdmVtZW50czogc3RyaW5nW10gPSBbXTtcbiAgXG4gIC8vIOa3u+WKoOS4u+S9k+aPj+i/sFxuICBpZiAocmVxdWlyZW1lbnQuc3ViamVjdCkge1xuICAgIHByb21wdFBhcnRzLnB1c2gocmVxdWlyZW1lbnQuc3ViamVjdCk7XG4gIH1cbiAgXG4gIC8vIOa3u+WKoOmjjuagvOaPj+i/sFxuICBpZiAocmVxdWlyZW1lbnQuc3R5bGUgJiYgU1RZTEVfTUFQUElOR1NbcmVxdWlyZW1lbnQuc3R5bGUgYXMga2V5b2YgdHlwZW9mIFNUWUxFX01BUFBJTkdTXSkge1xuICAgIGNvbnN0IHN0eWxlRGVzYyA9IFNUWUxFX01BUFBJTkdTW3JlcXVpcmVtZW50LnN0eWxlIGFzIGtleW9mIHR5cGVvZiBTVFlMRV9NQVBQSU5HU107XG4gICAgcHJvbXB0UGFydHMucHVzaChzdHlsZURlc2MpO1xuICAgIGltcHJvdmVtZW50cy5wdXNoKGDmt7vliqDkuoYke3JlcXVpcmVtZW50LnN0eWxlfemjjuagvOeahOS4k+S4muaPj+i/sGApO1xuICB9XG4gIFxuICAvLyDmt7vliqDmnoTlm77mj4/ov7BcbiAgaWYgKHJlcXVpcmVtZW50LmNvbXBvc2l0aW9uICYmIENPTVBPU0lUSU9OX01BUFBJTkdTW3JlcXVpcmVtZW50LmNvbXBvc2l0aW9uIGFzIGtleW9mIHR5cGVvZiBDT01QT1NJVElPTl9NQVBQSU5HU10pIHtcbiAgICBjb25zdCBjb21wRGVzYyA9IENPTVBPU0lUSU9OX01BUFBJTkdTW3JlcXVpcmVtZW50LmNvbXBvc2l0aW9uIGFzIGtleW9mIHR5cGVvZiBDT01QT1NJVElPTl9NQVBQSU5HU107XG4gICAgcHJvbXB0UGFydHMucHVzaChjb21wRGVzYyk7XG4gICAgaW1wcm92ZW1lbnRzLnB1c2goYOS8mOWMluS6huaehOWbvuaPj+i/sGApO1xuICB9XG4gIFxuICAvLyDmt7vliqDlhYnnhafmlYjmnpxcbiAgaWYgKHJlcXVpcmVtZW50LmxpZ2h0aW5nICYmIExJR0hUSU5HX01BUFBJTkdTW3JlcXVpcmVtZW50LmxpZ2h0aW5nIGFzIGtleW9mIHR5cGVvZiBMSUdIVElOR19NQVBQSU5HU10pIHtcbiAgICBjb25zdCBsaWdodERlc2MgPSBMSUdIVElOR19NQVBQSU5HU1tyZXF1aXJlbWVudC5saWdodGluZyBhcyBrZXlvZiB0eXBlb2YgTElHSFRJTkdfTUFQUElOR1NdO1xuICAgIHByb21wdFBhcnRzLnB1c2gobGlnaHREZXNjKTtcbiAgICBpbXByb3ZlbWVudHMucHVzaChg5aKe5by65LqG5YWJ54Wn5pWI5p6c5o+P6L+wYCk7XG4gIH1cbiAgXG4gIC8vIOa3u+WKoOaDhee7quawm+WbtFxuICBpZiAocmVxdWlyZW1lbnQubW9vZCAmJiBNT09EX01BUFBJTkdTW3JlcXVpcmVtZW50Lm1vb2QgYXMga2V5b2YgdHlwZW9mIE1PT0RfTUFQUElOR1NdKSB7XG4gICAgY29uc3QgbW9vZERlc2MgPSBNT09EX01BUFBJTkdTW3JlcXVpcmVtZW50Lm1vb2QgYXMga2V5b2YgdHlwZW9mIE1PT0RfTUFQUElOR1NdO1xuICAgIHByb21wdFBhcnRzLnB1c2gobW9vZERlc2MpO1xuICAgIGltcHJvdmVtZW50cy5wdXNoKGDmt7vliqDkuoYke3JlcXVpcmVtZW50Lm1vb2R95rCb5Zu05o+P6L+wYCk7XG4gIH1cbiAgXG4gIC8vIOa3u+WKoOminOiJsuaWueahiFxuICBpZiAocmVxdWlyZW1lbnQuY29sb3JzICYmIHJlcXVpcmVtZW50LmNvbG9ycy5sZW5ndGggPiAwKSB7XG4gICAgY29uc3QgY29sb3JEZXNjID0gYCR7cmVxdWlyZW1lbnQuY29sb3JzLmpvaW4oJywgJyl9IGNvbG9yIHNjaGVtZWA7XG4gICAgcHJvbXB0UGFydHMucHVzaChjb2xvckRlc2MpO1xuICAgIGltcHJvdmVtZW50cy5wdXNoKGDmjIflrprkuobpopzoibLmlrnmoYhgKTtcbiAgfVxuICBcbiAgLy8g5re75Yqg6LSo6YeP5aKe5by66K+N5rGHXG4gIGNvbnN0IHF1YWxpdHlFbmhhbmNlcnMgPSBRVUFMSVRZX0VOSEFOQ0VSUy5zbGljZSgwLCAzKS5qb2luKCcsICcpO1xuICBwcm9tcHRQYXJ0cy5wdXNoKHF1YWxpdHlFbmhhbmNlcnMpO1xuICBpbXByb3ZlbWVudHMucHVzaCgn5re75Yqg5LqG6LSo6YeP5aKe5by66K+N5rGHJyk7XG4gIFxuICBjb25zdCBvcHRpbWl6ZWRQcm9tcHQgPSBwcm9tcHRQYXJ0cy5qb2luKCcsICcpO1xuICBcbiAgLy8g6K6h566X5L+h5b+D5bqmXG4gIGNvbnN0IGNvbmZpZGVuY2UgPSBNYXRoLm1pbigwLjksIDAuNSArIChpbXByb3ZlbWVudHMubGVuZ3RoICogMC4xKSk7XG4gIFxuICByZXR1cm4ge1xuICAgIG9yaWdpbmFsUHJvbXB0LFxuICAgIG9wdGltaXplZFByb21wdCxcbiAgICBpbXByb3ZlbWVudHMsXG4gICAgY29uZmlkZW5jZSxcbiAgICBzdWdnZXN0ZWRQYXJhbWV0ZXJzOiB7XG4gICAgICBhc3BlY3RSYXRpbzogJzE2OjknLFxuICAgICAgc3R5bGU6IHJlcXVpcmVtZW50LnN0eWxlIHx8ICdhdXRvJyxcbiAgICAgIHF1YWxpdHk6ICdoaWdoJ1xuICAgIH1cbiAgfTtcbn1cblxuLy8g5YiG5p6Q55So5oi36L6T5YWl5Lit55qE5YWz6ZSu6K+NXG5leHBvcnQgZnVuY3Rpb24gYW5hbHl6ZUtleXdvcmRzKGlucHV0OiBzdHJpbmcpOiB7XG4gIHN1YmplY3RzOiBzdHJpbmdbXTtcbiAgc3R5bGVzOiBzdHJpbmdbXTtcbiAgbW9vZHM6IHN0cmluZ1tdO1xuICBjb2xvcnM6IHN0cmluZ1tdO1xufSB7XG4gIGNvbnN0IHRleHQgPSBpbnB1dC50b0xvd2VyQ2FzZSgpO1xuICBcbiAgY29uc3Qgc3ViamVjdHM6IHN0cmluZ1tdID0gW107XG4gIGNvbnN0IHN0eWxlczogc3RyaW5nW10gPSBbXTtcbiAgY29uc3QgbW9vZHM6IHN0cmluZ1tdID0gW107XG4gIGNvbnN0IGNvbG9yczogc3RyaW5nW10gPSBbXTtcbiAgXG4gIC8vIOajgOa1i+mjjuagvOWFs+mUruivjVxuICBPYmplY3Qua2V5cyhTVFlMRV9NQVBQSU5HUykuZm9yRWFjaChzdHlsZSA9PiB7XG4gICAgaWYgKHRleHQuaW5jbHVkZXMoc3R5bGUudG9Mb3dlckNhc2UoKSkpIHtcbiAgICAgIHN0eWxlcy5wdXNoKHN0eWxlKTtcbiAgICB9XG4gIH0pO1xuICBcbiAgLy8g5qOA5rWL5oOF57uq5YWz6ZSu6K+NXG4gIE9iamVjdC5rZXlzKE1PT0RfTUFQUElOR1MpLmZvckVhY2gobW9vZCA9PiB7XG4gICAgaWYgKHRleHQuaW5jbHVkZXMobW9vZC50b0xvd2VyQ2FzZSgpKSkge1xuICAgICAgbW9vZHMucHVzaChtb29kKTtcbiAgICB9XG4gIH0pO1xuICBcbiAgLy8g5qOA5rWL6aKc6Imy5YWz6ZSu6K+NXG4gIGNvbnN0IGNvbG9yS2V5d29yZHMgPSBbJ+e6oicsICfok50nLCAn57u/JywgJ+m7hCcsICfntKsnLCAn5qmZJywgJ+eyiScsICfpu5EnLCAn55m9JywgJ+eBsCddO1xuICBjb2xvcktleXdvcmRzLmZvckVhY2goY29sb3IgPT4ge1xuICAgIGlmICh0ZXh0LmluY2x1ZGVzKGNvbG9yKSkge1xuICAgICAgY29sb3JzLnB1c2goY29sb3IpO1xuICAgIH1cbiAgfSk7XG4gIFxuICByZXR1cm4geyBzdWJqZWN0cywgc3R5bGVzLCBtb29kcywgY29sb3JzIH07XG59XG4iXSwibmFtZXMiOlsiU1RZTEVfTUFQUElOR1MiLCJNT09EX01BUFBJTkdTIiwiUVVBTElUWV9FTkhBTkNFUlMiLCJMSUdIVElOR19NQVBQSU5HUyIsIkNPTVBPU0lUSU9OX01BUFBJTkdTIiwiZXh0cmFjdEltYWdlUmVxdWlyZW1lbnQiLCJjb252ZXJzYXRpb25IaXN0b3J5IiwiZnVsbFRleHQiLCJqb2luIiwidG9Mb3dlckNhc2UiLCJyZXF1aXJlbWVudCIsImtleSIsInZhbHVlIiwiT2JqZWN0IiwiZW50cmllcyIsImluY2x1ZGVzIiwic3R5bGUiLCJtb29kIiwiY29sb3JzIiwiZm91bmRDb2xvcnMiLCJmaWx0ZXIiLCJjb2xvciIsImxlbmd0aCIsImdlbmVyYXRlT3B0aW1pemVkUHJvbXB0Iiwib3JpZ2luYWxQcm9tcHQiLCJwcm9tcHRQYXJ0cyIsImltcHJvdmVtZW50cyIsInN1YmplY3QiLCJwdXNoIiwic3R5bGVEZXNjIiwiY29tcG9zaXRpb24iLCJjb21wRGVzYyIsImxpZ2h0aW5nIiwibGlnaHREZXNjIiwibW9vZERlc2MiLCJjb2xvckRlc2MiLCJxdWFsaXR5RW5oYW5jZXJzIiwic2xpY2UiLCJvcHRpbWl6ZWRQcm9tcHQiLCJjb25maWRlbmNlIiwiTWF0aCIsIm1pbiIsInN1Z2dlc3RlZFBhcmFtZXRlcnMiLCJhc3BlY3RSYXRpbyIsInF1YWxpdHkiLCJhbmFseXplS2V5d29yZHMiLCJpbnB1dCIsInRleHQiLCJzdWJqZWN0cyIsInN0eWxlcyIsIm1vb2RzIiwia2V5cyIsImZvckVhY2giLCJjb2xvcktleXdvcmRzIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/promptOptimizer.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/openai"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();