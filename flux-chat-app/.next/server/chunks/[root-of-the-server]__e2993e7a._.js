module.exports = {

"[project]/.next-internal/server/app/api/chat/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/openai.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "PROMPT_OPTIMIZATION_PROMPT": (()=>PROMPT_OPTIMIZATION_PROMPT),
    "REQUIREMENT_ANALYSIS_PROMPT": (()=>REQUIREMENT_ANALYSIS_PROMPT),
    "analyzeRequirement": (()=>analyzeRequirement),
    "openai": (()=>openai),
    "optimizePrompt": (()=>optimizePrompt)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/index.mjs [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__ = __turbopack_context__.i("[project]/node_modules/openai/client.mjs [app-route] (ecmascript) <export OpenAI as default>");
;
const openai = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$openai$2f$client$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__OpenAI__as__default$3e$__["default"]({
    apiKey: process.env.OPENAI_API_KEY
});
const REQUIREMENT_ANALYSIS_PROMPT = `你是一个专业的图像生成助手，专门帮助用户将模糊的图像需求转换为清晰、具体的描述。

你的任务是：
1. 理解用户的图像生成需求
2. 识别缺失的关键信息
3. 通过友好的对话获取更多细节
4. 提取关键的视觉元素

请关注以下方面：
- 主题/主体 (subject)
- 艺术风格 (style) 
- 情绪/氛围 (mood)
- 色彩方案 (colors)
- 构图方式 (composition)
- 光照效果 (lighting)
- 图像质量要求 (quality)
- 其他重要元素 (additional elements)

请用自然、友好的语气与用户对话，一次只询问1-2个关键问题，避免让用户感到被审问。`;
const PROMPT_OPTIMIZATION_PROMPT = `你是一个专业的AI图像生成提示词优化专家。你需要将用户的需求转换为高质量的图像生成提示词。

优化原则：
1. 使用具体、描述性的词汇
2. 按重要性排序关键词
3. 包含艺术风格和技术参数
4. 添加质量增强词汇
5. 避免模糊或矛盾的描述

提示词结构：
[主体描述], [风格描述], [构图和视角], [光照和氛围], [色彩方案], [质量增强词汇]

请返回JSON格式的结果，包含：
- originalPrompt: 原始描述
- optimizedPrompt: 优化后的提示词
- improvements: 改进说明数组
- confidence: 优化信心度(0-1)
- suggestedParameters: 建议的生成参数`;
async function analyzeRequirement(userInput, conversationHistory) {
    // 检查是否有有效的API密钥
    if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === 'your_openai_api_key_here') {
        return generateDemoResponse(userInput, conversationHistory);
    }
    try {
        const response = await openai.chat.completions.create({
            model: 'gpt-4',
            messages: [
                {
                    role: 'system',
                    content: REQUIREMENT_ANALYSIS_PROMPT
                },
                ...conversationHistory.map((msg, index)=>({
                        role: index % 2 === 0 ? 'user' : 'assistant',
                        content: msg
                    })),
                {
                    role: 'user',
                    content: userInput
                }
            ],
            temperature: 0.7,
            max_tokens: 500
        });
        return response.choices[0]?.message?.content || '';
    } catch (error) {
        console.error('Error analyzing requirement:', error);
        // 回退到演示模式
        return generateDemoResponse(userInput, conversationHistory);
    }
}
// 演示模式响应生成器
function generateDemoResponse(userInput, conversationHistory) {
    const input = userInput.toLowerCase();
    // 简单的关键词检测和响应
    if (conversationHistory.length === 0) {
        // 首次交互
        if (input.includes('猫') || input.includes('cat')) {
            return '听起来很有趣！您想要什么风格的小猫图像呢？比如：\n\n• 写实风格的摄影作品\n• 可爱的卡通风格\n• 油画艺术风格\n• 水彩画风格\n\n另外，您希望小猫在什么环境中呢？';
        } else if (input.includes('风景') || input.includes('landscape')) {
            return '风景画很棒！请告诉我更多细节：\n\n• 您想要什么类型的风景？（山脉、海滩、森林、城市等）\n• 希望是什么时间？（日出、日落、夜晚等）\n• 偏好什么艺术风格？\n• 想要什么样的色彩氛围？';
        } else if (input.includes('人物') || input.includes('人')) {
            return '人物画像需要考虑很多细节！请告诉我：\n\n• 是肖像特写还是全身像？\n• 想要什么风格？（写实、卡通、艺术风格等）\n• 人物的年龄和特征？\n• 背景环境如何？\n• 服装风格有什么要求？';
        } else {
            return '很好的创意！为了帮您生成最佳的提示词，我需要了解更多细节：\n\n• 主要的视觉元素是什么？\n• 您偏好什么艺术风格？\n• 希望营造什么样的氛围？\n• 有特定的色彩偏好吗？\n\n请详细描述您的想法！';
        }
    } else {
        // 后续交互
        if (input.includes('写实') || input.includes('摄影')) {
            return '写实风格很棒！我建议：\n\n• 使用专业摄影术语\n• 强调细节和质感\n• 考虑光照效果\n\n还有其他细节需要补充吗？比如构图方式或特定的光照效果？';
        } else if (input.includes('卡通') || input.includes('可爱')) {
            return '卡通风格会很有趣！建议：\n\n• 强调可爱和友好的特征\n• 使用明亮的色彩\n• 简化的造型设计\n\n您希望是什么样的卡通风格？日式动漫、迪士尼风格，还是其他？';
        } else if (input.includes('颜色') || input.includes('色彩')) {
            return '色彩选择很重要！基于您的描述，我建议：\n\n• 温暖的色调营造舒适感\n• 对比色增加视觉冲击\n• 柔和的色彩营造宁静氛围\n\n您有特定的颜色偏好吗？';
        } else {
            return '很好！基于我们的对话，我已经收集到足够的信息来优化您的提示词了。让我为您生成一个专业的图像生成提示词！';
        }
    }
}
async function optimizePrompt(requirement, conversationContext) {
    // 检查是否有有效的API密钥
    if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === 'your_openai_api_key_here') {
        return generateDemoOptimization(requirement, conversationContext);
    }
    try {
        const response = await openai.chat.completions.create({
            model: 'gpt-4',
            messages: [
                {
                    role: 'system',
                    content: PROMPT_OPTIMIZATION_PROMPT
                },
                {
                    role: 'user',
                    content: `基于以下对话内容和需求，生成优化的图像生成提示词：

对话上下文：
${conversationContext}

最终需求：
${requirement}

请返回JSON格式的优化结果。`
                }
            ],
            temperature: 0.3,
            max_tokens: 800
        });
        const content = response.choices[0]?.message?.content || '';
        try {
            return JSON.parse(content);
        } catch  {
            // 如果JSON解析失败，返回基本结构
            return {
                originalPrompt: requirement,
                optimizedPrompt: content,
                improvements: [
                    'Generated optimized prompt'
                ],
                confidence: 0.8,
                suggestedParameters: {}
            };
        }
    } catch (error) {
        console.error('Error optimizing prompt:', error);
        // 回退到演示模式
        return generateDemoOptimization(requirement, conversationContext);
    }
}
// 演示模式的提示词优化
function generateDemoOptimization(requirement, conversationContext) {
    const context = conversationContext.toLowerCase();
    const req = requirement.toLowerCase();
    let optimizedPrompt = requirement;
    const improvements = [];
    // 基于内容添加优化
    if (req.includes('猫') || req.includes('cat')) {
        optimizedPrompt = `a cute cat, highly detailed, professional photography, soft lighting, warm colors, cozy atmosphere, sharp focus, 8k resolution`;
        improvements.push('添加了专业摄影术语', '增强了可爱特征描述', '指定了光照和色彩');
    } else if (req.includes('风景') || req.includes('landscape')) {
        optimizedPrompt = `beautiful landscape, cinematic composition, golden hour lighting, vibrant colors, highly detailed, professional photography, award winning, masterpiece`;
        improvements.push('添加了电影级构图', '指定了黄金时段光照', '增加了质量增强词汇');
    } else if (req.includes('人物') || req.includes('portrait')) {
        optimizedPrompt = `portrait photography, professional lighting, detailed facial features, high quality, sharp focus, artistic composition, studio lighting`;
        improvements.push('添加了肖像摄影术语', '强调了面部细节', '指定了专业光照');
    } else {
        // 通用优化
        optimizedPrompt = `${requirement}, highly detailed, professional quality, sharp focus, beautiful composition, award winning, masterpiece`;
        improvements.push('添加了质量增强词汇', '改善了整体描述结构');
    }
    // 基于对话上下文进一步优化
    if (context.includes('写实') || context.includes('摄影')) {
        optimizedPrompt += ', photorealistic, hyperrealistic';
        improvements.push('增强了写实风格描述');
    } else if (context.includes('卡通') || context.includes('动漫')) {
        optimizedPrompt += ', cartoon style, animated, stylized art';
        improvements.push('添加了卡通风格描述');
    } else if (context.includes('油画')) {
        optimizedPrompt += ', oil painting, classical art, painterly style';
        improvements.push('添加了油画风格描述');
    }
    return {
        originalPrompt: requirement,
        optimizedPrompt,
        improvements,
        confidence: 0.85,
        suggestedParameters: {
            aspectRatio: '16:9',
            style: context.includes('写实') ? 'photorealistic' : 'artistic',
            quality: 'high'
        }
    };
}
}}),
"[project]/src/lib/promptOptimizer.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "analyzeKeywords": (()=>analyzeKeywords),
    "extractImageRequirement": (()=>extractImageRequirement),
    "generateOptimizedPrompt": (()=>generateOptimizedPrompt)
});
// 艺术风格映射
const STYLE_MAPPINGS = {
    '写实': 'photorealistic, hyperrealistic, detailed',
    '卡通': 'cartoon style, animated, stylized',
    '油画': 'oil painting, classical art, painterly',
    '水彩': 'watercolor, soft brushstrokes, flowing',
    '素描': 'pencil sketch, charcoal drawing, monochrome',
    '数字艺术': 'digital art, concept art, modern',
    '像素艺术': 'pixel art, 8-bit, retro gaming style',
    '抽象': 'abstract art, non-representational, artistic',
    '极简': 'minimalist, clean, simple composition',
    '赛博朋克': 'cyberpunk, neon lights, futuristic, dystopian'
};
// 情绪氛围映射
const MOOD_MAPPINGS = {
    '温暖': 'warm, cozy, inviting atmosphere',
    '冷酷': 'cold, stark, dramatic lighting',
    '神秘': 'mysterious, enigmatic, atmospheric',
    '浪漫': 'romantic, dreamy, soft lighting',
    '史诗': 'epic, grand, cinematic',
    '宁静': 'peaceful, serene, calm',
    '活力': 'energetic, vibrant, dynamic',
    '忧郁': 'melancholic, moody, contemplative',
    '恐怖': 'horror, dark, ominous',
    '欢乐': 'joyful, cheerful, bright'
};
// 质量增强词汇
const QUALITY_ENHANCERS = [
    'highly detailed',
    'sharp focus',
    'professional photography',
    '8k resolution',
    'award winning',
    'masterpiece',
    'trending on artstation',
    'perfect composition'
];
// 光照效果映射
const LIGHTING_MAPPINGS = {
    '自然光': 'natural lighting, soft daylight',
    '金色时光': 'golden hour, warm sunset lighting',
    '蓝色时光': 'blue hour, twilight, cool tones',
    '戏剧性': 'dramatic lighting, high contrast',
    '柔和': 'soft lighting, diffused light',
    '背光': 'backlighting, rim light, silhouette',
    '霓虹': 'neon lighting, colorful glow',
    '月光': 'moonlight, nocturnal, ethereal',
    '工作室': 'studio lighting, professional setup',
    '环境光': 'ambient lighting, atmospheric'
};
// 构图方式映射
const COMPOSITION_MAPPINGS = {
    '特写': 'close-up, detailed portrait',
    '全身': 'full body shot, complete figure',
    '中景': 'medium shot, waist up',
    '远景': 'wide shot, establishing shot',
    '鸟瞰': 'aerial view, top-down perspective',
    '低角度': 'low angle, dramatic perspective',
    '对称': 'symmetrical composition, balanced',
    '三分法': 'rule of thirds, dynamic composition',
    '中心构图': 'centered composition, focal point',
    '对角线': 'diagonal composition, dynamic lines'
};
function extractImageRequirement(conversationHistory) {
    const fullText = conversationHistory.join(' ').toLowerCase();
    const requirement = {};
    // 提取风格
    for (const [key, value] of Object.entries(STYLE_MAPPINGS)){
        if (fullText.includes(key.toLowerCase()) || fullText.includes(value.toLowerCase())) {
            requirement.style = key;
            break;
        }
    }
    // 提取情绪
    for (const [key, value] of Object.entries(MOOD_MAPPINGS)){
        if (fullText.includes(key.toLowerCase())) {
            requirement.mood = key;
            break;
        }
    }
    // 提取颜色
    const colors = [
        '红',
        '蓝',
        '绿',
        '黄',
        '紫',
        '橙',
        '粉',
        '黑',
        '白',
        '灰'
    ];
    const foundColors = colors.filter((color)=>fullText.includes(color));
    if (foundColors.length > 0) {
        requirement.colors = foundColors;
    }
    return requirement;
}
function generateOptimizedPrompt(requirement, originalPrompt) {
    const promptParts = [];
    const improvements = [];
    // 添加主体描述
    if (requirement.subject) {
        promptParts.push(requirement.subject);
    }
    // 添加风格描述
    if (requirement.style && STYLE_MAPPINGS[requirement.style]) {
        const styleDesc = STYLE_MAPPINGS[requirement.style];
        promptParts.push(styleDesc);
        improvements.push(`添加了${requirement.style}风格的专业描述`);
    }
    // 添加构图描述
    if (requirement.composition && COMPOSITION_MAPPINGS[requirement.composition]) {
        const compDesc = COMPOSITION_MAPPINGS[requirement.composition];
        promptParts.push(compDesc);
        improvements.push(`优化了构图描述`);
    }
    // 添加光照效果
    if (requirement.lighting && LIGHTING_MAPPINGS[requirement.lighting]) {
        const lightDesc = LIGHTING_MAPPINGS[requirement.lighting];
        promptParts.push(lightDesc);
        improvements.push(`增强了光照效果描述`);
    }
    // 添加情绪氛围
    if (requirement.mood && MOOD_MAPPINGS[requirement.mood]) {
        const moodDesc = MOOD_MAPPINGS[requirement.mood];
        promptParts.push(moodDesc);
        improvements.push(`添加了${requirement.mood}氛围描述`);
    }
    // 添加颜色方案
    if (requirement.colors && requirement.colors.length > 0) {
        const colorDesc = `${requirement.colors.join(', ')} color scheme`;
        promptParts.push(colorDesc);
        improvements.push(`指定了颜色方案`);
    }
    // 添加质量增强词汇
    const qualityEnhancers = QUALITY_ENHANCERS.slice(0, 3).join(', ');
    promptParts.push(qualityEnhancers);
    improvements.push('添加了质量增强词汇');
    const optimizedPrompt = promptParts.join(', ');
    // 计算信心度
    const confidence = Math.min(0.9, 0.5 + improvements.length * 0.1);
    return {
        originalPrompt,
        optimizedPrompt,
        improvements,
        confidence,
        suggestedParameters: {
            aspectRatio: '16:9',
            style: requirement.style || 'auto',
            quality: 'high'
        }
    };
}
function analyzeKeywords(input) {
    const text = input.toLowerCase();
    const subjects = [];
    const styles = [];
    const moods = [];
    const colors = [];
    // 检测风格关键词
    Object.keys(STYLE_MAPPINGS).forEach((style)=>{
        if (text.includes(style.toLowerCase())) {
            styles.push(style);
        }
    });
    // 检测情绪关键词
    Object.keys(MOOD_MAPPINGS).forEach((mood)=>{
        if (text.includes(mood.toLowerCase())) {
            moods.push(mood);
        }
    });
    // 检测颜色关键词
    const colorKeywords = [
        '红',
        '蓝',
        '绿',
        '黄',
        '紫',
        '橙',
        '粉',
        '黑',
        '白',
        '灰'
    ];
    colorKeywords.forEach((color)=>{
        if (text.includes(color)) {
            colors.push(color);
        }
    });
    return {
        subjects,
        styles,
        moods,
        colors
    };
}
}}),
"[project]/src/app/api/chat/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/openai.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$promptOptimizer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/promptOptimizer.ts [app-route] (ecmascript)");
;
;
;
async function POST(request) {
    try {
        const { message, conversationHistory } = await request.json();
        if (!message) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Message is required'
            }, {
                status: 400
            });
        }
        // 分析用户输入中的关键词
        const keywords = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$promptOptimizer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["analyzeKeywords"])(message);
        // 从对话历史中提取图像需求
        const extractedRequirement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$promptOptimizer$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["extractImageRequirement"])([
            ...conversationHistory,
            message
        ]);
        // 使用OpenAI分析需求并生成回复
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$openai$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["analyzeRequirement"])(message, conversationHistory);
        // 检查是否需要进一步澄清
        const needsClarification = checkIfNeedsClarification(extractedRequirement);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            response,
            extractedRequirement,
            keywords,
            needsClarification,
            metadata: {
                type: needsClarification ? 'clarification' : 'analysis',
                extractedElements: Object.keys(extractedRequirement)
            }
        });
    } catch (error) {
        console.error('Chat API error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Internal server error'
        }, {
            status: 500
        });
    }
}
// 检查是否需要进一步澄清
function checkIfNeedsClarification(requirement) {
    const requiredFields = [
        'subject',
        'style',
        'mood'
    ];
    const missingFields = requiredFields.filter((field)=>!requirement[field]);
    // 如果缺少超过一半的关键信息，需要澄清
    return missingFields.length > requiredFields.length / 2;
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__e2993e7a._.js.map