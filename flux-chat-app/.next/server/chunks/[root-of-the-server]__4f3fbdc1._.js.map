{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/cursor/%E8%BF%9B%E9%98%B6/Flux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8/flux-chat-app/src/lib/openai.ts"], "sourcesContent": ["import OpenAI from 'openai';\n\n// 初始化OpenAI客户端\nexport const openai = new OpenAI({\n  apiKey: process.env.OPENAI_API_KEY,\n});\n\n// 系统提示词 - 用于需求理解和澄清\nexport const REQUIREMENT_ANALYSIS_PROMPT = `你是一个专业的图像生成助手，专门帮助用户将模糊的图像需求转换为清晰、具体的描述。\n\n你的任务是：\n1. 理解用户的图像生成需求\n2. 识别缺失的关键信息\n3. 通过友好的对话获取更多细节\n4. 提取关键的视觉元素\n\n请关注以下方面：\n- 主题/主体 (subject)\n- 艺术风格 (style) \n- 情绪/氛围 (mood)\n- 色彩方案 (colors)\n- 构图方式 (composition)\n- 光照效果 (lighting)\n- 图像质量要求 (quality)\n- 其他重要元素 (additional elements)\n\n请用自然、友好的语气与用户对话，一次只询问1-2个关键问题，避免让用户感到被审问。`;\n\n// 系统提示词 - 用于提示词优化\nexport const PROMPT_OPTIMIZATION_PROMPT = `你是一个专业的AI图像生成提示词优化专家。你需要将用户的需求转换为高质量的图像生成提示词。\n\n优化原则：\n1. 使用具体、描述性的词汇\n2. 按重要性排序关键词\n3. 包含艺术风格和技术参数\n4. 添加质量增强词汇\n5. 避免模糊或矛盾的描述\n\n提示词结构：\n[主体描述], [风格描述], [构图和视角], [光照和氛围], [色彩方案], [质量增强词汇]\n\n请返回JSON格式的结果，包含：\n- originalPrompt: 原始描述\n- optimizedPrompt: 优化后的提示词\n- improvements: 改进说明数组\n- confidence: 优化信心度(0-1)\n- suggestedParameters: 建议的生成参数`;\n\n// 分析用户需求并生成澄清问题\nexport async function analyzeRequirement(userInput: string, conversationHistory: string[]) {\n  // 检查是否有有效的API密钥\n  if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === 'your_openai_api_key_here') {\n    return generateDemoResponse(userInput, conversationHistory);\n  }\n\n  try {\n    const response = await openai.chat.completions.create({\n      model: 'gpt-4',\n      messages: [\n        { role: 'system', content: REQUIREMENT_ANALYSIS_PROMPT },\n        ...conversationHistory.map((msg, index) => ({\n          role: index % 2 === 0 ? 'user' as const : 'assistant' as const,\n          content: msg\n        })),\n        { role: 'user', content: userInput }\n      ],\n      temperature: 0.7,\n      max_tokens: 500,\n    });\n\n    return response.choices[0]?.message?.content || '';\n  } catch (error) {\n    console.error('Error analyzing requirement:', error);\n    // 回退到演示模式\n    return generateDemoResponse(userInput, conversationHistory);\n  }\n}\n\n// 演示模式响应生成器\nfunction generateDemoResponse(userInput: string, conversationHistory: string[]): string {\n  const input = userInput.toLowerCase();\n\n  // 简单的关键词检测和响应\n  if (conversationHistory.length === 0) {\n    // 首次交互\n    if (input.includes('猫') || input.includes('cat')) {\n      return '听起来很有趣！您想要什么风格的小猫图像呢？比如：\\n\\n• 写实风格的摄影作品\\n• 可爱的卡通风格\\n• 油画艺术风格\\n• 水彩画风格\\n\\n另外，您希望小猫在什么环境中呢？';\n    } else if (input.includes('风景') || input.includes('landscape')) {\n      return '风景画很棒！请告诉我更多细节：\\n\\n• 您想要什么类型的风景？（山脉、海滩、森林、城市等）\\n• 希望是什么时间？（日出、日落、夜晚等）\\n• 偏好什么艺术风格？\\n• 想要什么样的色彩氛围？';\n    } else if (input.includes('人物') || input.includes('人')) {\n      return '人物画像需要考虑很多细节！请告诉我：\\n\\n• 是肖像特写还是全身像？\\n• 想要什么风格？（写实、卡通、艺术风格等）\\n• 人物的年龄和特征？\\n• 背景环境如何？\\n• 服装风格有什么要求？';\n    } else {\n      return '很好的创意！为了帮您生成最佳的提示词，我需要了解更多细节：\\n\\n• 主要的视觉元素是什么？\\n• 您偏好什么艺术风格？\\n• 希望营造什么样的氛围？\\n• 有特定的色彩偏好吗？\\n\\n请详细描述您的想法！';\n    }\n  } else {\n    // 后续交互\n    if (input.includes('写实') || input.includes('摄影')) {\n      return '写实风格很棒！我建议：\\n\\n• 使用专业摄影术语\\n• 强调细节和质感\\n• 考虑光照效果\\n\\n还有其他细节需要补充吗？比如构图方式或特定的光照效果？';\n    } else if (input.includes('卡通') || input.includes('可爱')) {\n      return '卡通风格会很有趣！建议：\\n\\n• 强调可爱和友好的特征\\n• 使用明亮的色彩\\n• 简化的造型设计\\n\\n您希望是什么样的卡通风格？日式动漫、迪士尼风格，还是其他？';\n    } else if (input.includes('颜色') || input.includes('色彩')) {\n      return '色彩选择很重要！基于您的描述，我建议：\\n\\n• 温暖的色调营造舒适感\\n• 对比色增加视觉冲击\\n• 柔和的色彩营造宁静氛围\\n\\n您有特定的颜色偏好吗？';\n    } else {\n      return '很好！基于我们的对话，我已经收集到足够的信息来优化您的提示词了。让我为您生成一个专业的图像生成提示词！';\n    }\n  }\n}\n\n// 优化提示词\nexport async function optimizePrompt(requirement: string, conversationContext: string) {\n  // 检查是否有有效的API密钥\n  if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === 'your_openai_api_key_here') {\n    return generateDemoOptimization(requirement, conversationContext);\n  }\n\n  try {\n    const response = await openai.chat.completions.create({\n      model: 'gpt-4',\n      messages: [\n        { role: 'system', content: PROMPT_OPTIMIZATION_PROMPT },\n        {\n          role: 'user',\n          content: `基于以下对话内容和需求，生成优化的图像生成提示词：\n\n对话上下文：\n${conversationContext}\n\n最终需求：\n${requirement}\n\n请返回JSON格式的优化结果。`\n        }\n      ],\n      temperature: 0.3,\n      max_tokens: 800,\n    });\n\n    const content = response.choices[0]?.message?.content || '';\n\n    try {\n      return JSON.parse(content);\n    } catch {\n      // 如果JSON解析失败，返回基本结构\n      return {\n        originalPrompt: requirement,\n        optimizedPrompt: content,\n        improvements: ['Generated optimized prompt'],\n        confidence: 0.8,\n        suggestedParameters: {}\n      };\n    }\n  } catch (error) {\n    console.error('Error optimizing prompt:', error);\n    // 回退到演示模式\n    return generateDemoOptimization(requirement, conversationContext);\n  }\n}\n\n// 演示模式的提示词优化\nfunction generateDemoOptimization(requirement: string, conversationContext: string) {\n  const context = conversationContext.toLowerCase();\n  const req = requirement.toLowerCase();\n\n  let optimizedPrompt = requirement;\n  const improvements = [];\n\n  // 基于内容添加优化\n  if (req.includes('猫') || req.includes('cat')) {\n    optimizedPrompt = `a cute cat, highly detailed, professional photography, soft lighting, warm colors, cozy atmosphere, sharp focus, 8k resolution`;\n    improvements.push('添加了专业摄影术语', '增强了可爱特征描述', '指定了光照和色彩');\n  } else if (req.includes('风景') || req.includes('landscape')) {\n    optimizedPrompt = `beautiful landscape, cinematic composition, golden hour lighting, vibrant colors, highly detailed, professional photography, award winning, masterpiece`;\n    improvements.push('添加了电影级构图', '指定了黄金时段光照', '增加了质量增强词汇');\n  } else if (req.includes('人物') || req.includes('portrait')) {\n    optimizedPrompt = `portrait photography, professional lighting, detailed facial features, high quality, sharp focus, artistic composition, studio lighting`;\n    improvements.push('添加了肖像摄影术语', '强调了面部细节', '指定了专业光照');\n  } else {\n    // 通用优化\n    optimizedPrompt = `${requirement}, highly detailed, professional quality, sharp focus, beautiful composition, award winning, masterpiece`;\n    improvements.push('添加了质量增强词汇', '改善了整体描述结构');\n  }\n\n  // 基于对话上下文进一步优化\n  if (context.includes('写实') || context.includes('摄影')) {\n    optimizedPrompt += ', photorealistic, hyperrealistic';\n    improvements.push('增强了写实风格描述');\n  } else if (context.includes('卡通') || context.includes('动漫')) {\n    optimizedPrompt += ', cartoon style, animated, stylized art';\n    improvements.push('添加了卡通风格描述');\n  } else if (context.includes('油画')) {\n    optimizedPrompt += ', oil painting, classical art, painterly style';\n    improvements.push('添加了油画风格描述');\n  }\n\n  return {\n    originalPrompt: requirement,\n    optimizedPrompt,\n    improvements,\n    confidence: 0.85,\n    suggestedParameters: {\n      aspectRatio: '16:9',\n      style: context.includes('写实') ? 'photorealistic' : 'artistic',\n      quality: 'high'\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;;AAGO,MAAM,SAAS,IAAI,wKAAA,CAAA,UAAM,CAAC;IAC/B,QAAQ,QAAQ,GAAG,CAAC,cAAc;AACpC;AAGO,MAAM,8BAA8B,CAAC;;;;;;;;;;;;;;;;;;yCAkBH,CAAC;AAGnC,MAAM,6BAA6B,CAAC;;;;;;;;;;;;;;;;;8BAiBb,CAAC;AAGxB,eAAe,mBAAmB,SAAiB,EAAE,mBAA6B;IACvF,gBAAgB;IAChB,IAAI,CAAC,QAAQ,GAAG,CAAC,cAAc,IAAI,QAAQ,GAAG,CAAC,cAAc,KAAK,4BAA4B;QAC5F,OAAO,qBAAqB,WAAW;IACzC;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACpD,OAAO;YACP,UAAU;gBACR;oBAAE,MAAM;oBAAU,SAAS;gBAA4B;mBACpD,oBAAoB,GAAG,CAAC,CAAC,KAAK,QAAU,CAAC;wBAC1C,MAAM,QAAQ,MAAM,IAAI,SAAkB;wBAC1C,SAAS;oBACX,CAAC;gBACD;oBAAE,MAAM;oBAAQ,SAAS;gBAAU;aACpC;YACD,aAAa;YACb,YAAY;QACd;QAEA,OAAO,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;IAClD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,UAAU;QACV,OAAO,qBAAqB,WAAW;IACzC;AACF;AAEA,YAAY;AACZ,SAAS,qBAAqB,SAAiB,EAAE,mBAA6B;IAC5E,MAAM,QAAQ,UAAU,WAAW;IAEnC,cAAc;IACd,IAAI,oBAAoB,MAAM,KAAK,GAAG;QACpC,OAAO;QACP,IAAI,MAAM,QAAQ,CAAC,QAAQ,MAAM,QAAQ,CAAC,QAAQ;YAChD,OAAO;QACT,OAAO,IAAI,MAAM,QAAQ,CAAC,SAAS,MAAM,QAAQ,CAAC,cAAc;YAC9D,OAAO;QACT,OAAO,IAAI,MAAM,QAAQ,CAAC,SAAS,MAAM,QAAQ,CAAC,MAAM;YACtD,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF,OAAO;QACL,OAAO;QACP,IAAI,MAAM,QAAQ,CAAC,SAAS,MAAM,QAAQ,CAAC,OAAO;YAChD,OAAO;QACT,OAAO,IAAI,MAAM,QAAQ,CAAC,SAAS,MAAM,QAAQ,CAAC,OAAO;YACvD,OAAO;QACT,OAAO,IAAI,MAAM,QAAQ,CAAC,SAAS,MAAM,QAAQ,CAAC,OAAO;YACvD,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF;AACF;AAGO,eAAe,eAAe,WAAmB,EAAE,mBAA2B;IACnF,gBAAgB;IAChB,IAAI,CAAC,QAAQ,GAAG,CAAC,cAAc,IAAI,QAAQ,GAAG,CAAC,cAAc,KAAK,4BAA4B;QAC5F,OAAO,yBAAyB,aAAa;IAC/C;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACpD,OAAO;YACP,UAAU;gBACR;oBAAE,MAAM;oBAAU,SAAS;gBAA2B;gBACtD;oBACE,MAAM;oBACN,SAAS,CAAC;;;AAGpB,EAAE,oBAAoB;;;AAGtB,EAAE,YAAY;;eAEC,CAAC;gBACR;aACD;YACD,aAAa;YACb,YAAY;QACd;QAEA,MAAM,UAAU,SAAS,OAAO,CAAC,EAAE,EAAE,SAAS,WAAW;QAEzD,IAAI;YACF,OAAO,KAAK,KAAK,CAAC;QACpB,EAAE,OAAM;YACN,oBAAoB;YACpB,OAAO;gBACL,gBAAgB;gBAChB,iBAAiB;gBACjB,cAAc;oBAAC;iBAA6B;gBAC5C,YAAY;gBACZ,qBAAqB,CAAC;YACxB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,UAAU;QACV,OAAO,yBAAyB,aAAa;IAC/C;AACF;AAEA,aAAa;AACb,SAAS,yBAAyB,WAAmB,EAAE,mBAA2B;IAChF,MAAM,UAAU,oBAAoB,WAAW;IAC/C,MAAM,MAAM,YAAY,WAAW;IAEnC,IAAI,kBAAkB;IACtB,MAAM,eAAe,EAAE;IAEvB,WAAW;IACX,IAAI,IAAI,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ;QAC5C,kBAAkB,CAAC,8HAA8H,CAAC;QAClJ,aAAa,IAAI,CAAC,aAAa,aAAa;IAC9C,OAAO,IAAI,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,cAAc;QAC1D,kBAAkB,CAAC,uJAAuJ,CAAC;QAC3K,aAAa,IAAI,CAAC,YAAY,aAAa;IAC7C,OAAO,IAAI,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,aAAa;QACzD,kBAAkB,CAAC,uIAAuI,CAAC;QAC3J,aAAa,IAAI,CAAC,aAAa,WAAW;IAC5C,OAAO;QACL,OAAO;QACP,kBAAkB,GAAG,YAAY,uGAAuG,CAAC;QACzI,aAAa,IAAI,CAAC,aAAa;IACjC;IAEA,eAAe;IACf,IAAI,QAAQ,QAAQ,CAAC,SAAS,QAAQ,QAAQ,CAAC,OAAO;QACpD,mBAAmB;QACnB,aAAa,IAAI,CAAC;IACpB,OAAO,IAAI,QAAQ,QAAQ,CAAC,SAAS,QAAQ,QAAQ,CAAC,OAAO;QAC3D,mBAAmB;QACnB,aAAa,IAAI,CAAC;IACpB,OAAO,IAAI,QAAQ,QAAQ,CAAC,OAAO;QACjC,mBAAmB;QACnB,aAAa,IAAI,CAAC;IACpB;IAEA,OAAO;QACL,gBAAgB;QAChB;QACA;QACA,YAAY;QACZ,qBAAqB;YACnB,aAAa;YACb,OAAO,QAAQ,QAAQ,CAAC,QAAQ,mBAAmB;YACnD,SAAS;QACX;IACF;AACF", "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/cursor/%E8%BF%9B%E9%98%B6/Flux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8/flux-chat-app/src/lib/promptOptimizer.ts"], "sourcesContent": ["import { ImageRequirement, PromptOptimization } from './types';\n\n// 艺术风格映射\nconst STYLE_MAPPINGS = {\n  '写实': 'photorealistic, hyperrealistic, detailed',\n  '卡通': 'cartoon style, animated, stylized',\n  '油画': 'oil painting, classical art, painterly',\n  '水彩': 'watercolor, soft brushstrokes, flowing',\n  '素描': 'pencil sketch, charcoal drawing, monochrome',\n  '数字艺术': 'digital art, concept art, modern',\n  '像素艺术': 'pixel art, 8-bit, retro gaming style',\n  '抽象': 'abstract art, non-representational, artistic',\n  '极简': 'minimalist, clean, simple composition',\n  '赛博朋克': 'cyberpunk, neon lights, futuristic, dystopian'\n};\n\n// 情绪氛围映射\nconst MOOD_MAPPINGS = {\n  '温暖': 'warm, cozy, inviting atmosphere',\n  '冷酷': 'cold, stark, dramatic lighting',\n  '神秘': 'mysterious, enigmatic, atmospheric',\n  '浪漫': 'romantic, dreamy, soft lighting',\n  '史诗': 'epic, grand, cinematic',\n  '宁静': 'peaceful, serene, calm',\n  '活力': 'energetic, vibrant, dynamic',\n  '忧郁': 'melancholic, moody, contemplative',\n  '恐怖': 'horror, dark, ominous',\n  '欢乐': 'joyful, cheerful, bright'\n};\n\n// 质量增强词汇\nconst QUALITY_ENHANCERS = [\n  'highly detailed',\n  'sharp focus',\n  'professional photography',\n  '8k resolution',\n  'award winning',\n  'masterpiece',\n  'trending on artstation',\n  'perfect composition'\n];\n\n// 光照效果映射\nconst LIGHTING_MAPPINGS = {\n  '自然光': 'natural lighting, soft daylight',\n  '金色时光': 'golden hour, warm sunset lighting',\n  '蓝色时光': 'blue hour, twilight, cool tones',\n  '戏剧性': 'dramatic lighting, high contrast',\n  '柔和': 'soft lighting, diffused light',\n  '背光': 'backlighting, rim light, silhouette',\n  '霓虹': 'neon lighting, colorful glow',\n  '月光': 'moonlight, nocturnal, ethereal',\n  '工作室': 'studio lighting, professional setup',\n  '环境光': 'ambient lighting, atmospheric'\n};\n\n// 构图方式映射\nconst COMPOSITION_MAPPINGS = {\n  '特写': 'close-up, detailed portrait',\n  '全身': 'full body shot, complete figure',\n  '中景': 'medium shot, waist up',\n  '远景': 'wide shot, establishing shot',\n  '鸟瞰': 'aerial view, top-down perspective',\n  '低角度': 'low angle, dramatic perspective',\n  '对称': 'symmetrical composition, balanced',\n  '三分法': 'rule of thirds, dynamic composition',\n  '中心构图': 'centered composition, focal point',\n  '对角线': 'diagonal composition, dynamic lines'\n};\n\n// 从对话中提取图像需求\nexport function extractImageRequirement(conversationHistory: string[]): Partial<ImageRequirement> {\n  const fullText = conversationHistory.join(' ').toLowerCase();\n  \n  const requirement: Partial<ImageRequirement> = {};\n  \n  // 提取风格\n  for (const [key, value] of Object.entries(STYLE_MAPPINGS)) {\n    if (fullText.includes(key.toLowerCase()) || fullText.includes(value.toLowerCase())) {\n      requirement.style = key;\n      break;\n    }\n  }\n  \n  // 提取情绪\n  for (const [key, value] of Object.entries(MOOD_MAPPINGS)) {\n    if (fullText.includes(key.toLowerCase())) {\n      requirement.mood = key;\n      break;\n    }\n  }\n  \n  // 提取颜色\n  const colors = ['红', '蓝', '绿', '黄', '紫', '橙', '粉', '黑', '白', '灰'];\n  const foundColors = colors.filter(color => fullText.includes(color));\n  if (foundColors.length > 0) {\n    requirement.colors = foundColors;\n  }\n  \n  return requirement;\n}\n\n// 生成优化的提示词\nexport function generateOptimizedPrompt(\n  requirement: Partial<ImageRequirement>,\n  originalPrompt: string\n): PromptOptimization {\n  const promptParts: string[] = [];\n  const improvements: string[] = [];\n  \n  // 添加主体描述\n  if (requirement.subject) {\n    promptParts.push(requirement.subject);\n  }\n  \n  // 添加风格描述\n  if (requirement.style && STYLE_MAPPINGS[requirement.style as keyof typeof STYLE_MAPPINGS]) {\n    const styleDesc = STYLE_MAPPINGS[requirement.style as keyof typeof STYLE_MAPPINGS];\n    promptParts.push(styleDesc);\n    improvements.push(`添加了${requirement.style}风格的专业描述`);\n  }\n  \n  // 添加构图描述\n  if (requirement.composition && COMPOSITION_MAPPINGS[requirement.composition as keyof typeof COMPOSITION_MAPPINGS]) {\n    const compDesc = COMPOSITION_MAPPINGS[requirement.composition as keyof typeof COMPOSITION_MAPPINGS];\n    promptParts.push(compDesc);\n    improvements.push(`优化了构图描述`);\n  }\n  \n  // 添加光照效果\n  if (requirement.lighting && LIGHTING_MAPPINGS[requirement.lighting as keyof typeof LIGHTING_MAPPINGS]) {\n    const lightDesc = LIGHTING_MAPPINGS[requirement.lighting as keyof typeof LIGHTING_MAPPINGS];\n    promptParts.push(lightDesc);\n    improvements.push(`增强了光照效果描述`);\n  }\n  \n  // 添加情绪氛围\n  if (requirement.mood && MOOD_MAPPINGS[requirement.mood as keyof typeof MOOD_MAPPINGS]) {\n    const moodDesc = MOOD_MAPPINGS[requirement.mood as keyof typeof MOOD_MAPPINGS];\n    promptParts.push(moodDesc);\n    improvements.push(`添加了${requirement.mood}氛围描述`);\n  }\n  \n  // 添加颜色方案\n  if (requirement.colors && requirement.colors.length > 0) {\n    const colorDesc = `${requirement.colors.join(', ')} color scheme`;\n    promptParts.push(colorDesc);\n    improvements.push(`指定了颜色方案`);\n  }\n  \n  // 添加质量增强词汇\n  const qualityEnhancers = QUALITY_ENHANCERS.slice(0, 3).join(', ');\n  promptParts.push(qualityEnhancers);\n  improvements.push('添加了质量增强词汇');\n  \n  const optimizedPrompt = promptParts.join(', ');\n  \n  // 计算信心度\n  const confidence = Math.min(0.9, 0.5 + (improvements.length * 0.1));\n  \n  return {\n    originalPrompt,\n    optimizedPrompt,\n    improvements,\n    confidence,\n    suggestedParameters: {\n      aspectRatio: '16:9',\n      style: requirement.style || 'auto',\n      quality: 'high'\n    }\n  };\n}\n\n// 分析用户输入中的关键词\nexport function analyzeKeywords(input: string): {\n  subjects: string[];\n  styles: string[];\n  moods: string[];\n  colors: string[];\n} {\n  const text = input.toLowerCase();\n  \n  const subjects: string[] = [];\n  const styles: string[] = [];\n  const moods: string[] = [];\n  const colors: string[] = [];\n  \n  // 检测风格关键词\n  Object.keys(STYLE_MAPPINGS).forEach(style => {\n    if (text.includes(style.toLowerCase())) {\n      styles.push(style);\n    }\n  });\n  \n  // 检测情绪关键词\n  Object.keys(MOOD_MAPPINGS).forEach(mood => {\n    if (text.includes(mood.toLowerCase())) {\n      moods.push(mood);\n    }\n  });\n  \n  // 检测颜色关键词\n  const colorKeywords = ['红', '蓝', '绿', '黄', '紫', '橙', '粉', '黑', '白', '灰'];\n  colorKeywords.forEach(color => {\n    if (text.includes(color)) {\n      colors.push(color);\n    }\n  });\n  \n  return { subjects, styles, moods, colors };\n}\n"], "names": [], "mappings": ";;;;;AAEA,SAAS;AACT,MAAM,iBAAiB;IACrB,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,MAAM;IACN,QAAQ;AACV;AAEA,SAAS;AACT,MAAM,gBAAgB;IACpB,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;AACR;AAEA,SAAS;AACT,MAAM,oBAAoB;IACxB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAAS;AACT,MAAM,oBAAoB;IACxB,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;IACP,OAAO;AACT;AAEA,SAAS;AACT,MAAM,uBAAuB;IAC3B,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,OAAO;IACP,QAAQ;IACR,OAAO;AACT;AAGO,SAAS,wBAAwB,mBAA6B;IACnE,MAAM,WAAW,oBAAoB,IAAI,CAAC,KAAK,WAAW;IAE1D,MAAM,cAAyC,CAAC;IAEhD,OAAO;IACP,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,gBAAiB;QACzD,IAAI,SAAS,QAAQ,CAAC,IAAI,WAAW,OAAO,SAAS,QAAQ,CAAC,MAAM,WAAW,KAAK;YAClF,YAAY,KAAK,GAAG;YACpB;QACF;IACF;IAEA,OAAO;IACP,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,eAAgB;QACxD,IAAI,SAAS,QAAQ,CAAC,IAAI,WAAW,KAAK;YACxC,YAAY,IAAI,GAAG;YACnB;QACF;IACF;IAEA,OAAO;IACP,MAAM,SAAS;QAAC;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;KAAI;IACjE,MAAM,cAAc,OAAO,MAAM,CAAC,CAAA,QAAS,SAAS,QAAQ,CAAC;IAC7D,IAAI,YAAY,MAAM,GAAG,GAAG;QAC1B,YAAY,MAAM,GAAG;IACvB;IAEA,OAAO;AACT;AAGO,SAAS,wBACd,WAAsC,EACtC,cAAsB;IAEtB,MAAM,cAAwB,EAAE;IAChC,MAAM,eAAyB,EAAE;IAEjC,SAAS;IACT,IAAI,YAAY,OAAO,EAAE;QACvB,YAAY,IAAI,CAAC,YAAY,OAAO;IACtC;IAEA,SAAS;IACT,IAAI,YAAY,KAAK,IAAI,cAAc,CAAC,YAAY,KAAK,CAAgC,EAAE;QACzF,MAAM,YAAY,cAAc,CAAC,YAAY,KAAK,CAAgC;QAClF,YAAY,IAAI,CAAC;QACjB,aAAa,IAAI,CAAC,CAAC,GAAG,EAAE,YAAY,KAAK,CAAC,OAAO,CAAC;IACpD;IAEA,SAAS;IACT,IAAI,YAAY,WAAW,IAAI,oBAAoB,CAAC,YAAY,WAAW,CAAsC,EAAE;QACjH,MAAM,WAAW,oBAAoB,CAAC,YAAY,WAAW,CAAsC;QACnG,YAAY,IAAI,CAAC;QACjB,aAAa,IAAI,CAAC,CAAC,OAAO,CAAC;IAC7B;IAEA,SAAS;IACT,IAAI,YAAY,QAAQ,IAAI,iBAAiB,CAAC,YAAY,QAAQ,CAAmC,EAAE;QACrG,MAAM,YAAY,iBAAiB,CAAC,YAAY,QAAQ,CAAmC;QAC3F,YAAY,IAAI,CAAC;QACjB,aAAa,IAAI,CAAC,CAAC,SAAS,CAAC;IAC/B;IAEA,SAAS;IACT,IAAI,YAAY,IAAI,IAAI,aAAa,CAAC,YAAY,IAAI,CAA+B,EAAE;QACrF,MAAM,WAAW,aAAa,CAAC,YAAY,IAAI,CAA+B;QAC9E,YAAY,IAAI,CAAC;QACjB,aAAa,IAAI,CAAC,CAAC,GAAG,EAAE,YAAY,IAAI,CAAC,IAAI,CAAC;IAChD;IAEA,SAAS;IACT,IAAI,YAAY,MAAM,IAAI,YAAY,MAAM,CAAC,MAAM,GAAG,GAAG;QACvD,MAAM,YAAY,GAAG,YAAY,MAAM,CAAC,IAAI,CAAC,MAAM,aAAa,CAAC;QACjE,YAAY,IAAI,CAAC;QACjB,aAAa,IAAI,CAAC,CAAC,OAAO,CAAC;IAC7B;IAEA,WAAW;IACX,MAAM,mBAAmB,kBAAkB,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;IAC5D,YAAY,IAAI,CAAC;IACjB,aAAa,IAAI,CAAC;IAElB,MAAM,kBAAkB,YAAY,IAAI,CAAC;IAEzC,QAAQ;IACR,MAAM,aAAa,KAAK,GAAG,CAAC,KAAK,MAAO,aAAa,MAAM,GAAG;IAE9D,OAAO;QACL;QACA;QACA;QACA;QACA,qBAAqB;YACnB,aAAa;YACb,OAAO,YAAY,KAAK,IAAI;YAC5B,SAAS;QACX;IACF;AACF;AAGO,SAAS,gBAAgB,KAAa;IAM3C,MAAM,OAAO,MAAM,WAAW;IAE9B,MAAM,WAAqB,EAAE;IAC7B,MAAM,SAAmB,EAAE;IAC3B,MAAM,QAAkB,EAAE;IAC1B,MAAM,SAAmB,EAAE;IAE3B,UAAU;IACV,OAAO,IAAI,CAAC,gBAAgB,OAAO,CAAC,CAAA;QAClC,IAAI,KAAK,QAAQ,CAAC,MAAM,WAAW,KAAK;YACtC,OAAO,IAAI,CAAC;QACd;IACF;IAEA,UAAU;IACV,OAAO,IAAI,CAAC,eAAe,OAAO,CAAC,CAAA;QACjC,IAAI,KAAK,QAAQ,CAAC,KAAK,WAAW,KAAK;YACrC,MAAM,IAAI,CAAC;QACb;IACF;IAEA,UAAU;IACV,MAAM,gBAAgB;QAAC;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;KAAI;IACxE,cAAc,OAAO,CAAC,CAAA;QACpB,IAAI,KAAK,QAAQ,CAAC,QAAQ;YACxB,OAAO,IAAI,CAAC;QACd;IACF;IAEA,OAAO;QAAE;QAAU;QAAQ;QAAO;IAAO;AAC3C", "debugId": null}}, {"offset": {"line": 479, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/cursor/%E8%BF%9B%E9%98%B6/Flux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8/flux-chat-app/src/app/api/optimize-prompt/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { optimizePrompt } from '@/lib/openai';\nimport { generateOptimizedPrompt, extractImageRequirement } from '@/lib/promptOptimizer';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { requirement, conversationHistory, useAI = true } = await request.json();\n\n    if (!requirement) {\n      return NextResponse.json(\n        { error: 'Requirement is required' },\n        { status: 400 }\n      );\n    }\n\n    let optimizationResult;\n\n    if (useAI) {\n      // 使用AI优化提示词\n      const conversationContext = conversationHistory.join('\\n');\n      optimizationResult = await optimizePrompt(requirement, conversationContext);\n    } else {\n      // 使用本地规则优化提示词\n      const extractedRequirement = extractImageRequirement(conversationHistory);\n      optimizationResult = generateOptimizedPrompt(extractedRequirement, requirement);\n    }\n\n    return NextResponse.json({\n      optimization: optimizationResult,\n      success: true\n    });\n\n  } catch (error) {\n    console.error('Prompt optimization error:', error);\n    \n    // 如果AI优化失败，回退到本地优化\n    try {\n      const { requirement, conversationHistory } = await request.json();\n      const extractedRequirement = extractImageRequirement(conversationHistory);\n      const fallbackResult = generateOptimizedPrompt(extractedRequirement, requirement);\n      \n      return NextResponse.json({\n        optimization: fallbackResult,\n        success: true,\n        fallback: true\n      });\n    } catch (fallbackError) {\n      return NextResponse.json(\n        { error: 'Failed to optimize prompt' },\n        { status: 500 }\n      );\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,WAAW,EAAE,mBAAmB,EAAE,QAAQ,IAAI,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE7E,IAAI,CAAC,aAAa;YAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI;QAEJ,IAAI,OAAO;YACT,YAAY;YACZ,MAAM,sBAAsB,oBAAoB,IAAI,CAAC;YACrD,qBAAqB,MAAM,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa;QACzD,OAAO;YACL,cAAc;YACd,MAAM,uBAAuB,CAAA,GAAA,+HAAA,CAAA,0BAAuB,AAAD,EAAE;YACrD,qBAAqB,CAAA,GAAA,+HAAA,CAAA,0BAAuB,AAAD,EAAE,sBAAsB;QACrE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,cAAc;YACd,SAAS;QACX;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAE5C,mBAAmB;QACnB,IAAI;YACF,MAAM,EAAE,WAAW,EAAE,mBAAmB,EAAE,GAAG,MAAM,QAAQ,IAAI;YAC/D,MAAM,uBAAuB,CAAA,GAAA,+HAAA,CAAA,0BAAuB,AAAD,EAAE;YACrD,MAAM,iBAAiB,CAAA,GAAA,+HAAA,CAAA,0BAAuB,AAAD,EAAE,sBAAsB;YAErE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,cAAc;gBACd,SAAS;gBACT,UAAU;YACZ;QACF,EAAE,OAAO,eAAe;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA4B,GACrC;gBAAE,QAAQ;YAAI;QAElB;IACF;AACF", "debugId": null}}]}