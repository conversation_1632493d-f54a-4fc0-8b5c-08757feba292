"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/Chat/ImageDisplay.tsx":
/*!**********************************************!*\
  !*** ./src/components/Chat/ImageDisplay.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ImageDisplay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,CpuChipIcon,HeartIcon,MagnifyingGlassIcon,PhotoIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,CpuChipIcon,HeartIcon,MagnifyingGlassIcon,PhotoIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,CpuChipIcon,HeartIcon,MagnifyingGlassIcon,PhotoIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,CpuChipIcon,HeartIcon,MagnifyingGlassIcon,PhotoIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,CpuChipIcon,HeartIcon,MagnifyingGlassIcon,PhotoIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,CpuChipIcon,HeartIcon,MagnifyingGlassIcon,PhotoIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,CpuChipIcon,HeartIcon,MagnifyingGlassIcon,PhotoIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShareIcon.js\");\n/* harmony import */ var _barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/HeartIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ImageDisplay(param) {\n    let { images, metadata, isLoading = false, onRegenerate, onVariation } = param;\n    _s();\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [likedImages, setLikedImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // 下载图像\n    const downloadImage = async (imageUrl, index)=>{\n        try {\n            const response = await fetch(imageUrl);\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = \"generated-image-\".concat(index + 1, \".jpg\");\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n        } catch (error) {\n            console.error('Download failed:', error);\n        }\n    };\n    // 分享图像\n    const shareImage = async (imageUrl)=>{\n        if (navigator.share) {\n            try {\n                await navigator.share({\n                    title: 'AI生成的图像',\n                    text: metadata.prompt_used,\n                    url: imageUrl\n                });\n            } catch (error) {\n                console.error('Share failed:', error);\n            }\n        } else {\n            // 回退到复制链接\n            navigator.clipboard.writeText(imageUrl);\n            alert('图像链接已复制到剪贴板');\n        }\n    };\n    // 切换喜欢状态\n    const toggleLike = (index)=>{\n        const newLiked = new Set(likedImages);\n        if (newLiked.has(index)) {\n            newLiked.delete(index);\n        } else {\n            newLiked.add(index);\n        }\n        setLikedImages(newLiked);\n    };\n    // 格式化生成时间\n    const formatGenerationTime = (time)=>{\n        if (!time) return '未知';\n        if (time < 1000) return \"\".concat(time, \"ms\");\n        return \"\".concat((time / 1000).toFixed(1), \"s\");\n    };\n    // 获取模型显示名称\n    const getModelDisplayName = (model)=>{\n        const modelNames = {\n            'flux-schnell': 'FLUX Schnell',\n            'flux-dev': 'FLUX Dev',\n            'flux-pro': 'FLUX Pro',\n            'sd-xl': 'Stable Diffusion XL',\n            'sd-3': 'Stable Diffusion 3'\n        };\n        return modelNames[model] || model;\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg border border-gray-200 p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"正在生成图像...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: [\n                            \"使用 \",\n                            getModelDisplayName(metadata.model_used),\n                            \" 模型生成中，请稍候\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                lineNumber: 119,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n            lineNumber: 118,\n            columnNumber: 7\n        }, this);\n    }\n    if (!images || images.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-50 rounded-lg border border-gray-200 p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"暂无图像\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: \"生成的图像将在这里显示\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                lineNumber: 133,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n            lineNumber: 132,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg border border-gray-200 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4 \".concat(images.length === 1 ? 'grid-cols-1' : images.length === 2 ? 'grid-cols-2' : 'grid-cols-2 md:grid-cols-3'),\n                    children: images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative group cursor-pointer\",\n                            onClick: ()=>setSelectedImage(index),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: image.url,\n                                    alt: \"Generated image \".concat(index + 1),\n                                    className: \"w-full h-auto rounded-lg shadow-sm hover:shadow-md transition-shadow\",\n                                    style: {\n                                        aspectRatio: \"\".concat(image.width, \"/\").concat(image.height)\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    setIsFullscreen(true);\n                                                },\n                                                className: \"p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors\",\n                                                title: \"全屏查看\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"w-4 h-4 text-gray-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    downloadImage(image.url, index);\n                                                },\n                                                className: \"p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors\",\n                                                title: \"下载图像\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-4 h-4 text-gray-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    toggleLike(index);\n                                                },\n                                                className: \"p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors\",\n                                                title: \"喜欢\",\n                                                children: likedImages.has(index) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-4 h-4 text-red-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-4 h-4 text-gray-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this),\n                                selectedImage === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 border-2 border-blue-500 rounded-lg pointer-events-none\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200 p-4 bg-gray-50\",\n                children: [\n                    metadata.demo_notice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-3 p-2 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-yellow-800\",\n                            children: [\n                                \"\\uD83C\\uDFAD \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"演示模式\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 18\n                                }, this),\n                                \": 这是示例图像。配置 FAL API 密钥以生成真实图像。\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/FAL_SETUP.md\",\n                                    className: \"text-yellow-900 underline ml-1\",\n                                    children: \"查看配置指南\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: getModelDisplayName(metadata.model_used)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 15\n                                            }, this),\n                                            metadata.demo_notice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs bg-yellow-100 text-yellow-800 px-1 rounded\",\n                                                children: \"演示\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this),\n                                    metadata.generation_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: formatGenerationTime(metadata.generation_time)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this),\n                                    metadata.seed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs\",\n                                        children: [\n                                            \"Seed: \",\n                                            metadata.seed\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>shareImage(images[selectedImage].url),\n                                        className: \"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"分享\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this),\n                                    onRegenerate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>onRegenerate(metadata.seed),\n                                        className: \"px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors\",\n                                        children: \"重新生成\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-gray-700\",\n                                children: \"提示词: \"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-600\",\n                                children: metadata.prompt_used\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, this),\n            isFullscreen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4\",\n                onClick: ()=>setIsFullscreen(false),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative max-w-full max-h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: images[selectedImage].url,\n                            alt: \"Fullscreen view\",\n                            className: \"max-w-full max-h-full object-contain\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsFullscreen(false),\n                            className: \"absolute top-4 right-4 text-white text-2xl hover:text-gray-300\",\n                            children: \"✕\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                lineNumber: 280,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageDisplay, \"dVJC2BGxr7IBjg45u7cntX42oJY=\");\n_c = ImageDisplay;\nvar _c;\n$RefreshReg$(_c, \"ImageDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Chat/ImageDisplay.tsx\n"));

/***/ })

});