#!/usr/bin/env node

// 图片编辑功能测试脚本
const testImageEditing = async () => {
  console.log('🎨 图片编辑模块测试');
  console.log('='.repeat(50));
  
  const baseUrl = 'http://localhost:3000';
  
  // 测试1: 获取图片编辑配置
  console.log('\n📋 测试1: 获取图片编辑配置');
  try {
    const configResponse = await fetch(`${baseUrl}/api/edit-image`);
    
    if (configResponse.ok) {
      const config = await configResponse.json();
      console.log('✅ 配置获取成功');
      console.log('🤖 可用模型:', Object.keys(config.models).join(', '));
      console.log('📁 支持格式:', config.supported_formats.join(', '));
      console.log('📏 最大文件:', config.max_file_size);
      console.log('💡 示例指令数量:', config.example_instructions.length);
      
      console.log('\n🎯 编辑指令示例:');
      config.example_instructions.slice(0, 5).forEach((instruction, index) => {
        console.log(`  ${index + 1}. ${instruction}`);
      });
    } else {
      console.log('❌ 配置获取失败:', configResponse.status);
    }
  } catch (error) {
    console.log('❌ 配置获取出错:', error.message);
  }
  
  // 测试2: 图片编辑API (演示模式)
  console.log('\n🎨 测试2: 图片编辑API (演示模式)');
  try {
    // 创建一个测试用的FormData
    const formData = new FormData();
    
    // 创建一个虚拟的图片文件 (1x1像素的PNG)
    const canvas = document?.createElement ? document.createElement('canvas') : null;
    if (canvas) {
      canvas.width = 1;
      canvas.height = 1;
      const ctx = canvas.getContext('2d');
      ctx.fillStyle = '#FF0000';
      ctx.fillRect(0, 0, 1, 1);
      
      canvas.toBlob(async (blob) => {
        const testFile = new File([blob], 'test.png', { type: 'image/png' });
        
        formData.append('image', testFile);
        formData.append('instruction', '将图片改为蓝色');
        formData.append('model', 'instruct-pix2pix');
        
        const editResponse = await fetch(`${baseUrl}/api/edit-image`, {
          method: 'POST',
          body: formData
        });
        
        const editData = await editResponse.json();
        
        if (editResponse.ok && editData.success) {
          console.log('✅ 图片编辑成功');
          console.log('🖼️ 编辑结果URL:', editData.edited_image.url.substring(0, 50) + '...');
          console.log('🤖 使用模型:', editData.metadata.model_used);
          console.log('⏱️ 处理时间:', editData.metadata.processing_time + 'ms');
        } else {
          console.log('ℹ️ 图片编辑需要API密钥');
          console.log('📝 错误信息:', editData.error);
          console.log('🎭 演示模式:', editData.demo_mode ? '是' : '否');
        }
      }, 'image/png');
    } else {
      // Node.js环境下的模拟测试
      console.log('ℹ️ Node.js环境，跳过实际文件上传测试');
      console.log('💡 在浏览器中测试图片上传功能');
    }
  } catch (error) {
    console.log('❌ 图片编辑测试出错:', error.message);
  }
  
  // 测试3: 参数验证
  console.log('\n🔍 测试3: 参数验证');
  try {
    // 测试缺少图片
    const noImageResponse = await fetch(`${baseUrl}/api/edit-image`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        instruction: '测试指令'
      })
    });
    
    if (noImageResponse.status === 400) {
      console.log('✅ 图片参数验证正常工作');
    } else {
      console.log('❌ 图片参数验证可能有问题');
    }
    
    // 测试缺少指令
    const formData = new FormData();
    formData.append('image', new Blob(['test'], { type: 'image/png' }), 'test.png');
    
    const noInstructionResponse = await fetch(`${baseUrl}/api/edit-image`, {
      method: 'POST',
      body: formData
    });
    
    if (noInstructionResponse.status === 400) {
      console.log('✅ 指令参数验证正常工作');
    } else {
      console.log('❌ 指令参数验证可能有问题');
    }
  } catch (error) {
    console.log('❌ 参数验证测试出错:', error.message);
  }
  
  // 测试4: 完整工作流测试
  console.log('\n🔄 测试4: 完整工作流测试');
  try {
    console.log('📝 步骤1: 用户上传图片');
    console.log('  - 支持拖拽上传');
    console.log('  - 支持点击选择文件');
    console.log('  - 自动验证文件类型和大小');
    
    console.log('\n🎨 步骤2: 输入编辑指令');
    console.log('  - 自然语言描述编辑需求');
    console.log('  - 支持快捷编辑模板');
    console.log('  - 实时指令验证');
    
    console.log('\n🚀 步骤3: AI图片编辑');
    console.log('  - 自动选择最佳编辑模型');
    console.log('  - 实时显示编辑进度');
    console.log('  - 生成高质量编辑结果');
    
    console.log('\n📱 步骤4: 结果展示和操作');
    console.log('  - 原图与编辑结果对比');
    console.log('  - 支持全屏查看');
    console.log('  - 一键下载和分享');
    console.log('  - 支持重新编辑');
    
    console.log('✅ 完整工作流设计合理');
  } catch (error) {
    console.log('❌ 工作流测试出错:', error.message);
  }
  
  // 环境检查
  console.log('\n🔧 环境配置检查');
  console.log('📍 API端点:', baseUrl);
  console.log('🔑 FAL API密钥:', process.env.FAL_API_KEY ? '已配置' : '未配置');
  
  if (!process.env.FAL_API_KEY) {
    console.log('\n💡 配置建议:');
    console.log('1. 获取FAL.AI API密钥: https://fal.ai/dashboard/keys');
    console.log('2. 添加到 .env.local: FAL_API_KEY=your_key_here');
    console.log('3. 重启开发服务器');
    console.log('4. 查看详细配置: FAL_SETUP.md');
  } else {
    console.log('🚀 FAL API已配置，可使用完整图片编辑功能');
  }
  
  console.log('\n✨ 测试完成！');
  console.log('\n📖 使用说明:');
  console.log('1. 访问 http://localhost:3000/chat');
  console.log('2. 点击图片按钮上传要编辑的图片');
  console.log('3. 输入编辑指令，如"改为黑白风格"');
  console.log('4. 查看编辑结果，支持对比和下载');
  console.log('5. 可以对同一张图片进行多次编辑');
  
  console.log('\n🎯 支持的编辑类型:');
  console.log('• 风格转换: 黑白、油画、水彩等');
  console.log('• 背景修改: 更换背景、移除背景');
  console.log('• 对象编辑: 添加、删除、修改对象');
  console.log('• 色彩调整: 调整颜色、亮度、对比度');
  console.log('• 场景变换: 白天变夜晚、季节变换');
  console.log('• 细节优化: 增强细节、去除噪点');
};

// 运行测试
testImageEditing().catch(console.error);
