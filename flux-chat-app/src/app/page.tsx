'use client';

export default function Home() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          🎨 AI图像生成助手
        </h1>
        <p className="text-gray-600 mb-4">
          这是一个对话式图像生成助手，可以帮助您将模糊的图像需求转换为专业的提示词。
        </p>
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-3 rounded-lg mb-6">
          <p className="text-sm text-blue-800">
            🚀 <strong>现已支持DeepSeek</strong> - 更低成本，更好的中文优化，更强的创意能力！
          </p>
        </div>
        <div className="space-y-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold text-blue-900 mb-2">✅ 核心功能</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• 智能对话交互</li>
              <li>• 需求澄清</li>
              <li>• 提示词优化</li>
              <li>• 实时反馈</li>
            </ul>
          </div>

          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="font-semibold text-green-900 mb-2">🚀 增强功能</h3>
            <ul className="text-sm text-green-800 space-y-1">
              <li>• 历史会话管理</li>
              <li>• 提示词模板库</li>
              <li>• 个性化设置</li>
              <li>• 演示模式</li>
            </ul>
          </div>

          <button
            onClick={() => window.location.href = '/chat'}
            className="w-full bg-blue-500 text-white py-3 px-4 rounded-lg hover:bg-blue-600 transition-colors font-medium"
          >
            开始使用
          </button>
        </div>
      </div>
    </div>
  );
}
