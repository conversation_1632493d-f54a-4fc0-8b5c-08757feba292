'use client';

import { useState } from 'react';
import ImageDisplay from '@/components/Chat/ImageDisplay';
import ImageGenerationPanel from '@/components/Chat/ImageGenerationPanel';
import ImageUpload from '@/components/Chat/ImageUpload';
import ImageEditResult from '@/components/Chat/ImageEditResult';

interface ChatMessage {
  id: string;
  role: string;
  content: string;
  images?: any[];
  metadata?: any;
  editResult?: {
    originalImage: { url: string; name: string };
    editedImage: any;
    metadata: any;
  };
}

interface UploadedImage {
  id: string;
  file: File;
  url: string;
  name: string;
  size: number;
  type: string;
}

export default function ChatPage() {
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isGeneratingImage, setIsGeneratingImage] = useState(false);
  const [isEditingImage, setIsEditingImage] = useState(false);
  const [optimizedPrompt, setOptimizedPrompt] = useState<string>('');
  const [uploadedImages, setUploadedImages] = useState<UploadedImage[]>([]);
  const [showImageUpload, setShowImageUpload] = useState(false);

  const sendMessage = async () => {
    if (!message.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: message
    };

    setMessages(prev => [...prev, userMessage]);
    setMessage('');
    setIsLoading(true);

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: userMessage.content,
          conversationHistory: messages.map(m => m.content)
        })
      });

      const data = await response.json();

      if (response.ok) {
        const assistantMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: data.response
        };

        setMessages(prev => [...prev, assistantMessage]);

        // 检查是否需要自动生成提示词优化
        if (!data.needsClarification && data.extractedRequirement) {
          await generateOptimizedPrompt();
        }
      } else {
        setMessages(prev => [...prev, {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: '抱歉，发生了错误。请稍后重试。'
        }]);
      }
    } catch (error) {
      setMessages(prev => [...prev, {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: '网络错误，请检查连接。'
      }]);
    } finally {
      setIsLoading(false);
    }
  };

  // 生成优化提示词
  const generateOptimizedPrompt = async () => {
    try {
      const conversationHistory = messages.map(m => m.content);
      const requirement = conversationHistory.join(' ');

      const response = await fetch('/api/optimize-prompt', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          requirement,
          conversationHistory,
          useAI: true,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.optimization?.optimizedPrompt) {
          setOptimizedPrompt(data.optimization.optimizedPrompt);
        }
      }
    } catch (error) {
      console.error('Failed to generate optimized prompt:', error);
    }
  };

  // 处理图像生成
  const handleImageGeneration = async (config: any, prompt: string) => {
    setIsGeneratingImage(true);

    try {
      const response = await fetch('/api/generate-image', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt,
          ...config
        })
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // 添加图像消息
        const imageMessage: ChatMessage = {
          id: Date.now().toString(),
          role: 'assistant',
          content: `已为您生成图像！使用了 ${data.metadata.model_used} 模型。`,
          images: data.images,
          metadata: data.metadata
        };

        setMessages(prev => [...prev, imageMessage]);
      } else {
        // 处理错误
        const errorMessage: ChatMessage = {
          id: Date.now().toString(),
          role: 'assistant',
          content: data.demo_mode
            ? '图像生成功能需要配置 FAL API 密钥。请查看配置说明。'
            : `图像生成失败: ${data.error || '未知错误'}`
        };

        setMessages(prev => [...prev, errorMessage]);
      }
    } catch (error) {
      const errorMessage: ChatMessage = {
        id: Date.now().toString(),
        role: 'assistant',
        content: '图像生成请求失败，请检查网络连接。'
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsGeneratingImage(false);
    }
  };

  // 处理图片上传
  const handleImageUpload = (images: UploadedImage[]) => {
    setUploadedImages(images);
  };

  // 处理图片编辑
  const handleImageEdit = async (image: UploadedImage, instruction: string) => {
    setIsEditingImage(true);

    try {
      const formData = new FormData();
      formData.append('image', image.file);
      formData.append('instruction', instruction);
      formData.append('model', 'instruct-pix2pix');

      const response = await fetch('/api/edit-image', {
        method: 'POST',
        body: formData
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // 添加编辑结果消息
        const editMessage: ChatMessage = {
          id: Date.now().toString(),
          role: 'assistant',
          content: `已完成图片编辑！编辑指令: "${instruction}"`,
          editResult: {
            originalImage: {
              url: image.url,
              name: image.name
            },
            editedImage: data.edited_image,
            metadata: data.metadata
          }
        };

        setMessages(prev => [...prev, editMessage]);
      } else {
        // 处理错误
        const errorMessage: ChatMessage = {
          id: Date.now().toString(),
          role: 'assistant',
          content: data.demo_mode
            ? '图片编辑功能需要配置 FAL API 密钥。请查看配置说明。'
            : `图片编辑失败: ${data.error || '未知错误'}`
        };

        setMessages(prev => [...prev, errorMessage]);
      }
    } catch (error) {
      const errorMessage: ChatMessage = {
        id: Date.now().toString(),
        role: 'assistant',
        content: '图片编辑请求失败，请检查网络连接。'
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsEditingImage(false);
    }
  };

  // 重新编辑图片
  const handleReEdit = async (editResult: any, newInstruction: string) => {
    // 创建临时的UploadedImage对象
    const tempImage: UploadedImage = {
      id: 'temp-' + Date.now(),
      file: new File([], editResult.originalImage.name), // 临时文件对象
      url: editResult.originalImage.url,
      name: editResult.originalImage.name,
      size: 0,
      type: 'image/jpeg'
    };

    await handleImageEdit(tempImage, newInstruction);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* 头部 */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <h1 className="text-xl font-semibold text-gray-900">AI图像生成助手</h1>
        <p className="text-sm text-gray-600">让我帮您优化图像生成提示词</p>
      </div>

      {/* 消息区域 */}
      <div className="flex-1 overflow-y-auto p-4">
        {messages.length === 0 ? (
          <div className="text-center text-gray-500 mt-8">
            <p className="text-lg font-medium">欢迎使用AI图像生成助手</p>
            <p className="text-sm mt-2">请描述您想要生成的图像，我会帮您优化提示词</p>
          </div>
        ) : (
          <div className="space-y-4 max-w-4xl mx-auto">
            {messages.map((msg) => (
              <div key={msg.id} className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                <div className={`max-w-[80%] ${msg.role === 'user' ? '' : 'w-full'}`}>
                  <div className={`rounded-lg px-4 py-2 ${
                    msg.role === 'user'
                      ? 'bg-blue-500 text-white'
                      : 'bg-white text-gray-900 border border-gray-200'
                  }`}>
                    <p className="whitespace-pre-wrap">{msg.content}</p>
                  </div>

                  {/* 显示生成的图像 */}
                  {msg.images && msg.images.length > 0 && (
                    <div className="mt-3">
                      <ImageDisplay
                        images={msg.images}
                        metadata={msg.metadata}
                        onRegenerate={(seed) => {
                          if (msg.metadata?.prompt_used) {
                            handleImageGeneration(
                              {
                                model: msg.metadata.parameters?.model || 'flux-schnell',
                                size: msg.metadata.parameters?.size || 'landscape',
                                num_images: 1,
                                seed
                              },
                              msg.metadata.prompt_used
                            );
                          }
                        }}
                      />
                    </div>
                  )}

                  {/* 显示图片编辑结果 */}
                  {msg.editResult && (
                    <div className="mt-3">
                      <ImageEditResult
                        originalImage={msg.editResult.originalImage}
                        editedImage={msg.editResult.editedImage}
                        metadata={msg.editResult.metadata}
                        onReEdit={(newInstruction) => {
                          handleReEdit(msg.editResult, newInstruction);
                        }}
                      />
                    </div>
                  )}
                </div>
              </div>
            ))}
            {isLoading && (
              <div className="flex justify-start">
                <div className="bg-white border border-gray-200 rounded-lg px-4 py-2">
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                    <span className="text-gray-500">AI正在思考...</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* 输入区域 */}
      <div className="bg-white border-t border-gray-200 p-4">
        <div className="max-w-4xl mx-auto space-y-3">
          {/* 图片上传区域 */}
          {showImageUpload && (
            <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-lg font-medium text-gray-900">上传图片进行编辑</h3>
                <button
                  onClick={() => setShowImageUpload(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  ✕
                </button>
              </div>
              <ImageUpload
                onImageUpload={handleImageUpload}
                onImageEdit={handleImageEdit}
                maxFiles={3}
                maxSize={10}
              />
            </div>
          )}

          {/* 图像生成面板 */}
          {optimizedPrompt && (
            <div className="flex justify-center">
              <ImageGenerationPanel
                onGenerate={handleImageGeneration}
                isGenerating={isGeneratingImage}
                optimizedPrompt={optimizedPrompt}
              />
            </div>
          )}

          {/* 消息输入 */}
          <div className="flex space-x-3">
            <button
              onClick={() => setShowImageUpload(!showImageUpload)}
              disabled={isLoading || isGeneratingImage || isEditingImage}
              className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
              title="上传图片进行编辑"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <span className="hidden sm:inline">图片</span>
            </button>

            <input
              type="text"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
              placeholder="描述您想要生成的图像，或上传图片进行编辑..."
              disabled={isLoading || isGeneratingImage || isEditingImage}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
            />
            <button
              onClick={sendMessage}
              disabled={!message.trim() || isLoading || isGeneratingImage || isEditingImage}
              className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? '发送中...' : '发送'}
            </button>
          </div>

          {/* 快捷提示 */}
          <div className="space-y-2">
            <div className="flex flex-wrap gap-2 justify-center">
              <span className="text-xs text-gray-500 w-full text-center">图像生成示例:</span>
              {['可爱的小猫', '未来城市', '油画风景', '卡通人物', '抽象艺术'].map((prompt) => (
                <button
                  key={prompt}
                  onClick={() => setMessage(prompt)}
                  disabled={isLoading || isGeneratingImage || isEditingImage}
                  className="px-3 py-1 text-sm bg-blue-50 text-blue-600 rounded-full hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {prompt}
                </button>
              ))}
            </div>

            {uploadedImages.length > 0 && (
              <div className="flex flex-wrap gap-2 justify-center">
                <span className="text-xs text-gray-500 w-full text-center">图片编辑示例:</span>
                {['改为黑白风格', '添加蓝天背景', '移除背景', '调整为夜晚场景', '添加彩色滤镜'].map((prompt) => (
                  <button
                    key={prompt}
                    onClick={() => {
                      if (uploadedImages.length > 0) {
                        handleImageEdit(uploadedImages[0], prompt);
                      }
                    }}
                    disabled={isLoading || isGeneratingImage || isEditingImage}
                    className="px-3 py-1 text-sm bg-green-50 text-green-600 rounded-full hover:bg-green-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {prompt}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
