import { NextRequest, NextResponse } from 'next/server';
import { 
  generateImage, 
  generateMultipleImages, 
  applyStylePreset,
  ImageGenerationParams,
  STYLE_PRESETS 
} from '@/lib/imageGeneration';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      prompt, 
      model = 'flux-schnell',
      size = 'landscape',
      num_images = 1,
      style_preset,
      guidance_scale,
      num_inference_steps,
      seed,
      negative_prompt,
      batch_mode = false
    } = body;

    // 验证必需参数
    if (!prompt || typeof prompt !== 'string') {
      return NextResponse.json(
        { error: 'Prompt is required and must be a string' },
        { status: 400 }
      );
    }

    // 检查API密钥
    if (!process.env.FAL_KEY && !process.env.FAL_API_KEY) {
      return NextResponse.json(
        { 
          error: 'FAL API key not configured',
          demo_mode: true,
          message: 'Please configure FAL_API_KEY in environment variables'
        },
        { status: 503 }
      );
    }

    // 构建生成参数
    let params: ImageGenerationParams = {
      prompt,
      model,
      size,
      num_images: Math.min(num_images, 4), // 限制最多4张
      guidance_scale,
      num_inference_steps,
      seed,
      negative_prompt,
    };

    // 应用风格预设
    if (style_preset && style_preset in STYLE_PRESETS) {
      params = applyStylePreset(params, style_preset as keyof typeof STYLE_PRESETS);
    }

    console.log('Image generation request:', {
      prompt: prompt.substring(0, 100) + '...',
      model,
      size,
      num_images: params.num_images
    });

    // 生成图像
    const result = batch_mode && num_images > 1
      ? await generateMultipleImages(params, num_images)
      : await generateImage(params);

    if (result.success) {
      return NextResponse.json({
        success: true,
        images: result.images,
        metadata: {
          model_used: result.model_used,
          generation_time: result.generation_time,
          seed: result.seed,
          prompt_used: prompt,
          parameters: params
        }
      });
    } else {
      return NextResponse.json(
        { 
          error: result.error,
          success: false,
          model_used: result.model_used
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Image generation API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// GET方法用于获取支持的模型和配置
export async function GET() {
  try {
    return NextResponse.json({
      models: {
        'flux-schnell': {
          name: 'FLUX Schnell',
          description: '快速生成，4步即可完成',
          speed: 'fast',
          quality: 'good'
        },
        'flux-dev': {
          name: 'FLUX Dev',
          description: '开发版本，平衡速度和质量',
          speed: 'medium',
          quality: 'high'
        },
        'flux-pro': {
          name: 'FLUX Pro',
          description: '专业版本，最高质量',
          speed: 'slow',
          quality: 'excellent'
        },
        'sd-xl': {
          name: 'Stable Diffusion XL',
          description: '经典的高质量模型',
          speed: 'medium',
          quality: 'high'
        }
      },
      sizes: {
        'square': '1024x1024',
        'portrait': '768x1024',
        'landscape': '1024x768',
        'wide': '1344x768',
        'tall': '768x1344'
      },
      style_presets: Object.keys(STYLE_PRESETS),
      limits: {
        max_images_per_request: 4,
        max_prompt_length: 1000
      }
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to get configuration' },
      { status: 500 }
    );
  }
}
