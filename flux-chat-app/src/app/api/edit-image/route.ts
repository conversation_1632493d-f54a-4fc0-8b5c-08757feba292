import { NextRequest, NextResponse } from 'next/server';
import * as fal from '@fal-ai/serverless-client';

// 配置fal.ai客户端
fal.config({
  credentials: process.env.FAL_KEY || process.env.FAL_API_KEY,
});

// 支持的图片编辑模型
const IMAGE_EDIT_MODELS = {
  'instruct-pix2pix': 'fal-ai/instruct-pix2pix',
  'controlnet-inpaint': 'fal-ai/stable-diffusion-xl-controlnet-inpaint',
  'flux-fill': 'fal-ai/flux/fill',
  'flux-redux': 'fal-ai/flux/redux',
} as const;

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const image = formData.get('image') as File;
    const instruction = formData.get('instruction') as string;
    const model = formData.get('model') as string || 'instruct-pix2pix';
    const strength = parseFloat(formData.get('strength') as string || '0.8');

    // 验证必需参数
    if (!image) {
      return NextResponse.json(
        { error: 'Image file is required' },
        { status: 400 }
      );
    }

    if (!instruction || typeof instruction !== 'string') {
      return NextResponse.json(
        { error: 'Instruction is required and must be a string' },
        { status: 400 }
      );
    }

    // 检查API密钥
    const hasValidFalKey = (process.env.FAL_KEY && process.env.FAL_KEY !== 'your_fal_api_key_here') ||
                          (process.env.FAL_API_KEY && process.env.FAL_API_KEY !== 'your_fal_api_key_here');
    
    if (!hasValidFalKey) {
      // 演示模式 - 返回模拟的编辑结果
      return NextResponse.json({
        success: true,
        demo_mode: true,
        edited_image: {
          url: 'https://picsum.photos/1024/768?random=' + Date.now(),
          width: 1024,
          height: 768,
          content_type: 'image/jpeg'
        },
        metadata: {
          model_used: model,
          instruction_used: instruction,
          processing_time: 5000 + Math.random() * 3000,
          original_filename: image.name,
          demo_notice: 'This is a demo result. Configure FAL_API_KEY for real image editing.'
        }
      });
    }

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(image.type)) {
      return NextResponse.json(
        { error: 'Unsupported image format. Please use JPEG, PNG, or WebP.' },
        { status: 400 }
      );
    }

    // 验证文件大小 (最大10MB)
    const maxSize = 10 * 1024 * 1024;
    if (image.size > maxSize) {
      return NextResponse.json(
        { error: 'Image file too large. Maximum size is 10MB.' },
        { status: 400 }
      );
    }

    console.log('Image editing request:', {
      filename: image.name,
      size: image.size,
      type: image.type,
      instruction: instruction.substring(0, 100) + '...',
      model
    });

    // 将图片转换为base64或上传到临时存储
    const imageBuffer = await image.arrayBuffer();
    const imageBase64 = Buffer.from(imageBuffer).toString('base64');
    const imageDataUrl = `data:${image.type};base64,${imageBase64}`;

    const startTime = Date.now();
    
    // 根据模型选择不同的编辑方式
    let result;
    const modelEndpoint = IMAGE_EDIT_MODELS[model as keyof typeof IMAGE_EDIT_MODELS] || IMAGE_EDIT_MODELS['instruct-pix2pix'];

    if (model === 'instruct-pix2pix') {
      // 使用InstructPix2Pix进行指令式编辑
      result = await fal.subscribe(modelEndpoint, {
        input: {
          image_url: imageDataUrl,
          prompt: instruction,
          num_inference_steps: 20,
          guidance_scale: 7.5,
          image_guidance_scale: 1.5,
          strength: strength,
        },
        logs: true,
        onQueueUpdate: (update) => {
          console.log('Edit queue update:', update);
        },
      });
    } else if (model === 'flux-fill') {
      // 使用FLUX Fill进行填充编辑
      result = await fal.subscribe(modelEndpoint, {
        input: {
          image_url: imageDataUrl,
          prompt: instruction,
          strength: strength,
          num_inference_steps: 28,
          guidance_scale: 3.5,
        },
        logs: true,
      });
    } else {
      // 默认使用InstructPix2Pix
      result = await fal.subscribe(IMAGE_EDIT_MODELS['instruct-pix2pix'], {
        input: {
          image_url: imageDataUrl,
          prompt: instruction,
          num_inference_steps: 20,
          guidance_scale: 7.5,
          image_guidance_scale: 1.5,
          strength: strength,
        },
        logs: true,
      });
    }

    const processingTime = Date.now() - startTime;

    if (result.data && result.data.images && result.data.images.length > 0) {
      const editedImage = result.data.images[0];
      
      return NextResponse.json({
        success: true,
        edited_image: {
          url: editedImage.url,
          width: editedImage.width || 1024,
          height: editedImage.height || 768,
          content_type: editedImage.content_type || 'image/jpeg',
        },
        metadata: {
          model_used: model,
          instruction_used: instruction,
          processing_time: processingTime,
          original_filename: image.name,
          strength_used: strength,
          seed: result.data.seed,
        }
      });
    } else {
      throw new Error('No edited image generated');
    }

  } catch (error) {
    console.error('Image editing error:', error);
    return NextResponse.json(
      { 
        error: 'Image editing failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// GET方法用于获取支持的编辑模型和配置
export async function GET() {
  try {
    return NextResponse.json({
      models: {
        'instruct-pix2pix': {
          name: 'InstructPix2Pix',
          description: '基于指令的图片编辑，适合大部分编辑任务',
          speed: 'medium',
          quality: 'high',
          best_for: ['风格转换', '对象修改', '场景变换']
        },
        'flux-fill': {
          name: 'FLUX Fill',
          description: 'FLUX模型的填充编辑功能',
          speed: 'fast',
          quality: 'excellent',
          best_for: ['局部编辑', '对象替换', '背景修改']
        },
        'flux-redux': {
          name: 'FLUX Redux',
          description: 'FLUX模型的重构编辑功能',
          speed: 'medium',
          quality: 'excellent',
          best_for: ['整体重构', '风格迁移', '质量提升']
        }
      },
      supported_formats: ['image/jpeg', 'image/png', 'image/webp'],
      max_file_size: '10MB',
      parameters: {
        strength: {
          min: 0.1,
          max: 1.0,
          default: 0.8,
          description: '编辑强度，值越高变化越大'
        }
      },
      example_instructions: [
        '将背景改为蓝天白云',
        '给人物添加一顶红色帽子',
        '将图片转换为黑白风格',
        '移除背景中的汽车',
        '将白天场景改为夜晚',
        '给建筑物添加彩色灯光',
        '将夏天场景改为冬天雪景',
        '调整人物的服装颜色为蓝色'
      ]
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to get editing configuration' },
      { status: 500 }
    );
  }
}
