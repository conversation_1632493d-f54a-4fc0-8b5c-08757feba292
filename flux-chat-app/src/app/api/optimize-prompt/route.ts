import { NextRequest, NextResponse } from 'next/server';
import { optimizePrompt } from '@/lib/openai';
import { generateOptimizedPrompt, extractImageRequirement } from '@/lib/promptOptimizer';

export async function POST(request: NextRequest) {
  try {
    const { requirement, conversationHistory, useAI = true } = await request.json();

    if (!requirement) {
      return NextResponse.json(
        { error: 'Requirement is required' },
        { status: 400 }
      );
    }

    let optimizationResult;

    if (useAI) {
      // 使用AI优化提示词
      const conversationContext = conversationHistory.join('\n');
      optimizationResult = await optimizePrompt(requirement, conversationContext);
    } else {
      // 使用本地规则优化提示词
      const extractedRequirement = extractImageRequirement(conversationHistory);
      optimizationResult = generateOptimizedPrompt(extractedRequirement, requirement);
    }

    return NextResponse.json({
      optimization: optimizationResult,
      success: true
    });

  } catch (error) {
    console.error('Prompt optimization error:', error);
    
    // 如果AI优化失败，回退到本地优化
    try {
      const { requirement, conversationHistory } = await request.json();
      const extractedRequirement = extractImageRequirement(conversationHistory);
      const fallbackResult = generateOptimizedPrompt(extractedRequirement, requirement);
      
      return NextResponse.json({
        optimization: fallbackResult,
        success: true,
        fallback: true
      });
    } catch (fallbackError) {
      return NextResponse.json(
        { error: 'Failed to optimize prompt' },
        { status: 500 }
      );
    }
  }
}
