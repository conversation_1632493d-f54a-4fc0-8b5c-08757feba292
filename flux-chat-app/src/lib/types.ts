// 消息类型定义
export interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: {
    type?: 'clarification' | 'prompt_generation' | 'optimization';
    extractedElements?: string[];
    optimizedPrompt?: string;
  };
}

// 对话会话类型
export interface ChatSession {
  id: string;
  messages: Message[];
  status: 'active' | 'completed';
  createdAt: Date;
  updatedAt: Date;
}

// 图像需求分析结果
export interface ImageRequirement {
  subject: string;
  style: string;
  mood: string;
  colors: string[];
  composition: string;
  lighting: string;
  quality: string;
  additionalElements: string[];
}

// 提示词优化结果
export interface PromptOptimization {
  originalPrompt: string;
  optimizedPrompt: string;
  improvements: string[];
  confidence: number;
  suggestedParameters?: {
    aspectRatio?: string;
    style?: string;
    quality?: string;
  };
}

// 对话状态
export interface ChatState {
  session: ChatSession;
  isLoading: boolean;
  currentRequirement?: Partial<ImageRequirement>;
  optimizedPrompt?: PromptOptimization;
}

// 对话动作
export type ChatAction =
  | { type: 'ADD_MESSAGE'; payload: Message }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'UPDATE_REQUIREMENT'; payload: Partial<ImageRequirement> }
  | { type: 'SET_OPTIMIZED_PROMPT'; payload: PromptOptimization }
  | { type: 'RESET_SESSION' };
