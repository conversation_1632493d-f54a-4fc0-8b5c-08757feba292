import { ImageRequirement, PromptOptimization } from './types';

// 艺术风格映射
const STYLE_MAPPINGS = {
  '写实': 'photorealistic, hyperrealistic, detailed',
  '卡通': 'cartoon style, animated, stylized',
  '油画': 'oil painting, classical art, painterly',
  '水彩': 'watercolor, soft brushstrokes, flowing',
  '素描': 'pencil sketch, charcoal drawing, monochrome',
  '数字艺术': 'digital art, concept art, modern',
  '像素艺术': 'pixel art, 8-bit, retro gaming style',
  '抽象': 'abstract art, non-representational, artistic',
  '极简': 'minimalist, clean, simple composition',
  '赛博朋克': 'cyberpunk, neon lights, futuristic, dystopian'
};

// 情绪氛围映射
const MOOD_MAPPINGS = {
  '温暖': 'warm, cozy, inviting atmosphere',
  '冷酷': 'cold, stark, dramatic lighting',
  '神秘': 'mysterious, enigmatic, atmospheric',
  '浪漫': 'romantic, dreamy, soft lighting',
  '史诗': 'epic, grand, cinematic',
  '宁静': 'peaceful, serene, calm',
  '活力': 'energetic, vibrant, dynamic',
  '忧郁': 'melancholic, moody, contemplative',
  '恐怖': 'horror, dark, ominous',
  '欢乐': 'joyful, cheerful, bright'
};

// 质量增强词汇
const QUALITY_ENHANCERS = [
  'highly detailed',
  'sharp focus',
  'professional photography',
  '8k resolution',
  'award winning',
  'masterpiece',
  'trending on artstation',
  'perfect composition'
];

// 光照效果映射
const LIGHTING_MAPPINGS = {
  '自然光': 'natural lighting, soft daylight',
  '金色时光': 'golden hour, warm sunset lighting',
  '蓝色时光': 'blue hour, twilight, cool tones',
  '戏剧性': 'dramatic lighting, high contrast',
  '柔和': 'soft lighting, diffused light',
  '背光': 'backlighting, rim light, silhouette',
  '霓虹': 'neon lighting, colorful glow',
  '月光': 'moonlight, nocturnal, ethereal',
  '工作室': 'studio lighting, professional setup',
  '环境光': 'ambient lighting, atmospheric'
};

// 构图方式映射
const COMPOSITION_MAPPINGS = {
  '特写': 'close-up, detailed portrait',
  '全身': 'full body shot, complete figure',
  '中景': 'medium shot, waist up',
  '远景': 'wide shot, establishing shot',
  '鸟瞰': 'aerial view, top-down perspective',
  '低角度': 'low angle, dramatic perspective',
  '对称': 'symmetrical composition, balanced',
  '三分法': 'rule of thirds, dynamic composition',
  '中心构图': 'centered composition, focal point',
  '对角线': 'diagonal composition, dynamic lines'
};

// 从对话中提取图像需求
export function extractImageRequirement(conversationHistory: string[]): Partial<ImageRequirement> {
  const fullText = conversationHistory.join(' ').toLowerCase();
  
  const requirement: Partial<ImageRequirement> = {};
  
  // 提取风格
  for (const [key, value] of Object.entries(STYLE_MAPPINGS)) {
    if (fullText.includes(key.toLowerCase()) || fullText.includes(value.toLowerCase())) {
      requirement.style = key;
      break;
    }
  }
  
  // 提取情绪
  for (const [key, value] of Object.entries(MOOD_MAPPINGS)) {
    if (fullText.includes(key.toLowerCase())) {
      requirement.mood = key;
      break;
    }
  }
  
  // 提取颜色
  const colors = ['红', '蓝', '绿', '黄', '紫', '橙', '粉', '黑', '白', '灰'];
  const foundColors = colors.filter(color => fullText.includes(color));
  if (foundColors.length > 0) {
    requirement.colors = foundColors;
  }
  
  return requirement;
}

// 生成优化的提示词
export function generateOptimizedPrompt(
  requirement: Partial<ImageRequirement>,
  originalPrompt: string
): PromptOptimization {
  const promptParts: string[] = [];
  const improvements: string[] = [];
  
  // 添加主体描述
  if (requirement.subject) {
    promptParts.push(requirement.subject);
  }
  
  // 添加风格描述
  if (requirement.style && STYLE_MAPPINGS[requirement.style as keyof typeof STYLE_MAPPINGS]) {
    const styleDesc = STYLE_MAPPINGS[requirement.style as keyof typeof STYLE_MAPPINGS];
    promptParts.push(styleDesc);
    improvements.push(`添加了${requirement.style}风格的专业描述`);
  }
  
  // 添加构图描述
  if (requirement.composition && COMPOSITION_MAPPINGS[requirement.composition as keyof typeof COMPOSITION_MAPPINGS]) {
    const compDesc = COMPOSITION_MAPPINGS[requirement.composition as keyof typeof COMPOSITION_MAPPINGS];
    promptParts.push(compDesc);
    improvements.push(`优化了构图描述`);
  }
  
  // 添加光照效果
  if (requirement.lighting && LIGHTING_MAPPINGS[requirement.lighting as keyof typeof LIGHTING_MAPPINGS]) {
    const lightDesc = LIGHTING_MAPPINGS[requirement.lighting as keyof typeof LIGHTING_MAPPINGS];
    promptParts.push(lightDesc);
    improvements.push(`增强了光照效果描述`);
  }
  
  // 添加情绪氛围
  if (requirement.mood && MOOD_MAPPINGS[requirement.mood as keyof typeof MOOD_MAPPINGS]) {
    const moodDesc = MOOD_MAPPINGS[requirement.mood as keyof typeof MOOD_MAPPINGS];
    promptParts.push(moodDesc);
    improvements.push(`添加了${requirement.mood}氛围描述`);
  }
  
  // 添加颜色方案
  if (requirement.colors && requirement.colors.length > 0) {
    const colorDesc = `${requirement.colors.join(', ')} color scheme`;
    promptParts.push(colorDesc);
    improvements.push(`指定了颜色方案`);
  }
  
  // 添加质量增强词汇
  const qualityEnhancers = QUALITY_ENHANCERS.slice(0, 3).join(', ');
  promptParts.push(qualityEnhancers);
  improvements.push('添加了质量增强词汇');
  
  const optimizedPrompt = promptParts.join(', ');
  
  // 计算信心度
  const confidence = Math.min(0.9, 0.5 + (improvements.length * 0.1));
  
  return {
    originalPrompt,
    optimizedPrompt,
    improvements,
    confidence,
    suggestedParameters: {
      aspectRatio: '16:9',
      style: requirement.style || 'auto',
      quality: 'high'
    }
  };
}

// 分析用户输入中的关键词
export function analyzeKeywords(input: string): {
  subjects: string[];
  styles: string[];
  moods: string[];
  colors: string[];
} {
  const text = input.toLowerCase();
  
  const subjects: string[] = [];
  const styles: string[] = [];
  const moods: string[] = [];
  const colors: string[] = [];
  
  // 检测风格关键词
  Object.keys(STYLE_MAPPINGS).forEach(style => {
    if (text.includes(style.toLowerCase())) {
      styles.push(style);
    }
  });
  
  // 检测情绪关键词
  Object.keys(MOOD_MAPPINGS).forEach(mood => {
    if (text.includes(mood.toLowerCase())) {
      moods.push(mood);
    }
  });
  
  // 检测颜色关键词
  const colorKeywords = ['红', '蓝', '绿', '黄', '紫', '橙', '粉', '黑', '白', '灰'];
  colorKeywords.forEach(color => {
    if (text.includes(color)) {
      colors.push(color);
    }
  });
  
  return { subjects, styles, moods, colors };
}
