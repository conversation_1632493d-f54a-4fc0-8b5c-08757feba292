import OpenAI from 'openai';

// 初始化AI客户端 - 支持OpenAI和DeepSeek
export const aiClient = new OpenAI({
  apiKey: process.env.DEEPSEEK_API_KEY || process.env.OPENAI_API_KEY,
  baseURL: process.env.DEEPSEEK_API_KEY ? 'https://api.deepseek.com' : undefined,
});

// 获取当前使用的模型
export const getModel = () => {
  if (process.env.DEEPSEEK_API_KEY && process.env.DEEPSEEK_API_KEY !== 'your_deepseek_api_key_here') {
    return 'deepseek-chat';
  }
  return 'gpt-4';
};

// 获取当前AI提供商
export const getProvider = () => {
  if (process.env.DEEPSEEK_API_KEY && process.env.DEEPSEEK_API_KEY !== 'your_deepseek_api_key_here') {
    return 'deepseek';
  }
  return 'openai';
};

// 系统提示词 - 用于需求理解和澄清
export const REQUIREMENT_ANALYSIS_PROMPT = `你是一个专业的AI图像生成助手，专门帮助用户将模糊的图像需求转换为清晰、具体的描述。

你的核心任务：
1. 深度理解用户的图像生成需求和意图
2. 智能识别描述中缺失的关键视觉信息
3. 通过自然对话逐步获取完整的创作细节
4. 提取并整理关键的视觉元素

重点关注的视觉要素：
- 主题内容：主体对象、场景、人物等
- 艺术风格：写实、卡通、油画、水彩、数字艺术等
- 情绪氛围：温暖、冷酷、神秘、浪漫、史诗等
- 色彩搭配：主色调、配色方案、色彩情感
- 构图布局：视角、景别、对称性、焦点
- 光影效果：光源、明暗、氛围光、特殊效果
- 质量要求：分辨率、细节程度、专业水准
- 特殊元素：背景、道具、特效、风格化处理

对话原则：
- 使用温和、专业且富有启发性的语气
- 每次只询问1-2个最关键的问题
- 根据用户回答智能调整后续问题
- 避免机械化的问答，保持对话的自然流畅
- 适时提供创意建议和专业指导`;

// 系统提示词 - 用于提示词优化
export const PROMPT_OPTIMIZATION_PROMPT = `你是一位顶级的AI图像生成提示词优化专家，拥有丰富的视觉艺术和AI绘画经验。你的任务是将用户的创意需求转换为高效、精准的图像生成提示词。

核心优化策略：
1. 精确性：使用具体、明确的描述性词汇，避免模糊表达
2. 层次性：按视觉重要性和影响力排序关键词
3. 专业性：融入艺术风格、技术参数和行业术语
4. 完整性：确保涵盖主体、风格、构图、光影、色彩等要素
5. 兼容性：优化后的提示词应适用于主流AI绘画模型

标准提示词架构：
[核心主体] + [详细描述] + [艺术风格] + [构图视角] + [光影效果] + [色彩方案] + [质量增强] + [技术参数]

输出要求：
请严格按照JSON格式返回优化结果：
{
  "originalPrompt": "用户原始描述",
  "optimizedPrompt": "专业优化后的完整提示词",
  "improvements": ["具体改进点1", "具体改进点2", "..."],
  "confidence": 0.85,
  "suggestedParameters": {
    "aspectRatio": "推荐宽高比",
    "style": "推荐风格设置",
    "quality": "质量等级"
  }
}

优化重点：
- 主体描述要具体生动，包含关键特征
- 风格描述要专业准确，符合艺术分类
- 构图要素要明确，包含视角和布局
- 光影描述要富有表现力和技术性
- 色彩搭配要和谐且有视觉冲击力
- 质量词汇要权威，提升生成效果`;

// 分析用户需求并生成澄清问题
export async function analyzeRequirement(userInput: string, conversationHistory: string[]) {
  // 检查是否有有效的API密钥
  const hasValidKey = (process.env.DEEPSEEK_API_KEY && process.env.DEEPSEEK_API_KEY !== 'your_deepseek_api_key_here') ||
                     (process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'your_openai_api_key_here');

  if (!hasValidKey) {
    return generateDemoResponse(userInput, conversationHistory);
  }

  try {
    const model = getModel();
    const provider = getProvider();

    console.log(`Using ${provider} with model: ${model}`);

    const response = await aiClient.chat.completions.create({
      model: model,
      messages: [
        { role: 'system', content: REQUIREMENT_ANALYSIS_PROMPT },
        ...conversationHistory.map((msg, index) => ({
          role: index % 2 === 0 ? 'user' as const : 'assistant' as const,
          content: msg
        })),
        { role: 'user', content: userInput }
      ],
      temperature: 0.7,
      max_tokens: 800,
      // DeepSeek特定参数
      ...(provider === 'deepseek' && {
        top_p: 0.9,
        frequency_penalty: 0.1,
        presence_penalty: 0.1
      })
    });

    return response.choices[0]?.message?.content || '';
  } catch (error) {
    console.error(`Error analyzing requirement with ${getProvider()}:`, error);
    // 回退到演示模式
    return generateDemoResponse(userInput, conversationHistory);
  }
}

// 演示模式响应生成器
function generateDemoResponse(userInput: string, conversationHistory: string[]): string {
  const input = userInput.toLowerCase();

  // 简单的关键词检测和响应
  if (conversationHistory.length === 0) {
    // 首次交互
    if (input.includes('猫') || input.includes('cat')) {
      return '听起来很有趣！您想要什么风格的小猫图像呢？比如：\n\n• 写实风格的摄影作品\n• 可爱的卡通风格\n• 油画艺术风格\n• 水彩画风格\n\n另外，您希望小猫在什么环境中呢？';
    } else if (input.includes('风景') || input.includes('landscape')) {
      return '风景画很棒！请告诉我更多细节：\n\n• 您想要什么类型的风景？（山脉、海滩、森林、城市等）\n• 希望是什么时间？（日出、日落、夜晚等）\n• 偏好什么艺术风格？\n• 想要什么样的色彩氛围？';
    } else if (input.includes('人物') || input.includes('人')) {
      return '人物画像需要考虑很多细节！请告诉我：\n\n• 是肖像特写还是全身像？\n• 想要什么风格？（写实、卡通、艺术风格等）\n• 人物的年龄和特征？\n• 背景环境如何？\n• 服装风格有什么要求？';
    } else {
      return '很好的创意！为了帮您生成最佳的提示词，我需要了解更多细节：\n\n• 主要的视觉元素是什么？\n• 您偏好什么艺术风格？\n• 希望营造什么样的氛围？\n• 有特定的色彩偏好吗？\n\n请详细描述您的想法！';
    }
  } else {
    // 后续交互
    if (input.includes('写实') || input.includes('摄影')) {
      return '写实风格很棒！我建议：\n\n• 使用专业摄影术语\n• 强调细节和质感\n• 考虑光照效果\n\n还有其他细节需要补充吗？比如构图方式或特定的光照效果？';
    } else if (input.includes('卡通') || input.includes('可爱')) {
      return '卡通风格会很有趣！建议：\n\n• 强调可爱和友好的特征\n• 使用明亮的色彩\n• 简化的造型设计\n\n您希望是什么样的卡通风格？日式动漫、迪士尼风格，还是其他？';
    } else if (input.includes('颜色') || input.includes('色彩')) {
      return '色彩选择很重要！基于您的描述，我建议：\n\n• 温暖的色调营造舒适感\n• 对比色增加视觉冲击\n• 柔和的色彩营造宁静氛围\n\n您有特定的颜色偏好吗？';
    } else {
      return '很好！基于我们的对话，我已经收集到足够的信息来优化您的提示词了。让我为您生成一个专业的图像生成提示词！';
    }
  }
}

// 优化提示词
export async function optimizePrompt(requirement: string, conversationContext: string) {
  // 检查是否有有效的API密钥
  const hasValidKey = (process.env.DEEPSEEK_API_KEY && process.env.DEEPSEEK_API_KEY !== 'your_deepseek_api_key_here') ||
                     (process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'your_openai_api_key_here');

  if (!hasValidKey) {
    return generateDemoOptimization(requirement, conversationContext);
  }

  try {
    const model = getModel();
    const provider = getProvider();

    console.log(`Optimizing prompt using ${provider} with model: ${model}`);

    const response = await aiClient.chat.completions.create({
      model: model,
      messages: [
        { role: 'system', content: PROMPT_OPTIMIZATION_PROMPT },
        {
          role: 'user',
          content: `请基于以下对话内容和用户需求，生成专业优化的图像生成提示词：

## 对话上下文：
${conversationContext}

## 用户最终需求：
${requirement}

## 输出要求：
请严格按照JSON格式返回优化结果，确保JSON格式正确且完整。`
        }
      ],
      temperature: 0.2,
      max_tokens: 1200,
      // DeepSeek特定参数
      ...(provider === 'deepseek' && {
        top_p: 0.8,
        frequency_penalty: 0.0,
        presence_penalty: 0.0
      })
    });

    const content = response.choices[0]?.message?.content || '';

    try {
      // 尝试提取JSON内容
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      const jsonContent = jsonMatch ? jsonMatch[0] : content;
      const parsed = JSON.parse(jsonContent);

      // 验证必要字段
      if (!parsed.optimizedPrompt) {
        throw new Error('Missing optimizedPrompt field');
      }

      return {
        originalPrompt: requirement,
        optimizedPrompt: parsed.optimizedPrompt,
        improvements: parsed.improvements || ['AI优化提示词结构和专业术语'],
        confidence: parsed.confidence || 0.85,
        suggestedParameters: parsed.suggestedParameters || {
          aspectRatio: '16:9',
          style: 'auto',
          quality: 'high'
        }
      };
    } catch (parseError) {
      console.warn('JSON parsing failed, using fallback:', parseError);
      // 如果JSON解析失败，返回基本结构
      return {
        originalPrompt: requirement,
        optimizedPrompt: content.replace(/```json|```/g, '').trim(),
        improvements: [`使用${provider}优化提示词`, '增强专业术语和结构'],
        confidence: 0.8,
        suggestedParameters: {
          aspectRatio: '16:9',
          style: 'auto',
          quality: 'high'
        }
      };
    }
  } catch (error) {
    console.error(`Error optimizing prompt with ${getProvider()}:`, error);
    // 回退到演示模式
    return generateDemoOptimization(requirement, conversationContext);
  }
}

// 演示模式的提示词优化 - 模拟DeepSeek风格
function generateDemoOptimization(requirement: string, conversationContext: string) {
  const context = conversationContext.toLowerCase();
  const req = requirement.toLowerCase();

  let optimizedPrompt = requirement;
  const improvements = [];

  // 基于内容添加专业优化 - DeepSeek风格
  if (req.includes('猫') || req.includes('cat')) {
    optimizedPrompt = `adorable cat, expressive eyes, fluffy fur texture, sitting pose, natural lighting, warm color palette, cozy indoor environment, professional pet photography, high resolution, detailed whiskers, soft focus background, 8k quality, award-winning composition`;
    improvements.push('DeepSeek优化：增强了宠物摄影专业术语', '细化了毛发质感和眼神描述', '优化了环境氛围和光照设置', '添加了构图和质量增强词汇');
  } else if (req.includes('风景') || req.includes('landscape')) {
    optimizedPrompt = `breathtaking landscape vista, dramatic sky formation, golden hour illumination, vibrant natural colors, panoramic composition, depth of field, atmospheric perspective, professional landscape photography, ultra-high definition, cinematic quality, nature's masterpiece`;
    improvements.push('DeepSeek优化：采用了电影级风景摄影术语', '强化了天空和光线的戏剧性', '增加了景深和透视效果', '提升了整体视觉冲击力');
  } else if (req.includes('人物') || req.includes('portrait')) {
    optimizedPrompt = `professional portrait photography, detailed facial features, expressive eyes, natural skin texture, studio lighting setup, shallow depth of field, artistic composition, high-end retouching, commercial quality, fashion photography style`;
    improvements.push('DeepSeek优化：专业人像摄影技术术语', '强调了面部细节和皮肤质感', '优化了灯光和景深设置', '提升了商业摄影品质');
  } else if (req.includes('未来') || req.includes('科幻')) {
    optimizedPrompt = `futuristic concept art, cyberpunk aesthetic, neon lighting effects, high-tech architecture, digital art style, sci-fi atmosphere, advanced technology, holographic elements, cinematic composition, ultra-modern design, 4k digital artwork`;
    improvements.push('DeepSeek优化：科幻概念艺术专业术语', '增强了赛博朋克视觉元素', '优化了未来科技感表达', '提升了数字艺术品质');
  } else {
    // 通用DeepSeek风格优化
    optimizedPrompt = `${requirement}, masterful composition, professional quality, intricate details, optimal lighting, harmonious color scheme, artistic excellence, high-definition clarity, visual impact, creative interpretation`;
    improvements.push('DeepSeek优化：提升了整体艺术表达', '增强了专业术语和技术参数', '优化了视觉效果描述', '改善了创意解释能力');
  }

  // 基于对话上下文的深度优化
  if (context.includes('写实') || context.includes('摄影')) {
    optimizedPrompt += ', photorealistic rendering, hyperrealistic details, camera-like precision, authentic textures';
    improvements.push('DeepSeek深度优化：增强写实主义表现力');
  } else if (context.includes('卡通') || context.includes('动漫')) {
    optimizedPrompt += ', stylized animation, vibrant cartoon aesthetics, character design excellence, animated art style';
    improvements.push('DeepSeek深度优化：强化动画艺术风格');
  } else if (context.includes('油画')) {
    optimizedPrompt += ', traditional oil painting technique, classical art mastery, painterly brushstrokes, fine art quality';
    improvements.push('DeepSeek深度优化：提升古典绘画技法表达');
  } else if (context.includes('水彩')) {
    optimizedPrompt += ', watercolor transparency, fluid brushwork, artistic spontaneity, delicate color blending';
    improvements.push('DeepSeek深度优化：增强水彩画艺术特色');
  }

  return {
    originalPrompt: requirement,
    optimizedPrompt,
    improvements,
    confidence: 0.92, // DeepSeek通常有更高的信心度
    suggestedParameters: {
      aspectRatio: context.includes('人物') ? '3:4' : '16:9',
      style: context.includes('写实') ? 'photorealistic' : 'artistic',
      quality: 'ultra-high',
      model: 'deepseek-optimized'
    }
  };
}
