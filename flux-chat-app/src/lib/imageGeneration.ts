import * as fal from '@fal-ai/serverless-client';

// 配置fal.ai客户端
fal.config({
  credentials: process.env.FAL_KEY || process.env.FAL_API_KEY,
});

// 支持的图像生成模型
export const IMAGE_MODELS = {
  // FLUX模型 - 高质量图像生成
  'flux-pro': 'fal-ai/flux-pro',
  'flux-dev': 'fal-ai/flux/dev',
  'flux-schnell': 'fal-ai/flux/schnell',
  
  // Stable Diffusion模型
  'sd-xl': 'fal-ai/stable-diffusion-xl',
  'sd-3': 'fal-ai/stable-diffusion-v3-medium',
  
  // 其他专业模型
  'playground': 'fal-ai/playground-v2-5',
  'kandinsky': 'fal-ai/kandinsky-3',
} as const;

// 图像尺寸预设
export const IMAGE_SIZES = {
  'square': { width: 1024, height: 1024 },
  'portrait': { width: 768, height: 1024 },
  'landscape': { width: 1024, height: 768 },
  'wide': { width: 1344, height: 768 },
  'tall': { width: 768, height: 1344 },
  'ultra-wide': { width: 1536, height: 640 },
} as const;

// 图像生成参数接口
export interface ImageGenerationParams {
  prompt: string;
  model?: keyof typeof IMAGE_MODELS;
  size?: keyof typeof IMAGE_SIZES;
  num_images?: number;
  guidance_scale?: number;
  num_inference_steps?: number;
  seed?: number;
  safety_tolerance?: number;
  negative_prompt?: string;
}

// 图像生成结果接口
export interface ImageGenerationResult {
  success: boolean;
  images?: Array<{
    url: string;
    width: number;
    height: number;
    content_type: string;
  }>;
  error?: string;
  model_used: string;
  generation_time?: number;
  seed?: number;
}

// 主要图像生成函数
export async function generateImage(params: ImageGenerationParams): Promise<ImageGenerationResult> {
  try {
    const model = params.model || 'flux-schnell';
    const modelEndpoint = IMAGE_MODELS[model];
    const size = IMAGE_SIZES[params.size || 'landscape'];
    
    console.log(`Generating image with model: ${model} (${modelEndpoint})`);
    
    // 构建请求参数
    const requestParams = {
      prompt: params.prompt,
      image_size: params.size || 'landscape',
      num_images: params.num_images || 1,
      guidance_scale: params.guidance_scale || 7.5,
      num_inference_steps: params.num_inference_steps || getDefaultSteps(model),
      enable_safety_checker: true,
      safety_tolerance: params.safety_tolerance || 2,
      ...(params.negative_prompt && { negative_prompt: params.negative_prompt }),
      ...(params.seed && { seed: params.seed }),
      ...size,
    };

    const startTime = Date.now();
    
    // 调用fal.ai API
    const result = await fal.subscribe(modelEndpoint, {
      input: requestParams,
      logs: true,
      onQueueUpdate: (update) => {
        console.log('Queue update:', update);
      },
    });

    const generationTime = Date.now() - startTime;
    
    if (result.data && result.data.images && result.data.images.length > 0) {
      return {
        success: true,
        images: result.data.images.map((img: any) => ({
          url: img.url,
          width: img.width || size.width,
          height: img.height || size.height,
          content_type: img.content_type || 'image/jpeg',
        })),
        model_used: model,
        generation_time: generationTime,
        seed: result.data.seed,
      };
    } else {
      throw new Error('No images generated');
    }
    
  } catch (error) {
    console.error('Image generation error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      model_used: params.model || 'flux-schnell',
    };
  }
}

// 获取模型默认步数
function getDefaultSteps(model: keyof typeof IMAGE_MODELS): number {
  const stepMap = {
    'flux-pro': 50,
    'flux-dev': 50,
    'flux-schnell': 4,  // FLUX Schnell专为快速生成优化
    'sd-xl': 30,
    'sd-3': 28,
    'playground': 50,
    'kandinsky': 100,
  };
  return stepMap[model] || 30;
}

// 批量图像生成
export async function generateMultipleImages(
  params: ImageGenerationParams,
  count: number = 4
): Promise<ImageGenerationResult> {
  try {
    const batchParams = {
      ...params,
      num_images: Math.min(count, 4), // 限制单次最多4张
    };
    
    return await generateImage(batchParams);
  } catch (error) {
    console.error('Batch image generation error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Batch generation failed',
      model_used: params.model || 'flux-schnell',
    };
  }
}

// 图像风格预设
export const STYLE_PRESETS = {
  'photorealistic': {
    negative_prompt: 'cartoon, anime, painting, drawing, sketch, low quality, blurry',
    guidance_scale: 7.5,
  },
  'artistic': {
    negative_prompt: 'low quality, blurry, distorted',
    guidance_scale: 8.0,
  },
  'anime': {
    negative_prompt: 'realistic, photograph, low quality, blurry',
    guidance_scale: 7.0,
  },
  'cartoon': {
    negative_prompt: 'realistic, photograph, dark, scary, low quality',
    guidance_scale: 7.0,
  },
  'cinematic': {
    negative_prompt: 'low quality, amateur, snapshot, casual',
    guidance_scale: 8.5,
  },
} as const;

// 应用风格预设
export function applyStylePreset(
  params: ImageGenerationParams,
  style: keyof typeof STYLE_PRESETS
): ImageGenerationParams {
  const preset = STYLE_PRESETS[style];
  return {
    ...params,
    negative_prompt: preset.negative_prompt,
    guidance_scale: preset.guidance_scale,
  };
}

// 检查fal.ai服务状态
export async function checkServiceStatus(): Promise<boolean> {
  try {
    // 尝试调用一个简单的模型来检查服务状态
    await fal.subscribe('fal-ai/flux/schnell', {
      input: {
        prompt: 'test',
        image_size: 'square',
        num_images: 1,
        num_inference_steps: 1,
      },
      timeout: 5000,
    });
    return true;
  } catch (error) {
    console.error('Service status check failed:', error);
    return false;
  }
}
