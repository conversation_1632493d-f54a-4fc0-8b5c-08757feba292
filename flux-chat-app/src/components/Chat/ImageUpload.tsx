'use client';

import React, { useState, useRef } from 'react';
import { 
  PhotoIcon,
  CloudArrowUpIcon,
  XMarkIcon,
  PencilIcon,
  EyeIcon
} from '@heroicons/react/24/outline';

interface UploadedImage {
  id: string;
  file: File;
  url: string;
  name: string;
  size: number;
  type: string;
}

interface ImageUploadProps {
  onImageUpload: (images: UploadedImage[]) => void;
  onImageEdit: (image: UploadedImage, instruction: string) => void;
  maxFiles?: number;
  maxSize?: number; // MB
  acceptedTypes?: string[];
}

export default function ImageUpload({ 
  onImageUpload, 
  onImageEdit,
  maxFiles = 5,
  maxSize = 10,
  acceptedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
}: ImageUploadProps) {
  const [uploadedImages, setUploadedImages] = useState<UploadedImage[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [editingImage, setEditingImage] = useState<UploadedImage | null>(null);
  const [editInstruction, setEditInstruction] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 处理文件选择
  const handleFileSelect = (files: FileList | null) => {
    if (!files) return;
    
    const validFiles = Array.from(files).filter(file => {
      // 检查文件类型
      if (!acceptedTypes.includes(file.type)) {
        alert(`不支持的文件类型: ${file.type}`);
        return false;
      }
      
      // 检查文件大小
      if (file.size > maxSize * 1024 * 1024) {
        alert(`文件过大: ${file.name} (最大${maxSize}MB)`);
        return false;
      }
      
      return true;
    });

    if (uploadedImages.length + validFiles.length > maxFiles) {
      alert(`最多只能上传${maxFiles}张图片`);
      return;
    }

    setIsUploading(true);
    
    const newImages: UploadedImage[] = validFiles.map(file => ({
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      file,
      url: URL.createObjectURL(file),
      name: file.name,
      size: file.size,
      type: file.type
    }));

    const updatedImages = [...uploadedImages, ...newImages];
    setUploadedImages(updatedImages);
    onImageUpload(updatedImages);
    setIsUploading(false);
  };

  // 拖拽处理
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    handleFileSelect(e.dataTransfer.files);
  };

  // 删除图片
  const removeImage = (imageId: string) => {
    const imageToRemove = uploadedImages.find(img => img.id === imageId);
    if (imageToRemove) {
      URL.revokeObjectURL(imageToRemove.url);
    }
    
    const updatedImages = uploadedImages.filter(img => img.id !== imageId);
    setUploadedImages(updatedImages);
    onImageUpload(updatedImages);
  };

  // 开始编辑图片
  const startImageEdit = (image: UploadedImage) => {
    setEditingImage(image);
    setEditInstruction('');
  };

  // 提交编辑指令
  const submitEdit = () => {
    if (editingImage && editInstruction.trim()) {
      onImageEdit(editingImage, editInstruction.trim());
      setEditingImage(null);
      setEditInstruction('');
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-4">
      {/* 上传区域 */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          isDragging
            ? 'border-blue-500 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={acceptedTypes.join(',')}
          onChange={(e) => handleFileSelect(e.target.files)}
          className="hidden"
        />
        
        <div className="space-y-2">
          <CloudArrowUpIcon className="w-12 h-12 text-gray-400 mx-auto" />
          <div>
            <button
              onClick={() => fileInputRef.current?.click()}
              disabled={isUploading || uploadedImages.length >= maxFiles}
              className="text-blue-600 hover:text-blue-800 font-medium disabled:text-gray-400"
            >
              点击上传图片
            </button>
            <span className="text-gray-500"> 或拖拽图片到此处</span>
          </div>
          <p className="text-sm text-gray-500">
            支持 JPG, PNG, WebP, GIF 格式，最大 {maxSize}MB，最多 {maxFiles} 张
          </p>
        </div>
      </div>

      {/* 已上传的图片列表 */}
      {uploadedImages.length > 0 && (
        <div className="space-y-3">
          <h4 className="font-medium text-gray-900">已上传的图片 ({uploadedImages.length})</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {uploadedImages.map((image) => (
              <div key={image.id} className="border border-gray-200 rounded-lg p-3">
                <div className="flex items-start space-x-3">
                  {/* 图片预览 */}
                  <div className="flex-shrink-0">
                    <img
                      src={image.url}
                      alt={image.name}
                      className="w-16 h-16 object-cover rounded"
                    />
                  </div>
                  
                  {/* 图片信息 */}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {image.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatFileSize(image.size)} • {image.type}
                    </p>
                    
                    {/* 操作按钮 */}
                    <div className="flex space-x-2 mt-2">
                      <button
                        onClick={() => startImageEdit(image)}
                        className="flex items-center space-x-1 px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                      >
                        <PencilIcon className="w-3 h-3" />
                        <span>编辑</span>
                      </button>
                      
                      <button
                        onClick={() => window.open(image.url, '_blank')}
                        className="flex items-center space-x-1 px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
                      >
                        <EyeIcon className="w-3 h-3" />
                        <span>查看</span>
                      </button>
                      
                      <button
                        onClick={() => removeImage(image.id)}
                        className="flex items-center space-x-1 px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors"
                      >
                        <XMarkIcon className="w-3 h-3" />
                        <span>删除</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 图片编辑模态框 */}
      {editingImage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            {/* 头部 */}
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">编辑图片</h3>
                <button
                  onClick={() => setEditingImage(null)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <XMarkIcon className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* 内容 */}
            <div className="p-6 space-y-4">
              {/* 图片预览 */}
              <div className="text-center">
                <img
                  src={editingImage.url}
                  alt={editingImage.name}
                  className="max-w-full max-h-64 object-contain mx-auto rounded"
                />
                <p className="text-sm text-gray-500 mt-2">{editingImage.name}</p>
              </div>

              {/* 编辑指令输入 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  编辑指令
                </label>
                <textarea
                  value={editInstruction}
                  onChange={(e) => setEditInstruction(e.target.value)}
                  placeholder="描述您想要对图片进行的编辑，例如：&#10;• 将背景改为蓝天白云&#10;• 给人物添加帽子&#10;• 调整图片为黑白风格&#10;• 移除背景中的杂物"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                  rows={4}
                />
              </div>

              {/* 编辑建议 */}
              <div className="bg-blue-50 p-3 rounded-lg">
                <h4 className="text-sm font-medium text-blue-900 mb-2">💡 编辑建议</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• 描述要具体明确，避免模糊表达</li>
                  <li>• 可以指定颜色、风格、位置等细节</li>
                  <li>• 支持添加、删除、修改图片元素</li>
                  <li>• 可以调整整体风格和色调</li>
                </ul>
              </div>
            </div>

            {/* 底部按钮 */}
            <div className="p-6 border-t border-gray-200 flex justify-end space-x-3">
              <button
                onClick={() => setEditingImage(null)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                取消
              </button>
              <button
                onClick={submitEdit}
                disabled={!editInstruction.trim()}
                className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                开始编辑
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
