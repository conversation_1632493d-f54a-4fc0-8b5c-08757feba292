'use client';

import React, { useState, useEffect } from 'react';
import { 
  Cog6ToothIcon,
  MoonIcon,
  SunIcon,
  LanguageIcon,
  BellIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';

interface Settings {
  theme: 'light' | 'dark' | 'auto';
  language: 'zh' | 'en';
  notifications: boolean;
  autoOptimize: boolean;
  saveHistory: boolean;
  apiProvider: 'openai' | 'demo';
  maxHistoryItems: number;
}

interface SettingsPanelProps {
  onSettingsChange: (settings: Settings) => void;
}

const DEFAULT_SETTINGS: Settings = {
  theme: 'light',
  language: 'zh',
  notifications: true,
  autoOptimize: true,
  saveHistory: true,
  apiProvider: 'demo',
  maxHistoryItems: 20
};

export default function SettingsPanel({ onSettingsChange }: SettingsPanelProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [settings, setSettings] = useState<Settings>(DEFAULT_SETTINGS);

  // 从localStorage加载设置
  useEffect(() => {
    const savedSettings = localStorage.getItem('app-settings');
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        setSettings({ ...DEFAULT_SETTINGS, ...parsed });
      } catch (error) {
        console.error('Failed to load settings:', error);
      }
    }
  }, []);

  // 保存设置
  const updateSettings = (newSettings: Partial<Settings>) => {
    const updatedSettings = { ...settings, ...newSettings };
    setSettings(updatedSettings);
    localStorage.setItem('app-settings', JSON.stringify(updatedSettings));
    onSettingsChange(updatedSettings);
  };

  // 重置设置
  const resetSettings = () => {
    setSettings(DEFAULT_SETTINGS);
    localStorage.setItem('app-settings', JSON.stringify(DEFAULT_SETTINGS));
    onSettingsChange(DEFAULT_SETTINGS);
  };

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-4 right-4 bg-white shadow-lg rounded-full p-3 hover:shadow-xl transition-shadow z-10"
        title="设置"
      >
        <Cog6ToothIcon className="w-5 h-5 text-gray-600" />
      </button>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-30 flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 max-h-[80vh] overflow-y-auto">
        {/* 头部 */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">设置</h2>
            <button
              onClick={() => setIsOpen(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>
        </div>

        {/* 设置内容 */}
        <div className="p-6 space-y-6">
          {/* 外观设置 */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center gap-2">
              <SunIcon className="w-5 h-5" />
              外观
            </h3>
            
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  主题
                </label>
                <div className="flex gap-2">
                  {[
                    { value: 'light', label: '浅色', icon: SunIcon },
                    { value: 'dark', label: '深色', icon: MoonIcon },
                    { value: 'auto', label: '自动', icon: Cog6ToothIcon }
                  ].map(({ value, label, icon: Icon }) => (
                    <button
                      key={value}
                      onClick={() => updateSettings({ theme: value as Settings['theme'] })}
                      className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm transition-colors ${
                        settings.theme === value
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      <Icon className="w-4 h-4" />
                      {label}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* 语言设置 */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center gap-2">
              <LanguageIcon className="w-5 h-5" />
              语言
            </h3>
            
            <select
              value={settings.language}
              onChange={(e) => updateSettings({ language: e.target.value as Settings['language'] })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="zh">中文</option>
              <option value="en">English</option>
            </select>
          </div>

          {/* 功能设置 */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center gap-2">
              <ShieldCheckIcon className="w-5 h-5" />
              功能
            </h3>
            
            <div className="space-y-3">
              <label className="flex items-center justify-between">
                <span className="text-sm text-gray-700">自动优化提示词</span>
                <input
                  type="checkbox"
                  checked={settings.autoOptimize}
                  onChange={(e) => updateSettings({ autoOptimize: e.target.checked })}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </label>
              
              <label className="flex items-center justify-between">
                <span className="text-sm text-gray-700">保存对话历史</span>
                <input
                  type="checkbox"
                  checked={settings.saveHistory}
                  onChange={(e) => updateSettings({ saveHistory: e.target.checked })}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </label>
              
              <label className="flex items-center justify-between">
                <span className="text-sm text-gray-700">桌面通知</span>
                <input
                  type="checkbox"
                  checked={settings.notifications}
                  onChange={(e) => updateSettings({ notifications: e.target.checked })}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </label>
            </div>
          </div>

          {/* API设置 */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">API设置</h3>
            
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  API提供商
                </label>
                <select
                  value={settings.apiProvider}
                  onChange={(e) => updateSettings({ apiProvider: e.target.value as Settings['apiProvider'] })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="demo">演示模式</option>
                  <option value="openai">OpenAI</option>
                </select>
                <p className="text-xs text-gray-500 mt-1">
                  演示模式无需API密钥，功能有限
                </p>
              </div>
            </div>
          </div>

          {/* 存储设置 */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">存储</h3>
            
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  最大历史记录数量
                </label>
                <input
                  type="number"
                  min="5"
                  max="100"
                  value={settings.maxHistoryItems}
                  onChange={(e) => updateSettings({ maxHistoryItems: parseInt(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="p-6 border-t border-gray-200 flex gap-3">
          <button
            onClick={resetSettings}
            className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
          >
            重置设置
          </button>
          <button
            onClick={() => setIsOpen(false)}
            className="flex-1 px-4 py-2 text-white bg-blue-500 rounded-lg hover:bg-blue-600 transition-colors"
          >
            完成
          </button>
        </div>
      </div>
    </div>
  );
}
