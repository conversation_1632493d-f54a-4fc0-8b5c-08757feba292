'use client';

import React, { useState } from 'react';
import { 
  ArrowDownTrayIcon,
  ArrowsRightLeftIcon,
  ShareIcon,
  ClockIcon,
  CpuChipIcon,
  MagnifyingGlassIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';

interface EditedImage {
  url: string;
  width: number;
  height: number;
  content_type: string;
}

interface EditMetadata {
  model_used: string;
  instruction_used: string;
  processing_time: number;
  original_filename: string;
  strength_used?: number;
  seed?: number;
  demo_notice?: string;
}

interface ImageEditResultProps {
  originalImage: {
    url: string;
    name: string;
  };
  editedImage: EditedImage;
  metadata: EditMetadata;
  onReEdit?: (newInstruction: string) => void;
  onDownload?: () => void;
}

export default function ImageEditResult({ 
  originalImage, 
  editedImage, 
  metadata,
  onReEdit,
  onDownload 
}: ImageEditResultProps) {
  const [showComparison, setShowComparison] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [fullscreenImage, setFullscreenImage] = useState<'original' | 'edited'>('edited');

  // 下载编辑后的图片
  const downloadEditedImage = async () => {
    try {
      const response = await fetch(editedImage.url);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `edited_${metadata.original_filename}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      if (onDownload) onDownload();
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  // 分享图片
  const shareImage = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: '编辑后的图片',
          text: `使用AI编辑: ${metadata.instruction_used}`,
          url: editedImage.url,
        });
      } catch (error) {
        console.error('Share failed:', error);
      }
    } else {
      // 回退到复制链接
      navigator.clipboard.writeText(editedImage.url);
      alert('图片链接已复制到剪贴板');
    }
  };

  // 格式化处理时间
  const formatProcessingTime = (time: number) => {
    if (time < 1000) return `${time}ms`;
    return `${(time / 1000).toFixed(1)}s`;
  };

  // 获取模型显示名称
  const getModelDisplayName = (model: string) => {
    const modelNames: Record<string, string> = {
      'instruct-pix2pix': 'InstructPix2Pix',
      'flux-fill': 'FLUX Fill',
      'flux-redux': 'FLUX Redux',
      'controlnet-inpaint': 'ControlNet Inpaint',
    };
    return modelNames[model] || model;
  };

  // 全屏查看
  const openFullscreen = (imageType: 'original' | 'edited') => {
    setFullscreenImage(imageType);
    setIsFullscreen(true);
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
      {/* 演示模式提示 */}
      {metadata.demo_notice && (
        <div className="p-3 bg-yellow-50 border-b border-yellow-200">
          <p className="text-sm text-yellow-800">
            🎭 <strong>演示模式</strong>: 这是示例结果。配置 FAL API 密钥以进行真实图片编辑。
          </p>
        </div>
      )}

      {/* 图片对比区域 */}
      <div className="p-4">
        {showComparison ? (
          // 对比视图
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 原图 */}
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-gray-700">原图</h4>
              <div className="relative group">
                <img
                  src={originalImage.url}
                  alt="原图"
                  className="w-full h-auto rounded-lg shadow-sm cursor-pointer"
                  onClick={() => openFullscreen('original')}
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
                  <button
                    onClick={() => openFullscreen('original')}
                    className="p-2 bg-white rounded-full shadow-lg"
                  >
                    <MagnifyingGlassIcon className="w-4 h-4 text-gray-700" />
                  </button>
                </div>
              </div>
            </div>

            {/* 编辑后 */}
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-gray-700">编辑后</h4>
              <div className="relative group">
                <img
                  src={editedImage.url}
                  alt="编辑后"
                  className="w-full h-auto rounded-lg shadow-sm cursor-pointer"
                  onClick={() => openFullscreen('edited')}
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
                  <button
                    onClick={() => openFullscreen('edited')}
                    className="p-2 bg-white rounded-full shadow-lg"
                  >
                    <MagnifyingGlassIcon className="w-4 h-4 text-gray-700" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        ) : (
          // 单图视图
          <div className="text-center">
            <img
              src={editedImage.url}
              alt="编辑结果"
              className="max-w-full h-auto rounded-lg shadow-sm mx-auto cursor-pointer"
              onClick={() => openFullscreen('edited')}
            />
          </div>
        )}

        {/* 视图切换按钮 */}
        <div className="flex justify-center mt-4">
          <button
            onClick={() => setShowComparison(!showComparison)}
            className="flex items-center space-x-2 px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            <ArrowsRightLeftIcon className="w-4 h-4" />
            <span>{showComparison ? '单图查看' : '对比查看'}</span>
          </button>
        </div>
      </div>

      {/* 编辑信息和操作 */}
      <div className="border-t border-gray-200 p-4 bg-gray-50">
        {/* 编辑指令 */}
        <div className="mb-3">
          <h5 className="text-sm font-medium text-gray-700 mb-1">编辑指令</h5>
          <p className="text-sm text-gray-600 bg-white p-2 rounded border">
            {metadata.instruction_used}
          </p>
        </div>

        {/* 元数据 */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-4 text-sm text-gray-600">
            <div className="flex items-center space-x-1">
              <CpuChipIcon className="w-4 h-4" />
              <span>{getModelDisplayName(metadata.model_used)}</span>
            </div>
            
            <div className="flex items-center space-x-1">
              <ClockIcon className="w-4 h-4" />
              <span>{formatProcessingTime(metadata.processing_time)}</span>
            </div>
            
            {metadata.strength_used && (
              <div className="text-xs">
                强度: {metadata.strength_used}
              </div>
            )}
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex flex-wrap gap-2">
          <button
            onClick={downloadEditedImage}
            className="flex items-center space-x-1 px-3 py-2 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors"
          >
            <ArrowDownTrayIcon className="w-4 h-4" />
            <span>下载</span>
          </button>
          
          <button
            onClick={shareImage}
            className="flex items-center space-x-1 px-3 py-2 bg-gray-100 text-gray-700 text-sm rounded hover:bg-gray-200 transition-colors"
          >
            <ShareIcon className="w-4 h-4" />
            <span>分享</span>
          </button>
          
          {onReEdit && (
            <button
              onClick={() => {
                const newInstruction = prompt('请输入新的编辑指令:', metadata.instruction_used);
                if (newInstruction && newInstruction.trim()) {
                  onReEdit(newInstruction.trim());
                }
              }}
              className="flex items-center space-x-1 px-3 py-2 bg-green-100 text-green-700 text-sm rounded hover:bg-green-200 transition-colors"
            >
              <ArrowPathIcon className="w-4 h-4" />
              <span>重新编辑</span>
            </button>
          )}
        </div>
      </div>

      {/* 全屏模态框 */}
      {isFullscreen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4"
          onClick={() => setIsFullscreen(false)}
        >
          <div className="relative max-w-full max-h-full">
            <img
              src={fullscreenImage === 'original' ? originalImage.url : editedImage.url}
              alt={fullscreenImage === 'original' ? '原图' : '编辑后'}
              className="max-w-full max-h-full object-contain"
            />
            
            {/* 切换按钮 */}
            <div className="absolute top-4 left-4 flex space-x-2">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setFullscreenImage('original');
                }}
                className={`px-3 py-1 text-sm rounded ${
                  fullscreenImage === 'original'
                    ? 'bg-white text-black'
                    : 'bg-black bg-opacity-50 text-white'
                }`}
              >
                原图
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setFullscreenImage('edited');
                }}
                className={`px-3 py-1 text-sm rounded ${
                  fullscreenImage === 'edited'
                    ? 'bg-white text-black'
                    : 'bg-black bg-opacity-50 text-white'
                }`}
              >
                编辑后
              </button>
            </div>
            
            {/* 关闭按钮 */}
            <button
              onClick={() => setIsFullscreen(false)}
              className="absolute top-4 right-4 text-white text-2xl hover:text-gray-300"
            >
              ✕
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
