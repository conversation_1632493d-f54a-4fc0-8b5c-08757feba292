'use client';

import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useChat } from '@/contexts/ChatContext';
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import PromptPreview from './PromptPreview';
import SessionHistory from './SessionHistory';
import PromptTemplates from './PromptTemplates';
import SettingsPanel from './SettingsPanel';
import { ArrowPathIcon, TrashIcon } from '@heroicons/react/24/outline';

export default function ChatInterface() {
  const { state, addMessage, setLoading, resetSession, dispatch } = useChat();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [settings, setSettings] = useState({
    autoOptimize: true,
    saveHistory: true,
    apiProvider: 'demo' as 'openai' | 'demo'
  });

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [state.session.messages]);

  // 发送消息处理
  const handleSendMessage = useCallback(async (message: string) => {
    // 添加用户消息
    addMessage(message, 'user');
    setLoading(true);

    try {
      // 准备对话历史
      const conversationHistory = state.session.messages.map(msg => msg.content);

      // 调用聊天API
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message,
          conversationHistory,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to get response');
      }

      const data = await response.json();

      // 添加助手回复
      addMessage(data.response, 'assistant', {
        type: data.metadata?.type || 'analysis',
        extractedElements: data.metadata?.extractedElements || [],
      });

      // 更新需求信息
      if (data.extractedRequirement) {
        dispatch({
          type: 'UPDATE_REQUIREMENT',
          payload: data.extractedRequirement,
        });
      }

      // 如果收集到足够信息，自动生成优化提示词
      if (!data.needsClarification && data.extractedRequirement) {
        await generateOptimizedPrompt(conversationHistory.concat([message, data.response]));
      }

    } catch (error) {
      console.error('Error sending message:', error);
      addMessage('抱歉，发生了错误。请稍后重试。', 'assistant', {
        type: 'error'
      });
    } finally {
      setLoading(false);
    }
  }, [state.session.messages, addMessage, setLoading, dispatch]);

  // 生成优化提示词
  const generateOptimizedPrompt = useCallback(async (conversationHistory: string[]) => {
    try {
      const requirement = conversationHistory.join(' ');
      
      const response = await fetch('/api/optimize-prompt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          requirement,
          conversationHistory,
          useAI: true,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to optimize prompt');
      }

      const data = await response.json();

      if (data.optimization) {
        dispatch({
          type: 'SET_OPTIMIZED_PROMPT',
          payload: data.optimization,
        });

        // 添加优化结果消息
        addMessage(
          `我已经为您优化了提示词！优化后的提示词更加专业和详细，可以帮助生成更好的图像效果。`,
          'assistant',
          {
            type: 'optimization',
            optimizedPrompt: data.optimization.optimizedPrompt,
          }
        );
      }

    } catch (error) {
      console.error('Error generating optimized prompt:', error);
    }
  }, [dispatch, addMessage]);

  // 处理图像生成
  const handleGenerateImage = useCallback((prompt: string) => {
    // 这里将来集成图像生成API
    addMessage(
      `正在使用以下提示词生成图像：\n\n"${prompt}"\n\n图像生成功能即将推出，敬请期待！`,
      'assistant',
      {
        type: 'image_generation',
      }
    );
  }, [addMessage]);

  // 处理提示词编辑
  const handleEditPrompt = useCallback((newPrompt: string) => {
    if (state.optimizedPrompt) {
      const updatedOptimization = {
        ...state.optimizedPrompt,
        optimizedPrompt: newPrompt,
        improvements: [...state.optimizedPrompt.improvements, '用户手动编辑'],
      };
      
      dispatch({
        type: 'SET_OPTIMIZED_PROMPT',
        payload: updatedOptimization,
      });

      addMessage(
        '提示词已更新！您可以使用新的提示词生成图像。',
        'assistant',
        {
          type: 'optimization',
          optimizedPrompt: newPrompt,
        }
      );
    }
  }, [state.optimizedPrompt, dispatch, addMessage]);

  // 处理会话加载
  const handleLoadSession = useCallback((session: any) => {
    // 重置当前会话并加载历史会话
    dispatch({ type: 'RESET_SESSION' });
    session.messages.forEach((msg: any) => {
      addMessage(msg.content, msg.role, msg.metadata);
    });
  }, [dispatch, addMessage]);

  // 处理模板选择
  const handleSelectTemplate = useCallback((prompt: string) => {
    addMessage(prompt, 'user');
  }, [addMessage]);

  // 处理设置变更
  const handleSettingsChange = useCallback((newSettings: any) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  }, []);

  // 保存当前会话到历史
  const saveCurrentSession = useCallback(() => {
    if (settings.saveHistory && state.session.messages.length > 0) {
      const sessionToSave = {
        ...state.session,
        updatedAt: new Date()
      };
      // 这里可以触发保存到SessionHistory组件
    }
  }, [settings.saveHistory, state.session]);

  // 当会话更新时保存
  useEffect(() => {
    saveCurrentSession();
  }, [state.session.messages, saveCurrentSession]);

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      {/* 头部 */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-semibold text-gray-900">AI图像生成助手</h1>
            <p className="text-sm text-gray-600">让我帮您优化图像生成提示词</p>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => generateOptimizedPrompt(state.session.messages.map(m => m.content))}
              disabled={state.isLoading || state.session.messages.length === 0}
              className="flex items-center space-x-1 px-3 py-2 text-sm bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              title="重新生成优化提示词"
            >
              <ArrowPathIcon className="w-4 h-4" />
              <span>优化提示词</span>
            </button>
            
            <button
              onClick={resetSession}
              disabled={state.isLoading}
              className="flex items-center space-x-1 px-3 py-2 text-sm bg-gray-500 text-white rounded-lg hover:bg-gray-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              title="开始新对话"
            >
              <TrashIcon className="w-4 h-4" />
              <span>新对话</span>
            </button>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* 消息列表 */}
        <div className="flex-1 overflow-hidden">
          <MessageList messages={state.session.messages} />
          <div ref={messagesEndRef} />
        </div>

        {/* 优化提示词预览 */}
        {state.optimizedPrompt && (
          <div className="px-4 py-2 bg-gray-50">
            <PromptPreview
              optimization={state.optimizedPrompt}
              onGenerateImage={handleGenerateImage}
              onEditPrompt={handleEditPrompt}
            />
          </div>
        )}

        {/* 输入区域 */}
        <MessageInput
          onSendMessage={handleSendMessage}
          isLoading={state.isLoading}
          placeholder={
            state.session.messages.length === 0
              ? "描述您想要生成的图像，例如：一只可爱的小猫坐在花园里..."
              : "继续描述更多细节..."
          }
        />
      </div>

      {/* 侧边栏组件 */}
      <SessionHistory
        onLoadSession={handleLoadSession}
        currentSessionId={state.session.id}
      />

      <PromptTemplates
        onSelectTemplate={handleSelectTemplate}
      />

      <SettingsPanel
        onSettingsChange={handleSettingsChange}
      />
    </div>
  );
}
