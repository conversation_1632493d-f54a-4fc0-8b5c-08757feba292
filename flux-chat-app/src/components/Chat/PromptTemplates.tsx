'use client';

import React, { useState } from 'react';
import { 
  SparklesIcon, 
  PlusIcon, 
  HeartIcon,
  EyeIcon,
  DocumentDuplicateIcon 
} from '@heroicons/react/24/outline';

interface PromptTemplate {
  id: string;
  title: string;
  description: string;
  prompt: string;
  category: string;
  tags: string[];
  isPopular?: boolean;
}

interface PromptTemplatesProps {
  onSelectTemplate: (prompt: string) => void;
}

const BUILT_IN_TEMPLATES: PromptTemplate[] = [
  {
    id: '1',
    title: '可爱动物肖像',
    description: '生成可爱动物的高质量肖像照片',
    prompt: '一只{动物}，可爱的表情，专业摄影，柔和光照，温暖色调',
    category: '动物',
    tags: ['可爱', '肖像', '摄影'],
    isPopular: true
  },
  {
    id: '2',
    title: '未来科幻城市',
    description: '创造充满科技感的未来城市景观',
    prompt: '未来科幻城市，{时间}，霓虹灯，高楼大厦，飞行汽车，赛博朋克风格',
    category: '科幻',
    tags: ['未来', '城市', '科幻'],
    isPopular: true
  },
  {
    id: '3',
    title: '古典油画风格',
    description: '模仿古典大师的油画技法',
    prompt: '{主题}，古典油画风格，{艺术家}风格，细腻笔触，丰富色彩',
    category: '艺术',
    tags: ['油画', '古典', '艺术'],
    isPopular: false
  },
  {
    id: '4',
    title: '梦幻水彩画',
    description: '柔美的水彩画效果',
    prompt: '{主题}，水彩画风格，柔和色彩，流动笔触，梦幻氛围',
    category: '艺术',
    tags: ['水彩', '梦幻', '柔美'],
    isPopular: false
  },
  {
    id: '5',
    title: '极简主义设计',
    description: '简洁现代的极简风格',
    prompt: '{主题}，极简主义，简洁构图，单色调，现代设计',
    category: '设计',
    tags: ['极简', '现代', '设计'],
    isPopular: true
  },
  {
    id: '6',
    title: '魔幻森林场景',
    description: '神秘的魔幻森林环境',
    prompt: '魔幻森林，{时间}，神秘光线，古老树木，魔法氛围，奇幻生物',
    category: '奇幻',
    tags: ['魔幻', '森林', '奇幻'],
    isPopular: false
  }
];

export default function PromptTemplates({ onSelectTemplate }: PromptTemplatesProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('全部');
  const [customTemplates, setCustomTemplates] = useState<PromptTemplate[]>([]);

  // 获取所有模板
  const allTemplates = [...BUILT_IN_TEMPLATES, ...customTemplates];
  
  // 获取分类
  const categories = ['全部', ...Array.from(new Set(allTemplates.map(t => t.category)))];
  
  // 过滤模板
  const filteredTemplates = selectedCategory === '全部' 
    ? allTemplates 
    : allTemplates.filter(t => t.category === selectedCategory);

  // 处理模板选择
  const handleSelectTemplate = (template: PromptTemplate) => {
    let prompt = template.prompt;
    
    // 简单的变量替换提示
    const variables = prompt.match(/\{([^}]+)\}/g);
    if (variables) {
      const replacements: Record<string, string> = {};
      
      variables.forEach(variable => {
        const key = variable.slice(1, -1);
        const value = prompt(`请输入${key}:`);
        if (value) {
          replacements[variable] = value;
        }
      });
      
      Object.entries(replacements).forEach(([key, value]) => {
        prompt = prompt.replace(new RegExp(key.replace(/[{}]/g, '\\$&'), 'g'), value);
      });
    }
    
    onSelectTemplate(prompt);
    setIsOpen(false);
  };

  // 复制模板
  const copyTemplate = async (template: PromptTemplate) => {
    try {
      await navigator.clipboard.writeText(template.prompt);
      // 这里可以添加成功提示
    } catch (error) {
      console.error('Failed to copy template:', error);
    }
  };

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="fixed right-4 top-1/2 transform -translate-y-1/2 bg-white shadow-lg rounded-lg p-3 hover:shadow-xl transition-shadow z-10"
        title="提示词模板"
      >
        <SparklesIcon className="w-5 h-5 text-gray-600" />
      </button>
    );
  }

  return (
    <div className="fixed right-0 top-0 h-full w-80 bg-white shadow-xl z-20 flex flex-col">
      {/* 头部 */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-gray-900">提示词模板</h3>
          <button
            onClick={() => setIsOpen(false)}
            className="text-gray-500 hover:text-gray-700"
          >
            ✕
          </button>
        </div>
        
        {/* 分类选择 */}
        <div className="flex flex-wrap gap-2">
          {categories.map(category => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-3 py-1 text-sm rounded-full transition-colors ${
                selectedCategory === category
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {category}
            </button>
          ))}
        </div>
      </div>

      {/* 模板列表 */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-3">
          {filteredTemplates.map((template) => (
            <div
              key={template.id}
              className="border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between mb-2">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h4 className="font-medium text-gray-900">{template.title}</h4>
                    {template.isPopular && (
                      <HeartIcon className="w-4 h-4 text-red-500" />
                    )}
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{template.description}</p>
                </div>
              </div>
              
              {/* 标签 */}
              <div className="flex flex-wrap gap-1 mb-3">
                {template.tags.map(tag => (
                  <span
                    key={tag}
                    className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded"
                  >
                    {tag}
                  </span>
                ))}
              </div>
              
              {/* 提示词预览 */}
              <div className="bg-gray-50 p-2 rounded text-sm text-gray-700 mb-3 font-mono">
                {template.prompt.length > 60 
                  ? template.prompt.slice(0, 60) + '...'
                  : template.prompt
                }
              </div>
              
              {/* 操作按钮 */}
              <div className="flex gap-2">
                <button
                  onClick={() => handleSelectTemplate(template)}
                  className="flex-1 flex items-center justify-center gap-1 px-3 py-2 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors"
                >
                  <PlusIcon className="w-4 h-4" />
                  使用
                </button>
                
                <button
                  onClick={() => copyTemplate(template)}
                  className="px-3 py-2 bg-gray-100 text-gray-700 text-sm rounded hover:bg-gray-200 transition-colors"
                  title="复制模板"
                >
                  <DocumentDuplicateIcon className="w-4 h-4" />
                </button>
                
                <button
                  className="px-3 py-2 bg-gray-100 text-gray-700 text-sm rounded hover:bg-gray-200 transition-colors"
                  title="查看详情"
                >
                  <EyeIcon className="w-4 h-4" />
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 底部 */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <p className="text-xs text-gray-500 text-center">
          共 {filteredTemplates.length} 个模板
        </p>
      </div>
    </div>
  );
}
