'use client';

import React, { useState, useEffect } from 'react';
import { 
  CogIcon,
  PhotoIcon,
  SparklesIcon,
  AdjustmentsHorizontalIcon
} from '@heroicons/react/24/outline';

interface ImageGenerationConfig {
  model: string;
  size: string;
  num_images: number;
  style_preset?: string;
  guidance_scale: number;
  num_inference_steps?: number;
  seed?: number;
}

interface ImageGenerationPanelProps {
  onGenerate: (config: ImageGenerationConfig, prompt: string) => void;
  isGenerating: boolean;
  optimizedPrompt?: string;
}

export default function ImageGenerationPanel({ 
  onGenerate, 
  isGenerating, 
  optimizedPrompt 
}: ImageGenerationPanelProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [config, setConfig] = useState<ImageGenerationConfig>({
    model: 'flux-schnell',
    size: 'landscape',
    num_images: 1,
    guidance_scale: 7.5,
  });
  const [availableModels, setAvailableModels] = useState<any>({});
  const [customPrompt, setCustomPrompt] = useState('');

  // 加载可用模型和配置
  useEffect(() => {
    const loadConfig = async () => {
      try {
        const response = await fetch('/api/generate-image');
        if (response.ok) {
          const data = await response.json();
          setAvailableModels(data);
        }
      } catch (error) {
        console.error('Failed to load image generation config:', error);
      }
    };
    loadConfig();
  }, []);

  // 当有优化提示词时自动填充
  useEffect(() => {
    if (optimizedPrompt && !customPrompt) {
      setCustomPrompt(optimizedPrompt);
    }
  }, [optimizedPrompt, customPrompt]);

  const handleGenerate = () => {
    const promptToUse = customPrompt || optimizedPrompt || '';
    if (!promptToUse.trim()) {
      alert('请输入提示词或先优化对话中的提示词');
      return;
    }
    onGenerate(config, promptToUse);
    setIsOpen(false);
  };

  const getModelInfo = (modelKey: string) => {
    return availableModels.models?.[modelKey] || { 
      name: modelKey, 
      description: '未知模型',
      speed: 'medium',
      quality: 'good'
    };
  };

  const getSpeedColor = (speed: string) => {
    switch (speed) {
      case 'fast': return 'text-green-600 bg-green-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'slow': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getQualityColor = (quality: string) => {
    switch (quality) {
      case 'excellent': return 'text-purple-600 bg-purple-50';
      case 'high': return 'text-blue-600 bg-blue-50';
      case 'good': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  if (!isOpen) {
    return (
      <div className="flex items-center space-x-2">
        {optimizedPrompt && (
          <button
            onClick={() => onGenerate(config, optimizedPrompt)}
            disabled={isGenerating}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            {isGenerating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>生成中...</span>
              </>
            ) : (
              <>
                <PhotoIcon className="w-4 h-4" />
                <span>生成图像</span>
              </>
            )}
          </button>
        )}
        
        <button
          onClick={() => setIsOpen(true)}
          className="flex items-center space-x-2 px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          title="图像生成设置"
        >
          <CogIcon className="w-4 h-4" />
          <span>设置</span>
        </button>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-40 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* 头部 */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900 flex items-center space-x-2">
              <SparklesIcon className="w-6 h-6 text-blue-500" />
              <span>图像生成设置</span>
            </h2>
            <button
              onClick={() => setIsOpen(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>
        </div>

        {/* 内容 */}
        <div className="p-6 space-y-6">
          {/* 提示词编辑 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              提示词
            </label>
            <textarea
              value={customPrompt}
              onChange={(e) => setCustomPrompt(e.target.value)}
              placeholder={optimizedPrompt || "输入图像描述..."}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
              rows={3}
            />
            {optimizedPrompt && (
              <button
                onClick={() => setCustomPrompt(optimizedPrompt)}
                className="mt-2 text-sm text-blue-600 hover:text-blue-800"
              >
                使用优化后的提示词
              </button>
            )}
          </div>

          {/* 模型选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              生成模型
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {Object.entries(availableModels.models || {}).map(([key, model]: [string, any]) => (
                <div
                  key={key}
                  onClick={() => setConfig({ ...config, model: key })}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    config.model === key
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center justify-between mb-1">
                    <h4 className="font-medium text-gray-900">{model.name}</h4>
                    <div className="flex space-x-1">
                      <span className={`px-2 py-1 text-xs rounded ${getSpeedColor(model.speed)}`}>
                        {model.speed}
                      </span>
                      <span className={`px-2 py-1 text-xs rounded ${getQualityColor(model.quality)}`}>
                        {model.quality}
                      </span>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600">{model.description}</p>
                </div>
              ))}
            </div>
          </div>

          {/* 图像尺寸 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              图像尺寸
            </label>
            <div className="grid grid-cols-3 gap-2">
              {Object.entries(availableModels.sizes || {}).map(([key, size]: [string, any]) => (
                <button
                  key={key}
                  onClick={() => setConfig({ ...config, size: key })}
                  className={`p-2 text-sm border rounded transition-colors ${
                    config.size === key
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="font-medium">{key}</div>
                  <div className="text-xs text-gray-500">{size}</div>
                </button>
              ))}
            </div>
          </div>

          {/* 生成数量 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              生成数量: {config.num_images}
            </label>
            <input
              type="range"
              min="1"
              max="4"
              value={config.num_images}
              onChange={(e) => setConfig({ ...config, num_images: parseInt(e.target.value) })}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>1张</span>
              <span>4张</span>
            </div>
          </div>

          {/* 高级设置 */}
          <details className="border border-gray-200 rounded-lg">
            <summary className="p-3 cursor-pointer flex items-center space-x-2 hover:bg-gray-50">
              <AdjustmentsHorizontalIcon className="w-4 h-4" />
              <span className="font-medium">高级设置</span>
            </summary>
            <div className="p-3 border-t border-gray-200 space-y-4">
              {/* 引导强度 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  引导强度: {config.guidance_scale}
                </label>
                <input
                  type="range"
                  min="1"
                  max="20"
                  step="0.5"
                  value={config.guidance_scale}
                  onChange={(e) => setConfig({ ...config, guidance_scale: parseFloat(e.target.value) })}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>创意</span>
                  <span>精确</span>
                </div>
              </div>

              {/* 推理步数 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  推理步数 (可选)
                </label>
                <input
                  type="number"
                  min="1"
                  max="100"
                  value={config.num_inference_steps || ''}
                  onChange={(e) => setConfig({ 
                    ...config, 
                    num_inference_steps: e.target.value ? parseInt(e.target.value) : undefined 
                  })}
                  placeholder="使用默认值"
                  className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* 随机种子 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  随机种子 (可选)
                </label>
                <input
                  type="number"
                  value={config.seed || ''}
                  onChange={(e) => setConfig({ 
                    ...config, 
                    seed: e.target.value ? parseInt(e.target.value) : undefined 
                  })}
                  placeholder="随机生成"
                  className="w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </details>
        </div>

        {/* 底部按钮 */}
        <div className="p-6 border-t border-gray-200 flex justify-end space-x-3">
          <button
            onClick={() => setIsOpen(false)}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
          >
            取消
          </button>
          <button
            onClick={handleGenerate}
            disabled={isGenerating || !customPrompt.trim()}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
          >
            {isGenerating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>生成中...</span>
              </>
            ) : (
              <>
                <PhotoIcon className="w-4 h-4" />
                <span>开始生成</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
