'use client';

import React, { useState } from 'react';
import { PromptOptimization } from '@/lib/types';
import { 
  ClipboardDocumentIcon, 
  CheckIcon, 
  SparklesIcon,
  AdjustmentsHorizontalIcon,
  EyeIcon
} from '@heroicons/react/24/outline';

interface PromptPreviewProps {
  optimization: PromptOptimization;
  onGenerateImage?: (prompt: string) => void;
  onEditPrompt?: (prompt: string) => void;
}

export default function PromptPreview({ 
  optimization, 
  onGenerateImage, 
  onEditPrompt 
}: PromptPreviewProps) {
  const [copied, setCopied] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editedPrompt, setEditedPrompt] = useState(optimization.optimizedPrompt);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(optimization.optimizedPrompt);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  const handleSaveEdit = () => {
    if (onEditPrompt) {
      onEditPrompt(editedPrompt);
    }
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    setEditedPrompt(optimization.optimizedPrompt);
    setIsEditing(false);
  };

  return (
    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 mb-4">
      {/* 标题 */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <SparklesIcon className="w-5 h-5 text-blue-500" />
          <h3 className="text-lg font-semibold text-gray-900">优化后的提示词</h3>
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-green-400 rounded-full"></div>
            <span className="text-sm text-gray-600">
              信心度: {Math.round(optimization.confidence * 100)}%
            </span>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setIsEditing(!isEditing)}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-white rounded-lg transition-colors"
            title="编辑提示词"
          >
            <AdjustmentsHorizontalIcon className="w-4 h-4" />
          </button>
          
          <button
            onClick={handleCopy}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-white rounded-lg transition-colors"
            title="复制提示词"
          >
            {copied ? (
              <CheckIcon className="w-4 h-4 text-green-500" />
            ) : (
              <ClipboardDocumentIcon className="w-4 h-4" />
            )}
          </button>
        </div>
      </div>

      {/* 提示词内容 */}
      <div className="mb-4">
        {isEditing ? (
          <div className="space-y-3">
            <textarea
              value={editedPrompt}
              onChange={(e) => setEditedPrompt(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows={4}
              placeholder="编辑您的提示词..."
            />
            <div className="flex justify-end space-x-2">
              <button
                onClick={handleCancelEdit}
                className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleSaveEdit}
                className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              >
                保存
              </button>
            </div>
          </div>
        ) : (
          <div className="bg-white rounded-lg p-4 border border-gray-200">
            <p className="text-gray-800 leading-relaxed font-mono text-sm">
              {optimization.optimizedPrompt}
            </p>
          </div>
        )}
      </div>

      {/* 改进说明 */}
      {optimization.improvements.length > 0 && (
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">优化改进:</h4>
          <ul className="space-y-1">
            {optimization.improvements.map((improvement, index) => (
              <li key={index} className="flex items-start space-x-2 text-sm text-gray-600">
                <CheckIcon className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                <span>{improvement}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* 建议参数 */}
      {optimization.suggestedParameters && (
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">建议参数:</h4>
          <div className="flex flex-wrap gap-2">
            {Object.entries(optimization.suggestedParameters).map(([key, value]) => (
              <span 
                key={key}
                className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
              >
                {getParameterLabel(key)}: {value}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* 对比显示 */}
      <details className="mb-4">
        <summary className="cursor-pointer text-sm font-medium text-gray-700 hover:text-gray-900 flex items-center space-x-1">
          <EyeIcon className="w-4 h-4" />
          <span>查看原始 vs 优化对比</span>
        </summary>
        <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h5 className="text-xs font-medium text-gray-500 mb-2">原始描述:</h5>
            <div className="bg-gray-50 p-3 rounded text-sm text-gray-700">
              {optimization.originalPrompt}
            </div>
          </div>
          <div>
            <h5 className="text-xs font-medium text-gray-500 mb-2">优化后:</h5>
            <div className="bg-blue-50 p-3 rounded text-sm text-gray-700">
              {optimization.optimizedPrompt}
            </div>
          </div>
        </div>
      </details>

      {/* 操作按钮 */}
      <div className="flex flex-wrap gap-3">
        {onGenerateImage && (
          <button
            onClick={() => onGenerateImage(optimization.optimizedPrompt)}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            <SparklesIcon className="w-4 h-4" />
            <span>生成图像</span>
          </button>
        )}
        
        <button
          onClick={handleCopy}
          className="flex items-center space-x-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
        >
          {copied ? (
            <>
              <CheckIcon className="w-4 h-4 text-green-500" />
              <span>已复制</span>
            </>
          ) : (
            <>
              <ClipboardDocumentIcon className="w-4 h-4" />
              <span>复制提示词</span>
            </>
          )}
        </button>
      </div>
    </div>
  );
}

function getParameterLabel(key: string): string {
  const labels: Record<string, string> = {
    aspectRatio: '宽高比',
    style: '风格',
    quality: '质量'
  };
  return labels[key] || key;
}
