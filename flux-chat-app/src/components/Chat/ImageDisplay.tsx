'use client';

import React, { useState } from 'react';
import { 
  PhotoIcon,
  ArrowDownTrayIcon,
  MagnifyingGlassIcon,
  ShareIcon,
  HeartIcon,
  ClockIcon,
  CpuChipIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';

interface GeneratedImage {
  url: string;
  width: number;
  height: number;
  content_type: string;
}

interface ImageMetadata {
  model_used: string;
  generation_time?: number;
  seed?: number;
  prompt_used: string;
  parameters: any;
}

interface ImageDisplayProps {
  images: GeneratedImage[];
  metadata: ImageMetadata;
  isLoading?: boolean;
  onRegenerate?: (seed?: number) => void;
  onVariation?: (imageUrl: string) => void;
}

export default function ImageDisplay({ 
  images, 
  metadata, 
  isLoading = false,
  onRegenerate,
  onVariation 
}: ImageDisplayProps) {
  const [selectedImage, setSelectedImage] = useState<number>(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [likedImages, setLikedImages] = useState<Set<number>>(new Set());

  // 下载图像
  const downloadImage = async (imageUrl: string, index: number) => {
    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `generated-image-${index + 1}.jpg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  // 分享图像
  const shareImage = async (imageUrl: string) => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'AI生成的图像',
          text: metadata.prompt_used,
          url: imageUrl,
        });
      } catch (error) {
        console.error('Share failed:', error);
      }
    } else {
      // 回退到复制链接
      navigator.clipboard.writeText(imageUrl);
      alert('图像链接已复制到剪贴板');
    }
  };

  // 切换喜欢状态
  const toggleLike = (index: number) => {
    const newLiked = new Set(likedImages);
    if (newLiked.has(index)) {
      newLiked.delete(index);
    } else {
      newLiked.add(index);
    }
    setLikedImages(newLiked);
  };

  // 格式化生成时间
  const formatGenerationTime = (time?: number) => {
    if (!time) return '未知';
    if (time < 1000) return `${time}ms`;
    return `${(time / 1000).toFixed(1)}s`;
  };

  // 获取模型显示名称
  const getModelDisplayName = (model: string) => {
    const modelNames: Record<string, string> = {
      'flux-schnell': 'FLUX Schnell',
      'flux-dev': 'FLUX Dev',
      'flux-pro': 'FLUX Pro',
      'sd-xl': 'Stable Diffusion XL',
      'sd-3': 'Stable Diffusion 3',
    };
    return modelNames[model] || model;
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">正在生成图像...</h3>
          <p className="text-sm text-gray-600">
            使用 {getModelDisplayName(metadata.model_used)} 模型生成中，请稍候
          </p>
        </div>
      </div>
    );
  }

  if (!images || images.length === 0) {
    return (
      <div className="bg-gray-50 rounded-lg border border-gray-200 p-6">
        <div className="text-center">
          <PhotoIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无图像</h3>
          <p className="text-sm text-gray-600">生成的图像将在这里显示</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
      {/* 图像网格 */}
      <div className="p-4">
        <div className={`grid gap-4 ${
          images.length === 1 ? 'grid-cols-1' : 
          images.length === 2 ? 'grid-cols-2' : 
          'grid-cols-2 md:grid-cols-3'
        }`}>
          {images.map((image, index) => (
            <div
              key={index}
              className="relative group cursor-pointer"
              onClick={() => setSelectedImage(index)}
            >
              <img
                src={image.url}
                alt={`Generated image ${index + 1}`}
                className="w-full h-auto rounded-lg shadow-sm hover:shadow-md transition-shadow"
                style={{ aspectRatio: `${image.width}/${image.height}` }}
              />
              
              {/* 悬停操作 */}
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
                <div className="flex space-x-2">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsFullscreen(true);
                    }}
                    className="p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors"
                    title="全屏查看"
                  >
                    <MagnifyingGlassIcon className="w-4 h-4 text-gray-700" />
                  </button>
                  
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      downloadImage(image.url, index);
                    }}
                    className="p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors"
                    title="下载图像"
                  >
                    <ArrowDownTrayIcon className="w-4 h-4 text-gray-700" />
                  </button>
                  
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleLike(index);
                    }}
                    className="p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors"
                    title="喜欢"
                  >
                    {likedImages.has(index) ? (
                      <HeartSolidIcon className="w-4 h-4 text-red-500" />
                    ) : (
                      <HeartIcon className="w-4 h-4 text-gray-700" />
                    )}
                  </button>
                </div>
              </div>
              
              {/* 选中指示器 */}
              {selectedImage === index && (
                <div className="absolute inset-0 border-2 border-blue-500 rounded-lg pointer-events-none"></div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* 元数据和操作 */}
      <div className="border-t border-gray-200 p-4 bg-gray-50">
        {/* 演示模式提示 */}
        {metadata.demo_notice && (
          <div className="mb-3 p-2 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-sm text-yellow-800">
              🎭 <strong>演示模式</strong>: 这是示例图像。配置 FAL API 密钥以生成真实图像。
              <a href="/FAL_SETUP.md" className="text-yellow-900 underline ml-1">查看配置指南</a>
            </p>
          </div>
        )}

        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-4 text-sm text-gray-600">
            <div className="flex items-center space-x-1">
              <CpuChipIcon className="w-4 h-4" />
              <span>{getModelDisplayName(metadata.model_used)}</span>
              {metadata.demo_notice && (
                <span className="text-xs bg-yellow-100 text-yellow-800 px-1 rounded">演示</span>
              )}
            </div>

            {metadata.generation_time && (
              <div className="flex items-center space-x-1">
                <ClockIcon className="w-4 h-4" />
                <span>{formatGenerationTime(metadata.generation_time)}</span>
              </div>
            )}

            {metadata.seed && (
              <div className="text-xs">
                Seed: {metadata.seed}
              </div>
            )}
          </div>
          
          <div className="flex space-x-2">
            <button
              onClick={() => shareImage(images[selectedImage].url)}
              className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors flex items-center space-x-1"
            >
              <ShareIcon className="w-4 h-4" />
              <span>分享</span>
            </button>
            
            {onRegenerate && (
              <button
                onClick={() => onRegenerate(metadata.seed)}
                className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              >
                重新生成
              </button>
            )}
          </div>
        </div>
        
        {/* 提示词显示 */}
        <div className="text-sm">
          <span className="font-medium text-gray-700">提示词: </span>
          <span className="text-gray-600">{metadata.prompt_used}</span>
        </div>
      </div>

      {/* 全屏模态框 */}
      {isFullscreen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4"
          onClick={() => setIsFullscreen(false)}
        >
          <div className="relative max-w-full max-h-full">
            <img
              src={images[selectedImage].url}
              alt="Fullscreen view"
              className="max-w-full max-h-full object-contain"
            />
            <button
              onClick={() => setIsFullscreen(false)}
              className="absolute top-4 right-4 text-white text-2xl hover:text-gray-300"
            >
              ✕
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
