'use client';

import React, { useState, useEffect } from 'react';
import { ChatSession } from '@/lib/types';
import { 
  ClockIcon, 
  TrashIcon, 
  ChatBubbleLeftRightIcon,
  MagnifyingGlassIcon 
} from '@heroicons/react/24/outline';

interface SessionHistoryProps {
  onLoadSession: (session: ChatSession) => void;
  currentSessionId: string;
}

export default function SessionHistory({ onLoadSession, currentSessionId }: SessionHistoryProps) {
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isOpen, setIsOpen] = useState(false);

  // 从localStorage加载历史会话
  useEffect(() => {
    const savedSessions = localStorage.getItem('chat-sessions');
    if (savedSessions) {
      try {
        const parsed = JSON.parse(savedSessions);
        setSessions(parsed.map((s: any) => ({
          ...s,
          createdAt: new Date(s.createdAt),
          updatedAt: new Date(s.updatedAt),
        })));
      } catch (error) {
        console.error('Failed to load sessions:', error);
      }
    }
  }, []);

  // 保存会话到localStorage
  const saveSession = (session: ChatSession) => {
    const updatedSessions = sessions.filter(s => s.id !== session.id);
    updatedSessions.unshift(session);
    
    // 只保留最近的20个会话
    const limitedSessions = updatedSessions.slice(0, 20);
    setSessions(limitedSessions);
    
    localStorage.setItem('chat-sessions', JSON.stringify(limitedSessions));
  };

  // 删除会话
  const deleteSession = (sessionId: string) => {
    const updatedSessions = sessions.filter(s => s.id !== sessionId);
    setSessions(updatedSessions);
    localStorage.setItem('chat-sessions', JSON.stringify(updatedSessions));
  };

  // 过滤会话
  const filteredSessions = sessions.filter(session => {
    if (!searchTerm) return true;
    
    const searchLower = searchTerm.toLowerCase();
    return session.messages.some(msg => 
      msg.content.toLowerCase().includes(searchLower)
    );
  });

  // 获取会话预览文本
  const getSessionPreview = (session: ChatSession): string => {
    const firstUserMessage = session.messages.find(m => m.role === 'user');
    return firstUserMessage?.content.slice(0, 50) + '...' || '新对话';
  };

  // 格式化时间
  const formatTime = (date: Date): string => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) {
      return date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } else if (days === 1) {
      return '昨天';
    } else if (days < 7) {
      return `${days}天前`;
    } else {
      return date.toLocaleDateString('zh-CN');
    }
  };

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="fixed left-4 top-1/2 transform -translate-y-1/2 bg-white shadow-lg rounded-lg p-3 hover:shadow-xl transition-shadow z-10"
        title="查看历史对话"
      >
        <ClockIcon className="w-5 h-5 text-gray-600" />
      </button>
    );
  }

  return (
    <div className="fixed left-0 top-0 h-full w-80 bg-white shadow-xl z-20 flex flex-col">
      {/* 头部 */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-gray-900">历史对话</h3>
          <button
            onClick={() => setIsOpen(false)}
            className="text-gray-500 hover:text-gray-700"
          >
            ✕
          </button>
        </div>
        
        {/* 搜索框 */}
        <div className="relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="搜索对话..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* 会话列表 */}
      <div className="flex-1 overflow-y-auto">
        {filteredSessions.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            <ChatBubbleLeftRightIcon className="w-12 h-12 mx-auto mb-2 text-gray-300" />
            <p>暂无历史对话</p>
          </div>
        ) : (
          <div className="p-2">
            {filteredSessions.map((session) => (
              <div
                key={session.id}
                className={`p-3 mb-2 rounded-lg cursor-pointer transition-colors ${
                  session.id === currentSessionId
                    ? 'bg-blue-50 border border-blue-200'
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => {
                  onLoadSession(session);
                  setIsOpen(false);
                }}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {getSessionPreview(session)}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {session.messages.length} 条消息
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      {formatTime(session.updatedAt)}
                    </p>
                  </div>
                  
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteSession(session.id);
                    }}
                    className="ml-2 p-1 text-gray-400 hover:text-red-500 transition-colors"
                    title="删除对话"
                  >
                    <TrashIcon className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 底部统计 */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <p className="text-xs text-gray-500 text-center">
          共 {sessions.length} 个历史对话
        </p>
      </div>
    </div>
  );

  // 暴露保存会话的方法给父组件使用
  React.useImperativeHandle(React.forwardRef(() => null), () => ({
    saveSession
  }));
}
