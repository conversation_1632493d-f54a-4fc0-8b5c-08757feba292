'use client';

import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { ChatState, ChatAction, Message, ChatSession } from '@/lib/types';

// 初始状态
const initialState: ChatState = {
  session: {
    id: '',
    messages: [],
    status: 'active',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  isLoading: false,
};

// Reducer函数
function chatReducer(state: ChatState, action: ChatAction): ChatState {
  switch (action.type) {
    case 'ADD_MESSAGE':
      return {
        ...state,
        session: {
          ...state.session,
          messages: [...state.session.messages, action.payload],
          updatedAt: new Date(),
        },
      };
    
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    
    case 'UPDATE_REQUIREMENT':
      return {
        ...state,
        currentRequirement: {
          ...state.currentRequirement,
          ...action.payload,
        },
      };
    
    case 'SET_OPTIMIZED_PROMPT':
      return {
        ...state,
        optimizedPrompt: action.payload,
      };
    
    case 'RESET_SESSION':
      return {
        ...initialState,
        session: {
          ...initialState.session,
          id: generateSessionId(),
        },
      };
    
    default:
      return state;
  }
}

// 生成会话ID
function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// 创建Context
const ChatContext = createContext<{
  state: ChatState;
  dispatch: React.Dispatch<ChatAction>;
  addMessage: (content: string, role: 'user' | 'assistant', metadata?: any) => void;
  setLoading: (loading: boolean) => void;
  resetSession: () => void;
} | null>(null);

// Provider组件
export function ChatProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(chatReducer, {
    ...initialState,
    session: {
      ...initialState.session,
      id: generateSessionId(),
    },
  });

  // 添加消息的辅助函数
  const addMessage = (content: string, role: 'user' | 'assistant', metadata?: any) => {
    const message: Message = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      role,
      content,
      timestamp: new Date(),
      metadata,
    };
    dispatch({ type: 'ADD_MESSAGE', payload: message });
  };

  // 设置加载状态的辅助函数
  const setLoading = (loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: loading });
  };

  // 重置会话的辅助函数
  const resetSession = () => {
    dispatch({ type: 'RESET_SESSION' });
  };

  const value = {
    state,
    dispatch,
    addMessage,
    setLoading,
    resetSession,
  };

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  );
}

// Hook for using the chat context
export function useChat() {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
}
