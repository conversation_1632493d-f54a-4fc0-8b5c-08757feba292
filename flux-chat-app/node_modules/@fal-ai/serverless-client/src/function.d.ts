import { FalStream, StreamingConnectionMode } from "./streaming";
import { CompletedQueueStatus, EnqueueResult, QueueStatus } from "./types";
/**
 * The function input and other configuration when running
 * the function, such as the HTTP method to use.
 */
type RunOptions<Input> = {
    /**
     * The path to the function, if any. Defaults to ``.
     * @deprecated Pass the path as part of the app id itself, e.g. `fal-ai/sdxl/image-to-image`
     */
    readonly path?: string;
    /**
     * The function input. It will be submitted either as query params
     * or the body payload, depending on the `method`.
     */
    readonly input?: Input;
    /**
     * The HTTP method, defaults to `post`;
     */
    readonly method?: "get" | "post" | "put" | "delete" | string;
    /**
     * If `true`, the function will automatically upload any files
     * (i.e. instances of `Blob`).
     *
     * This is enabled by default. You can disable it by setting it to `false`.
     */
    readonly autoUpload?: boolean;
};
type ExtraOptions = {
    /**
     * If `true`, the function will use the queue to run the function
     * asynchronously and return the result in a separate call. This
     * influences how the URL is built.
     */
    readonly subdomain?: string;
    /**
     * The query parameters to include in the URL.
     */
    readonly query?: Record<string, string>;
};
/**
 * Builds the final url to run the function based on its `id` or alias and
 * a the options from `RunOptions<Input>`.
 *
 * @private
 * @param id the function id or alias
 * @param options the run options
 * @returns the final url to run the function
 */
export declare function buildUrl<Input>(id: string, options?: RunOptions<Input> & ExtraOptions): string;
export declare function send<Input, Output>(id: string, options?: RunOptions<Input> & ExtraOptions): Promise<Output>;
export type QueueStatusSubscriptionOptions = QueueStatusOptions & Omit<QueueSubscribeOptions, "onEnqueue" | "webhookUrl">;
/**
 * Runs a fal serverless function identified by its `id`.
 *
 * @param id the registered function revision id or alias.
 * @returns the remote function output
 */
export declare function run<Input, Output>(id: string, options?: RunOptions<Input>): Promise<Output>;
/**
 * Options for subscribing to the request queue.
 */
type QueueSubscribeOptions = {
    /**
     * The mode to use for subscribing to updates. It defaults to `polling`.
     * You can also use client-side streaming by setting it to `streaming`.
     *
     * **Note:** Streaming is currently experimental and once stable, it will
     * be the default mode.
     *
     * @see pollInterval
     */
    mode?: "polling" | "streaming";
    /**
     * Callback function that is called when a request is enqueued.
     * @param requestId - The unique identifier for the enqueued request.
     */
    onEnqueue?: (requestId: string) => void;
    /**
     * Callback function that is called when the status of the queue changes.
     * @param status - The current status of the queue.
     */
    onQueueUpdate?: (status: QueueStatus) => void;
    /**
     * If `true`, the response will include the logs for the request.
     * Defaults to `false`.
     */
    logs?: boolean;
    /**
     * The timeout (in milliseconds) for the request. If the request is not
     * completed within this time, the subscription will be cancelled.
     *
     * Keep in mind that although the client resolves the function on a timeout,
     * and will try to cancel the request on the server, the server might not be
     * able to cancel the request if it's already running.
     *
     * Note: currently, the timeout is not enforced and the default is `undefined`.
     * This behavior might change in the future.
     */
    timeout?: number;
    /**
     * The URL to send a webhook notification to when the request is completed.
     * @see WebHookResponse
     */
    webhookUrl?: string;
} & ({
    mode?: "polling";
    /**
     * The interval (in milliseconds) at which to poll for updates.
     * If not provided, a default value of `500` will be used.
     *
     * This value is ignored if `mode` is set to `streaming`.
     */
    pollInterval?: number;
} | {
    mode: "streaming";
    /**
     * The connection mode to use for streaming updates. It defaults to `server`.
     * Set to `client` if your server proxy doesn't support streaming.
     */
    connectionMode?: StreamingConnectionMode;
});
/**
 * Options for submitting a request to the queue.
 */
type SubmitOptions<Input> = RunOptions<Input> & {
    /**
     * The URL to send a webhook notification to when the request is completed.
     * @see WebHookResponse
     */
    webhookUrl?: string;
};
type BaseQueueOptions = {
    /**
     * The unique identifier for the enqueued request.
     */
    requestId: string;
};
type QueueStatusOptions = BaseQueueOptions & {
    /**
     * If `true`, the response will include the logs for the request.
     * Defaults to `false`.
     */
    logs?: boolean;
};
type QueueStatusStreamOptions = QueueStatusOptions & {
    /**
     * The connection mode to use for streaming updates. It defaults to `server`.
     * Set to `client` if your server proxy doesn't support streaming.
     */
    connectionMode?: StreamingConnectionMode;
};
/**
 * Represents a request queue with methods for submitting requests,
 * checking their status, retrieving results, and subscribing to updates.
 */
interface Queue {
    /**
     * Submits a request to the queue.
     *
     * @param endpointId - The ID of the function web endpoint.
     * @param options - Options to configure how the request is run.
     * @returns A promise that resolves to the result of enqueuing the request.
     */
    submit<Input>(endpointId: string, options: SubmitOptions<Input>): Promise<EnqueueResult>;
    /**
     * Retrieves the status of a specific request in the queue.
     *
     * @param endpointId - The ID of the function web endpoint.
     * @param options - Options to configure how the request is run.
     * @returns A promise that resolves to the status of the request.
     */
    status(endpointId: string, options: QueueStatusOptions): Promise<QueueStatus>;
    /**
     * Subscribes to updates for a specific request in the queue using HTTP streaming events.
     *
     * @param endpointId - The ID of the function web endpoint.
     * @param options - Options to configure how the request is run and how updates are received.
     * @returns The streaming object that can be used to listen for updates.
     */
    streamStatus(endpointId: string, options: QueueStatusStreamOptions): Promise<FalStream<unknown, QueueStatus>>;
    /**
     * Subscribes to updates for a specific request in the queue using polling or streaming.
     * See `options.mode` for more details.
     *
     * @param endpointId - The ID of the function web endpoint.
     * @param options - Options to configure how the request is run and how updates are received.
     * @returns A promise that resolves to the final status of the request.
     */
    subscribeToStatus(endpointId: string, options: QueueStatusSubscriptionOptions): Promise<CompletedQueueStatus>;
    /**
     * Retrieves the result of a specific request from the queue.
     *
     * @param endpointId - The ID of the function web endpoint.
     * @param options - Options to configure how the request is run.
     * @returns A promise that resolves to the result of the request.
     */
    result<Output>(endpointId: string, options: BaseQueueOptions): Promise<Output>;
    /**
     * Cancels a request in the queue.
     *
     * @param endpointId - The ID of the function web endpoint.
     * @param options - Options to configure how the request
     * is run and how updates are received.
     * @returns A promise that resolves once the request is cancelled.
     * @throws {Error} If the request cannot be cancelled.
     */
    cancel(endpointId: string, options: BaseQueueOptions): Promise<void>;
}
/**
 * The fal run queue module. It allows to submit a function to the queue and get its result
 * on a separate call. This is useful for long running functions that can be executed
 * asynchronously and not .
 */
export declare const queue: Queue;
/**
 * Subscribes to updates for a specific request in the queue.
 *
 * @param endpointId - The ID of the function web endpoint.
 * @param options - Options to configure how the request is run and how updates are received.
 * @returns A promise that resolves to the result of the request once it's completed.
 */
export declare function subscribe<Input, Output>(endpointId: string, options?: RunOptions<Input> & QueueSubscribeOptions): Promise<Output>;
export {};
