"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TARGET_URL_HEADER = void 0;
exports.withMiddleware = withMiddleware;
exports.withProxy = withProxy;
/**
 * Setup a execution chain of middleware functions.
 *
 * @param middlewares one or more middleware functions.
 * @returns a middleware function that executes the given middlewares in order.
 */
function withMiddleware(...middlewares) {
    return (config) => middlewares.reduce((configPromise, middleware) => configPromise.then((req) => middleware(req)), Promise.resolve(config));
}
exports.TARGET_URL_HEADER = "x-fal-target-url";
function withProxy(config) {
    // when running on the server, we don't need to proxy the request
    if (typeof window === "undefined") {
        return (requestConfig) => Promise.resolve(requestConfig);
    }
    return (requestConfig) => Promise.resolve(Object.assign(Object.assign({}, requestConfig), { url: config.targetUrl, headers: Object.assign(Object.assign({}, (requestConfig.headers || {})), { [exports.TARGET_URL_HEADER]: requestConfig.url }) }));
}
//# sourceMappingURL=middleware.js.map