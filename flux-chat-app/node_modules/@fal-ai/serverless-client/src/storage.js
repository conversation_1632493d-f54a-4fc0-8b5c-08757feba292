"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.storageImpl = void 0;
const config_1 = require("./config");
const request_1 = require("./request");
const utils_1 = require("./utils");
/**
 * Get the file extension from the content type. This is used to generate
 * a file name if the file name is not provided.
 *
 * @param contentType the content type of the file.
 * @returns the file extension or `bin` if the content type is not recognized.
 */
function getExtensionFromContentType(contentType) {
    var _a;
    const [_, fileType] = contentType.split("/");
    return (_a = fileType.split(/[-;]/)[0]) !== null && _a !== void 0 ? _a : "bin";
}
/**
 * Initiate the upload of a file to the server. This returns the URL to upload
 * the file to and the URL of the file once it is uploaded.
 *
 * @param file the file to upload
 * @returns the URL to upload the file to and the URL of the file once it is uploaded.
 */
function initiateUpload(file) {
    return __awaiter(this, void 0, void 0, function* () {
        const contentType = file.type || "application/octet-stream";
        const filename = file.name || `${Date.now()}.${getExtensionFromContentType(contentType)}`;
        return yield (0, request_1.dispatchRequest)("POST", `${(0, config_1.getRestApiUrl)()}/storage/upload/initiate`, {
            content_type: contentType,
            file_name: filename,
        });
    });
}
exports.storageImpl = {
    upload: (file) => __awaiter(void 0, void 0, void 0, function* () {
        const { fetch } = (0, config_1.getConfig)();
        const { upload_url: uploadUrl, file_url: url } = yield initiateUpload(file);
        const response = yield fetch(uploadUrl, {
            method: "PUT",
            body: file,
            headers: {
                "Content-Type": file.type || "application/octet-stream",
            },
        });
        const { responseHandler } = (0, config_1.getConfig)();
        yield responseHandler(response);
        return url;
    }),
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    transformInput: (input) => __awaiter(void 0, void 0, void 0, function* () {
        if (Array.isArray(input)) {
            return Promise.all(input.map((item) => exports.storageImpl.transformInput(item)));
        }
        else if (input instanceof Blob) {
            return yield exports.storageImpl.upload(input);
        }
        else if ((0, utils_1.isPlainObject)(input)) {
            const inputObject = input;
            const promises = Object.entries(inputObject).map((_a) => __awaiter(void 0, [_a], void 0, function* ([key, value]) {
                return [key, yield exports.storageImpl.transformInput(value)];
            }));
            const results = yield Promise.all(promises);
            return Object.fromEntries(results);
        }
        // Return the input as is if it's neither an object nor a file/blob/data URI
        return input;
    }),
};
//# sourceMappingURL=storage.js.map