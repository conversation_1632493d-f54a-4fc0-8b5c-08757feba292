{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../../../libs/client/src/auth.ts"], "names": [], "mappings": ";;;;;;;;;;;;AASA,sDAgBC;AAzBD,qCAAyC;AACzC,uCAA4C;AAC5C,mCAAqC;AAExB,QAAA,wBAAwB,GAAG,GAAG,CAAC;AAE5C;;GAEG;AACH,SAAsB,qBAAqB,CAAC,GAAW;;QACrD,MAAM,KAAK,GAAG,IAAA,kBAAU,EAAC,GAAG,CAAC,CAAC;QAC9B,MAAM,KAAK,GAAoB,MAAM,IAAA,yBAAe,EAClD,MAAM,EACN,GAAG,IAAA,sBAAa,GAAE,UAAU,EAC5B;YACE,YAAY,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;YAC3B,gBAAgB,EAAE,gCAAwB;SAC3C,CACF,CAAC;QACF,iFAAiF;QACjF,yCAAyC;QACzC,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjD,OAAO,KAAK,CAAC,QAAQ,CAAC,CAAC;QACzB,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;CAAA", "sourcesContent": ["import { getRestApiUrl } from \"./config\";\nimport { dispatchRequest } from \"./request\";\nimport { parseAppId } from \"./utils\";\n\nexport const TOKEN_EXPIRATION_SECONDS = 120;\n\n/**\n * Get a token to connect to the realtime endpoint.\n */\nexport async function getTemporaryAuthToken(app: string): Promise<string> {\n  const appId = parseAppId(app);\n  const token: string | object = await dispatchRequest<any, string>(\n    \"POST\",\n    `${getRestApiUrl()}/tokens/`,\n    {\n      allowed_apps: [appId.alias],\n      token_expiration: TOKEN_EXPIRATION_SECONDS,\n    },\n  );\n  // keep this in case the response was wrapped (old versions of the proxy do that)\n  // should be safe to remove in the future\n  if (typeof token !== \"string\" && token[\"detail\"]) {\n    return token[\"detail\"];\n  }\n  return token;\n}\n"]}