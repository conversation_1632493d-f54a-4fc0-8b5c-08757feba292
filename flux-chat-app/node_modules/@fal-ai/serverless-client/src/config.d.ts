import { type RequestMiddleware } from "./middleware";
import type { <PERSON><PERSON><PERSON><PERSON> } from "./response";
export type CredentialsResolver = () => string | undefined;
type FetchType = typeof fetch;
export declare function resolveDefaultFetch(): FetchType;
export type Config = {
    /**
     * The credentials to use for the fal serverless client. When using the
     * client in the browser, it's recommended to use a proxy server to avoid
     * exposing the credentials in the client's environment.
     *
     * By default it tries to use the `FAL_KEY` environment variable, when
     * `process.env` is defined.
     *
     * @see https://fal.ai/docs/model-endpoints/server-side
     * @see #suppressLocalCredentialsWarning
     */
    credentials?: undefined | string | CredentialsResolver;
    /**
     * Suppresses the warning when the fal credentials are exposed in the
     * browser's environment. Make sure you understand the security implications
     * before enabling this option.
     */
    suppressLocalCredentialsWarning?: boolean;
    /**
     * The URL of the proxy server to use for the client requests. The proxy
     * server should forward the requests to the fal serverless rest api.
     */
    proxyUrl?: string;
    /**
     * The request middleware to use for the client requests. By default it
     * doesn't apply any middleware.
     */
    requestMiddleware?: RequestMiddleware;
    /**
     * The response handler to use for the client requests. By default it uses
     * a built-in response handler that returns the JSON response.
     */
    responseHandler?: ResponseHandler<any>;
    /**
     * The fetch implementation to use for the client requests. By default it uses
     * the global `fetch` function.
     */
    fetch?: FetchType;
};
export type RequiredConfig = Required<Config>;
export declare const credentialsFromEnv: CredentialsResolver;
/**
 * Configures the fal serverless client.
 *
 * @param config the new configuration.
 */
export declare function config(config: Config): void;
/**
 * Get the current fal serverless client configuration.
 *
 * @returns the current client configuration.
 */
export declare function getConfig(): RequiredConfig;
/**
 * @returns the URL of the fal serverless rest api endpoint.
 */
export declare function getRestApiUrl(): string;
export {};
