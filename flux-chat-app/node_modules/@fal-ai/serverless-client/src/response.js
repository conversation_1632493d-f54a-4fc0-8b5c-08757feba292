"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ValidationError = exports.ApiError = void 0;
exports.defaultResponseHandler = defaultResponseHandler;
class ApiError extends Error {
    constructor({ message, status, body }) {
        super(message);
        this.name = "ApiError";
        this.status = status;
        this.body = body;
    }
}
exports.ApiError = ApiError;
class ValidationError extends ApiError {
    constructor(args) {
        super(args);
        this.name = "ValidationError";
    }
    get fieldErrors() {
        // NOTE: this is a hack to support both FastAPI/Pydantic errors
        // and some custom 422 errors that might not be in the Pydantic format.
        if (typeof this.body.detail === "string") {
            return [
                {
                    loc: ["body"],
                    msg: this.body.detail,
                    type: "value_error",
                },
            ];
        }
        return this.body.detail || [];
    }
    getFieldErrors(field) {
        return this.fieldErrors.filter((error) => error.loc[error.loc.length - 1] === field);
    }
}
exports.ValidationError = ValidationError;
function defaultResponseHandler(response) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a;
        const { status, statusText } = response;
        const contentType = (_a = response.headers.get("Content-Type")) !== null && _a !== void 0 ? _a : "";
        if (!response.ok) {
            if (contentType.includes("application/json")) {
                const body = yield response.json();
                const ErrorType = status === 422 ? ValidationError : ApiError;
                throw new ErrorType({
                    message: body.message || statusText,
                    status,
                    body,
                });
            }
            throw new ApiError({ message: `HTTP ${status}: ${statusText}`, status });
        }
        if (contentType.includes("application/json")) {
            return response.json();
        }
        if (contentType.includes("text/html")) {
            return response.text();
        }
        if (contentType.includes("application/octet-stream")) {
            return response.arrayBuffer();
        }
        // TODO convert to either number or bool automatically
        return response.text();
    });
}
//# sourceMappingURL=response.js.map