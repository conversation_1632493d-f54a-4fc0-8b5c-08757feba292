{"version": 3, "file": "middleware.js", "sourceRoot": "", "sources": ["../../../../libs/client/src/middleware.ts"], "names": [], "mappings": ";;;AAyBA,wCASC;AAQD,8BAcC;AArCD;;;;;GAKG;AACH,SAAgB,cAAc,CAC5B,GAAG,WAAgC;IAEnC,OAAO,CAAC,MAAM,EAAE,EAAE,CAChB,WAAW,CAAC,MAAM,CAChB,CAAC,aAAa,EAAE,UAAU,EAAE,EAAE,CAC5B,aAAa,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAC9C,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CACxB,CAAC;AACN,CAAC;AAMY,QAAA,iBAAiB,GAAG,kBAAkB,CAAC;AAEpD,SAAgB,SAAS,CAAC,MAA0B;IAClD,iEAAiE;IACjE,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;QAClC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;IAC3D,CAAC;IACD,OAAO,CAAC,aAAa,EAAE,EAAE,CACvB,OAAO,CAAC,OAAO,iCACV,aAAa,KAChB,GAAG,EAAE,MAAM,CAAC,SAAS,EACrB,OAAO,kCACF,CAAC,aAAa,CAAC,OAAO,IAAI,EAAE,CAAC,KAChC,CAAC,yBAAiB,CAAC,EAAE,aAAa,CAAC,GAAG,OAExC,CAAC;AACP,CAAC", "sourcesContent": ["/**\n * A request configuration object.\n *\n * **Note:** This is a simplified version of the `RequestConfig` type from the\n * `fetch` API. It contains only the properties that are relevant for the\n * fal client. It also works around the fact that the `fetch` API `Request`\n * does not support mutability, its clone method has critical limitations\n * to our use case.\n */\nexport type RequestConfig = {\n  url: string;\n  method: string;\n  headers?: Record<string, string | string[]>;\n};\n\nexport type RequestMiddleware = (\n  request: RequestConfig,\n) => Promise<RequestConfig>;\n\n/**\n * Setup a execution chain of middleware functions.\n *\n * @param middlewares one or more middleware functions.\n * @returns a middleware function that executes the given middlewares in order.\n */\nexport function withMiddleware(\n  ...middlewares: RequestMiddleware[]\n): RequestMiddleware {\n  return (config) =>\n    middlewares.reduce(\n      (configPromise, middleware) =>\n        configPromise.then((req) => middleware(req)),\n      Promise.resolve(config),\n    );\n}\n\nexport type RequestProxyConfig = {\n  targetUrl: string;\n};\n\nexport const TARGET_URL_HEADER = \"x-fal-target-url\";\n\nexport function withProxy(config: RequestProxyConfig): RequestMiddleware {\n  // when running on the server, we don't need to proxy the request\n  if (typeof window === \"undefined\") {\n    return (requestConfig) => Promise.resolve(requestConfig);\n  }\n  return (requestConfig) =>\n    Promise.resolve({\n      ...requestConfig,\n      url: config.targetUrl,\n      headers: {\n        ...(requestConfig.headers || {}),\n        [TARGET_URL_HEADER]: requestConfig.url,\n      },\n    });\n}\n"]}