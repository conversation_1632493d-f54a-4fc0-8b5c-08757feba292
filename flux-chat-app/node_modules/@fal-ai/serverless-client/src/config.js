"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.credentialsFromEnv = void 0;
exports.resolveDefaultFetch = resolveDefaultFetch;
exports.config = config;
exports.getConfig = getConfig;
exports.getRestApiUrl = getRestApiUrl;
const middleware_1 = require("./middleware");
const response_1 = require("./response");
function resolveDefaultFetch() {
    if (typeof fetch === "undefined") {
        throw new Error("Your environment does not support fetch. Please provide your own fetch implementation.");
    }
    return fetch;
}
/**
 * Checks if the required FAL environment variables are set.
 *
 * @returns `true` if the required environment variables are set,
 * `false` otherwise.
 */
function hasEnvVariables() {
    return (typeof process !== "undefined" &&
        process.env &&
        (typeof process.env.FAL_KEY !== "undefined" ||
            (typeof process.env.FAL_KEY_ID !== "undefined" &&
                typeof process.env.FAL_KEY_SECRET !== "undefined")));
}
const credentialsFromEnv = () => {
    if (!hasEnvVariables()) {
        return undefined;
    }
    if (typeof process.env.FAL_KEY !== "undefined") {
        return process.env.FAL_KEY;
    }
    return `${process.env.FAL_KEY_ID}:${process.env.FAL_KEY_SECRET}`;
};
exports.credentialsFromEnv = credentialsFromEnv;
const DEFAULT_CONFIG = {
    credentials: exports.credentialsFromEnv,
    suppressLocalCredentialsWarning: false,
    requestMiddleware: (request) => Promise.resolve(request),
    responseHandler: response_1.defaultResponseHandler,
};
let configuration;
/**
 * Configures the fal serverless client.
 *
 * @param config the new configuration.
 */
function config(config) {
    var _a;
    configuration = Object.assign(Object.assign(Object.assign({}, DEFAULT_CONFIG), config), { fetch: (_a = config.fetch) !== null && _a !== void 0 ? _a : resolveDefaultFetch() });
    if (config.proxyUrl) {
        configuration = Object.assign(Object.assign({}, configuration), { requestMiddleware: (0, middleware_1.withMiddleware)((0, middleware_1.withProxy)({ targetUrl: config.proxyUrl }), configuration.requestMiddleware) });
    }
    const { credentials: resolveCredentials, suppressLocalCredentialsWarning } = configuration;
    const credentials = typeof resolveCredentials === "function"
        ? resolveCredentials()
        : resolveCredentials;
    if (typeof window !== "undefined" &&
        credentials &&
        !suppressLocalCredentialsWarning) {
        console.warn("The fal credentials are exposed in the browser's environment. " +
            "That's not recommended for production use cases.");
    }
}
/**
 * Get the current fal serverless client configuration.
 *
 * @returns the current client configuration.
 */
function getConfig() {
    if (!configuration) {
        console.info("Using default configuration for the fal client");
        return Object.assign(Object.assign({}, DEFAULT_CONFIG), { fetch: resolveDefaultFetch() });
    }
    return configuration;
}
/**
 * @returns the URL of the fal serverless rest api endpoint.
 */
function getRestApiUrl() {
    return "https://rest.alpha.fal.ai";
}
//# sourceMappingURL=config.js.map