{"version": 3, "file": "streaming.js", "sourceRoot": "", "sources": ["../../../../libs/client/src/streaming.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAuUA,wBAYC;AAnVD,2DAAkD;AAClD,iCAA+C;AAC/C,qCAAqC;AACrC,yCAAsC;AACtC,uCAA4C;AAC5C,yCAA8D;AAC9D,uCAAwC;AAIxC,MAAM,yBAAyB,GAAG,mBAAmB,CAAC;AAyDtD,MAAM,oBAAoB,GAAG,EAAE,GAAG,IAAI,CAAC;AAMvC;;GAEG;AACH,MAAa,SAAS;IAkBpB,YAAY,UAAkB,EAAE,OAA6B;;QAZ7D,8BAA8B;QACtB,cAAS,GAA4C,IAAI,GAAG,EAAE,CAAC;QAC/D,WAAM,GAAa,EAAE,CAAC;QAE9B,cAAc;QACN,gBAAW,GAAuB,SAAS,CAAC;QAC5C,uBAAkB,GAAG,CAAC,CAAC;QACvB,iBAAY,GAAG,KAAK,CAAC;QAGrB,oBAAe,GAAG,IAAI,eAAe,EAAE,CAAC;QAiCxC,UAAK,GAAG,GAAS,EAAE;;YACzB,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;YACrC,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,cAAc,GAAG,QAAQ,EAAE,GAAG,OAAO,CAAC;YACtE,IAAI,CAAC;gBACH,IAAI,cAAc,KAAK,QAAQ,EAAE,CAAC;oBAChC,6DAA6D;oBAC7D,8BAA8B;oBAC9B,MAAM,KAAK,GAAG,MAAM,IAAA,4BAAqB,EAAC,UAAU,CAAC,CAAC;oBACtD,MAAM,EAAE,KAAK,EAAE,GAAG,IAAA,kBAAS,GAAE,CAAC;oBAC9B,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACpC,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;oBACnD,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE;wBACjD,MAAM,EAAE,MAAM,CAAC,WAAW,EAAE;wBAC5B,OAAO,EAAE;4BACP,MAAM,EAAE,MAAA,OAAO,CAAC,MAAM,mCAAI,yBAAyB;4BACnD,cAAc,EAAE,kBAAkB;yBACnC;wBACD,IAAI,EAAE,KAAK,IAAI,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;wBACnE,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,MAAM;qBACpC,CAAC,CAAC;oBACH,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;gBAC7C,CAAC;gBACD,OAAO,MAAM,IAAA,yBAAe,EAAC,MAAM,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE;oBAClE,OAAO,EAAE;wBACP,MAAM,EAAE,MAAA,OAAO,CAAC,MAAM,mCAAI,yBAAyB;qBACpD;oBACD,eAAe,EAAE,IAAI,CAAC,cAAc;oBACpC,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,MAAM;iBACpC,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC,CAAA,CAAC;QAEM,mBAAc,GAAG,CAAO,QAAkB,EAAE,EAAE;;YACpD,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;gBACjB,IAAI,CAAC;oBACH,yDAAyD;oBACzD,wDAAwD;oBACxD,MAAM,IAAA,iCAAsB,EAAC,QAAQ,CAAC,CAAC;gBACzC,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;gBAC5B,CAAC;gBACD,OAAO;YACT,CAAC;YAED,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;YAC3B,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,IAAI,CAAC,IAAI,CACP,OAAO,EACP,IAAI,mBAAQ,CAAC;oBACX,OAAO,EAAE,yBAAyB;oBAClC,MAAM,EAAE,GAAG;oBACX,IAAI,EAAE,SAAS;iBAChB,CAAC,CACH,CAAC;gBACF,OAAO;YACT,CAAC;YAED,MAAM,aAAa,GAAG,QAAQ,CAAC,OAAO;iBACnC,GAAG,CAAC,cAAc,CAAC;iBACnB,UAAU,CAAC,yBAAyB,CAAC,CAAC;YACzC,kFAAkF;YAClF,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;gBAChC,MAAM,YAAY,GAAG,GAAG,EAAE;oBACxB,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;wBACrC,IAAI,IAAI,EAAE,CAAC;4BACT,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;4BACpC,OAAO;wBACT,CAAC;wBACD,IAAI,CAAC,WAAW,GAAG,KAAe,CAAC;wBACnC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;wBACzB,YAAY,EAAE,CAAC;oBACjB,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC;gBACF,YAAY,EAAE,CAAC;gBACf,OAAO;YACT,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;YACzC,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAEzC,MAAM,MAAM,GAAG,IAAA,iCAAY,EAAC,CAAC,KAAK,EAAE,EAAE;gBACpC,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;oBAC3B,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;oBAExB,IAAI,CAAC;wBACH,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBACpC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;wBAC7B,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;wBAC9B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;wBAE9B,iDAAiD;wBACjD,IAAI,CAAC,IAAI,CAAC,SAAgB,EAAE,UAAU,CAAC,CAAC;oBAC1C,CAAC;oBAAC,OAAO,CAAC,EAAE,CAAC;wBACX,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;oBACxB,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,MAAA,IAAI,CAAC,OAAO,CAAC,OAAO,mCAAI,oBAAoB,CAAC;YAE7D,MAAM,mBAAmB,GAAG,GAAS,EAAE;gBACrC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;gBAC5C,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAErC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;gBAEnC,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,kBAAkB,GAAG,OAAO,EAAE,CAAC;oBACnD,IAAI,CAAC,IAAI,CACP,OAAO,EACP,IAAI,mBAAQ,CAAC;wBACX,OAAO,EAAE,gCAAgC,CAAC,OAAO,GAAG,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,4BAA4B;wBAChG,MAAM,EAAE,GAAG;qBACZ,CAAC,CACH,CAAC;gBACJ,CAAC;gBAED,IAAI,CAAC,IAAI,EAAE,CAAC;oBACV,mBAAmB,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAChD,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;gBACtC,CAAC;YACH,CAAC,CAAA,CAAC;YAEF,mBAAmB,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC9C,OAAO;QACT,CAAC,CAAA,CAAC;QAEM,gBAAW,GAAG,CAAC,KAAU,EAAE,EAAE;;YACnC,MAAM,QAAQ,GACZ,KAAK,YAAY,mBAAQ;gBACvB,CAAC,CAAC,KAAK;gBACP,CAAC,CAAC,IAAI,mBAAQ,CAAC;oBACX,OAAO,EAAE,MAAA,KAAK,CAAC,OAAO,mCAAI,2BAA2B;oBACrD,MAAM,EAAE,GAAG;iBACZ,CAAC,CAAC;YACT,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YAC7B,OAAO;QACT,CAAC,CAAC;QAEK,OAAE,GAAG,CAAC,IAAwB,EAAE,QAAsB,EAAE,EAAE;;YAC/D,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC9B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YAC/B,CAAC;YACD,MAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,0CAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3C,CAAC,CAAC;QAEM,SAAI,GAAG,CAAC,IAAwB,EAAE,KAAU,EAAE,EAAE;YACtD,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACjD,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBACjC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAClB,CAAC;QACH,CAAC,CAAC;QAmBF;;;;;;;;;WASG;QACI,SAAI,GAAG,GAAS,EAAE,gDAAC,OAAA,IAAI,CAAC,WAAW,CAAA,GAAA,CAAC;QAE3C;;WAEG;QACI,UAAK,GAAG,GAAG,EAAE;YAClB,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAC/B,CAAC,CAAC;QA5NA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,GAAG;YACN,MAAA,OAAO,CAAC,GAAG,mCACX,IAAA,mBAAQ,EAAC,UAAU,EAAE;gBACnB,IAAI,EAAE,SAAS;gBACf,KAAK,EAAE,OAAO,CAAC,WAAW;aAC3B,CAAC,CAAC;QACL,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,WAAW,GAAG,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACzD,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,MAAM,CACJ,IAAI,mBAAQ,CAAC;oBACX,OAAO,EAAE,yCAAyC;oBAClD,MAAM,EAAE,GAAG;oBACX,IAAI,EAAE,SAAS;iBAChB,CAAC,CACH,CAAC;YACJ,CAAC;YACD,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;gBACvB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;gBACzB,OAAO,CAAC,IAAI,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBACzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;gBACzB,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACvC,CAAC;IA8JM,CAAC,MAAM,CAAC,aAAa,CAAC;;YAC3B,IAAI,OAAO,GAAG,IAAI,CAAC;YACnB,MAAM,iBAAiB,GAAG,GAAG,EAAE,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC,CAAC;YAClD,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;YACpC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAC;YACnC,OAAO,OAAO,EAAE,CAAC;gBACf,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;gBACjC,IAAI,IAAI,EAAE,CAAC;oBACT,oBAAM,IAAI,CAAA,CAAC;gBACb,CAAC;gBAED,+DAA+D;gBAC/D,uCAAuC;gBACvC,cAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAA,CAAC;YAC1D,CAAC;QACH,CAAC;KAAA;CAoBF;AAhPD,8BAgPC;AAED;;;;;;;;GAQG;AACH,SAAsB,MAAM,CAC1B,UAAkB,EAClB,OAA6B;;QAE7B,MAAM,KAAK,GACT,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK;YAC3C,CAAC,CAAC,MAAM,qBAAW,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC;YACjD,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC;QACpB,OAAO,IAAI,SAAS,CAAgB,UAAU,kCACzC,OAAO,KACV,KAAK,EAAE,KAAc,IACrB,CAAC;IACL,CAAC;CAAA", "sourcesContent": ["import { createParser } from \"eventsource-parser\";\nimport { getTemporaryAuthToken } from \"./auth\";\nimport { getConfig } from \"./config\";\nimport { buildUrl } from \"./function\";\nimport { dispatchRequest } from \"./request\";\nimport { ApiError, default<PERSON><PERSON>po<PERSON><PERSON><PERSON><PERSON> } from \"./response\";\nimport { storageImpl } from \"./storage\";\n\nexport type StreamingConnectionMode = \"client\" | \"server\";\n\nconst CONTENT_TYPE_EVENT_STREAM = \"text/event-stream\";\n\n/**\n * The stream API options. It requires the API input and also\n * offers configuration options.\n */\ntype StreamOptions<Input> = {\n  /**\n   * The endpoint URL. If not provided, it will be generated from the\n   * `endpointId` and the `queryParams`.\n   */\n  readonly url?: string;\n\n  /**\n   * The API input payload.\n   */\n  readonly input?: Input;\n\n  /**\n   * The query parameters to be sent with the request.\n   */\n  readonly queryParams?: Record<string, string>;\n\n  /**\n   * The maximum time interval in milliseconds between stream chunks. Defaults to 15s.\n   */\n  readonly timeout?: number;\n\n  /**\n   * Whether it should auto-upload File-like types to fal's storage\n   * or not.\n   */\n  readonly autoUpload?: boolean;\n\n  /**\n   * The HTTP method, defaults to `post`;\n   */\n  readonly method?: \"get\" | \"post\" | \"put\" | \"delete\" | string;\n\n  /**\n   * The content type the client accepts as response.\n   * By default this is set to `text/event-stream`.\n   */\n  readonly accept?: string;\n\n  /**\n   * The streaming connection mode. This is used to determine\n   * whether the streaming will be done from the browser itself (client)\n   * or through your own server, either when running on NodeJS or when\n   * using a proxy that supports streaming.\n   *\n   * It defaults to `server`. Set to `client` if your server proxy doesn't\n   * support streaming.\n   */\n  readonly connectionMode?: StreamingConnectionMode;\n};\n\nconst EVENT_STREAM_TIMEOUT = 15 * 1000;\n\ntype FalStreamEventType = \"data\" | \"error\" | \"done\";\n\ntype EventHandler<T = any> = (event: T) => void;\n\n/**\n * The class representing a streaming response. With t\n */\nexport class FalStream<Input, Output> {\n  // properties\n  endpointId: string;\n  url: string;\n  options: StreamOptions<Input>;\n\n  // support for event listeners\n  private listeners: Map<FalStreamEventType, EventHandler[]> = new Map();\n  private buffer: Output[] = [];\n\n  // local state\n  private currentData: Output | undefined = undefined;\n  private lastEventTimestamp = 0;\n  private streamClosed = false;\n  private donePromise: Promise<Output>;\n\n  private abortController = new AbortController();\n\n  constructor(endpointId: string, options: StreamOptions<Input>) {\n    this.endpointId = endpointId;\n    this.url =\n      options.url ??\n      buildUrl(endpointId, {\n        path: \"/stream\",\n        query: options.queryParams,\n      });\n    this.options = options;\n    this.donePromise = new Promise<Output>((resolve, reject) => {\n      if (this.streamClosed) {\n        reject(\n          new ApiError({\n            message: \"Streaming connection is already closed.\",\n            status: 400,\n            body: undefined,\n          }),\n        );\n      }\n      this.on(\"done\", (data) => {\n        this.streamClosed = true;\n        resolve(data);\n      });\n      this.on(\"error\", (error) => {\n        this.streamClosed = true;\n        reject(error);\n      });\n    });\n    this.start().catch(this.handleError);\n  }\n\n  private start = async () => {\n    const { endpointId, options } = this;\n    const { input, method = \"post\", connectionMode = \"server\" } = options;\n    try {\n      if (connectionMode === \"client\") {\n        // if we are in the browser, we need to get a temporary token\n        // to authenticate the request\n        const token = await getTemporaryAuthToken(endpointId);\n        const { fetch } = getConfig();\n        const parsedUrl = new URL(this.url);\n        parsedUrl.searchParams.set(\"fal_jwt_token\", token);\n        const response = await fetch(parsedUrl.toString(), {\n          method: method.toUpperCase(),\n          headers: {\n            accept: options.accept ?? CONTENT_TYPE_EVENT_STREAM,\n            \"content-type\": \"application/json\",\n          },\n          body: input && method !== \"get\" ? JSON.stringify(input) : undefined,\n          signal: this.abortController.signal,\n        });\n        return await this.handleResponse(response);\n      }\n      return await dispatchRequest(method.toUpperCase(), this.url, input, {\n        headers: {\n          accept: options.accept ?? CONTENT_TYPE_EVENT_STREAM,\n        },\n        responseHandler: this.handleResponse,\n        signal: this.abortController.signal,\n      });\n    } catch (error) {\n      this.handleError(error);\n    }\n  };\n\n  private handleResponse = async (response: Response) => {\n    if (!response.ok) {\n      try {\n        // we know the response failed, call the response handler\n        // so the exception gets converted to ApiError correctly\n        await defaultResponseHandler(response);\n      } catch (error) {\n        this.emit(\"error\", error);\n      }\n      return;\n    }\n\n    const body = response.body;\n    if (!body) {\n      this.emit(\n        \"error\",\n        new ApiError({\n          message: \"Response body is empty.\",\n          status: 400,\n          body: undefined,\n        }),\n      );\n      return;\n    }\n\n    const isEventStream = response.headers\n      .get(\"content-type\")\n      .startsWith(CONTENT_TYPE_EVENT_STREAM);\n    // any response that is not a text/event-stream will be handled as a binary stream\n    if (!isEventStream) {\n      const reader = body.getReader();\n      const emitRawChunk = () => {\n        reader.read().then(({ done, value }) => {\n          if (done) {\n            this.emit(\"done\", this.currentData);\n            return;\n          }\n          this.currentData = value as Output;\n          this.emit(\"data\", value);\n          emitRawChunk();\n        });\n      };\n      emitRawChunk();\n      return;\n    }\n\n    const decoder = new TextDecoder(\"utf-8\");\n    const reader = response.body.getReader();\n\n    const parser = createParser((event) => {\n      if (event.type === \"event\") {\n        const data = event.data;\n\n        try {\n          const parsedData = JSON.parse(data);\n          this.buffer.push(parsedData);\n          this.currentData = parsedData;\n          this.emit(\"data\", parsedData);\n\n          // also emit 'message'for backwards compatibility\n          this.emit(\"message\" as any, parsedData);\n        } catch (e) {\n          this.emit(\"error\", e);\n        }\n      }\n    });\n\n    const timeout = this.options.timeout ?? EVENT_STREAM_TIMEOUT;\n\n    const readPartialResponse = async () => {\n      const { value, done } = await reader.read();\n      this.lastEventTimestamp = Date.now();\n\n      parser.feed(decoder.decode(value));\n\n      if (Date.now() - this.lastEventTimestamp > timeout) {\n        this.emit(\n          \"error\",\n          new ApiError({\n            message: `Event stream timed out after ${(timeout / 1000).toFixed(0)} seconds with no messages.`,\n            status: 408,\n          }),\n        );\n      }\n\n      if (!done) {\n        readPartialResponse().catch(this.handleError);\n      } else {\n        this.emit(\"done\", this.currentData);\n      }\n    };\n\n    readPartialResponse().catch(this.handleError);\n    return;\n  };\n\n  private handleError = (error: any) => {\n    const apiError =\n      error instanceof ApiError\n        ? error\n        : new ApiError({\n            message: error.message ?? \"An unknown error occurred\",\n            status: 500,\n          });\n    this.emit(\"error\", apiError);\n    return;\n  };\n\n  public on = (type: FalStreamEventType, listener: EventHandler) => {\n    if (!this.listeners.has(type)) {\n      this.listeners.set(type, []);\n    }\n    this.listeners.get(type)?.push(listener);\n  };\n\n  private emit = (type: FalStreamEventType, event: any) => {\n    const listeners = this.listeners.get(type) || [];\n    for (const listener of listeners) {\n      listener(event);\n    }\n  };\n\n  async *[Symbol.asyncIterator]() {\n    let running = true;\n    const stopAsyncIterator = () => (running = false);\n    this.on(\"error\", stopAsyncIterator);\n    this.on(\"done\", stopAsyncIterator);\n    while (running) {\n      const data = this.buffer.shift();\n      if (data) {\n        yield data;\n      }\n\n      // the short timeout ensures the while loop doesn't block other\n      // frames getting executed concurrently\n      await new Promise((resolve) => setTimeout(resolve, 16));\n    }\n  }\n\n  /**\n   * Gets a reference to the `Promise` that indicates whether the streaming\n   * is done or not. Developers should always call this in their apps to ensure\n   * the request is over.\n   *\n   * An alternative to this, is to use `on('done')` in case your application\n   * architecture works best with event listeners.\n   *\n   * @returns the promise that resolves when the request is done.\n   */\n  public done = async () => this.donePromise;\n\n  /**\n   * Aborts the streaming request.\n   */\n  public abort = () => {\n    this.abortController.abort();\n  };\n}\n\n/**\n * Calls a fal app that supports streaming and provides a streaming-capable\n * object as a result, that can be used to get partial results through either\n * `AsyncIterator` or through an event listener.\n *\n * @param endpointId the endpoint id, e.g. `fal-ai/llavav15-13b`.\n * @param options the request options, including the input payload.\n * @returns the `FalStream` instance.\n */\nexport async function stream<Input = Record<string, any>, Output = any>(\n  endpointId: string,\n  options: StreamOptions<Input>,\n): Promise<FalStream<Input, Output>> {\n  const input =\n    options.input && options.autoUpload !== false\n      ? await storageImpl.transformInput(options.input)\n      : options.input;\n  return new FalStream<Input, Output>(endpointId, {\n    ...options,\n    input: input as Input,\n  });\n}\n"]}