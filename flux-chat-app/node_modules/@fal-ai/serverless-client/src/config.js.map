{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../../../../libs/client/src/config.ts"], "names": [], "mappings": ";;;AAYA,kDAOC;AAuFD,wBA+BC;AAOD,8BASC;AAKD,sCAEC;AAhKD,6CAIsB;AAEtB,yCAAoD;AAMpD,SAAgB,mBAAmB;IACjC,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE,CAAC;QACjC,MAAM,IAAI,KAAK,CACb,wFAAwF,CACzF,CAAC;IACJ,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AA6CD;;;;;GAKG;AACH,SAAS,eAAe;IACtB,OAAO,CACL,OAAO,OAAO,KAAK,WAAW;QAC9B,OAAO,CAAC,GAAG;QACX,CAAC,OAAO,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,WAAW;YACzC,CAAC,OAAO,OAAO,CAAC,GAAG,CAAC,UAAU,KAAK,WAAW;gBAC5C,OAAO,OAAO,CAAC,GAAG,CAAC,cAAc,KAAK,WAAW,CAAC,CAAC,CACxD,CAAC;AACJ,CAAC;AAEM,MAAM,kBAAkB,GAAwB,GAAG,EAAE;IAC1D,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC;QACvB,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,IAAI,OAAO,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,WAAW,EAAE,CAAC;QAC/C,OAAO,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC;IAC7B,CAAC;IAED,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC;AACnE,CAAC,CAAC;AAVW,QAAA,kBAAkB,sBAU7B;AAEF,MAAM,cAAc,GAAoB;IACtC,WAAW,EAAE,0BAAkB;IAC/B,+BAA+B,EAAE,KAAK;IACtC,iBAAiB,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;IACxD,eAAe,EAAE,iCAAsB;CACxC,CAAC;AAEF,IAAI,aAA6B,CAAC;AAElC;;;;GAIG;AACH,SAAgB,MAAM,CAAC,MAAc;;IACnC,aAAa,GAAG,8CACX,cAAc,GACd,MAAM,KACT,KAAK,EAAE,MAAA,MAAM,CAAC,KAAK,mCAAI,mBAAmB,EAAE,GAC3B,CAAC;IACpB,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;QACpB,aAAa,mCACR,aAAa,KAChB,iBAAiB,EAAE,IAAA,2BAAc,EAC/B,IAAA,sBAAS,EAAC,EAAE,SAAS,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,EACzC,aAAa,CAAC,iBAAiB,CAChC,GACF,CAAC;IACJ,CAAC;IACD,MAAM,EAAE,WAAW,EAAE,kBAAkB,EAAE,+BAA+B,EAAE,GACxE,aAAa,CAAC;IAChB,MAAM,WAAW,GACf,OAAO,kBAAkB,KAAK,UAAU;QACtC,CAAC,CAAC,kBAAkB,EAAE;QACtB,CAAC,CAAC,kBAAkB,CAAC;IACzB,IACE,OAAO,MAAM,KAAK,WAAW;QAC7B,WAAW;QACX,CAAC,+BAA+B,EAChC,CAAC;QACD,OAAO,CAAC,IAAI,CACV,gEAAgE;YAC9D,kDAAkD,CACrD,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;;;GAIG;AACH,SAAgB,SAAS;IACvB,IAAI,CAAC,aAAa,EAAE,CAAC;QACnB,OAAO,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC;QAC/D,OAAO,gCACF,cAAc,KACjB,KAAK,EAAE,mBAAmB,EAAE,GACX,CAAC;IACtB,CAAC;IACD,OAAO,aAAa,CAAC;AACvB,CAAC;AAED;;GAEG;AACH,SAAgB,aAAa;IAC3B,OAAO,2BAA2B,CAAC;AACrC,CAAC", "sourcesContent": ["import {\n  withMiddleware,\n  withProxy,\n  type RequestMiddleware,\n} from \"./middleware\";\nimport type { <PERSON><PERSON>and<PERSON> } from \"./response\";\nimport { defaultResponseHandler } from \"./response\";\n\nexport type CredentialsResolver = () => string | undefined;\n\ntype FetchType = typeof fetch;\n\nexport function resolveDefaultFetch(): FetchType {\n  if (typeof fetch === \"undefined\") {\n    throw new Error(\n      \"Your environment does not support fetch. Please provide your own fetch implementation.\",\n    );\n  }\n  return fetch;\n}\n\nexport type Config = {\n  /**\n   * The credentials to use for the fal serverless client. When using the\n   * client in the browser, it's recommended to use a proxy server to avoid\n   * exposing the credentials in the client's environment.\n   *\n   * By default it tries to use the `FAL_KEY` environment variable, when\n   * `process.env` is defined.\n   *\n   * @see https://fal.ai/docs/model-endpoints/server-side\n   * @see #suppressLocalCredentialsWarning\n   */\n  credentials?: undefined | string | CredentialsResolver;\n  /**\n   * Suppresses the warning when the fal credentials are exposed in the\n   * browser's environment. Make sure you understand the security implications\n   * before enabling this option.\n   */\n  suppressLocalCredentialsWarning?: boolean;\n  /**\n   * The URL of the proxy server to use for the client requests. The proxy\n   * server should forward the requests to the fal serverless rest api.\n   */\n  proxyUrl?: string;\n  /**\n   * The request middleware to use for the client requests. By default it\n   * doesn't apply any middleware.\n   */\n  requestMiddleware?: RequestMiddleware;\n  /**\n   * The response handler to use for the client requests. By default it uses\n   * a built-in response handler that returns the JSON response.\n   */\n  responseHandler?: ResponseHandler<any>;\n  /**\n   * The fetch implementation to use for the client requests. By default it uses\n   * the global `fetch` function.\n   */\n  fetch?: FetchType;\n};\n\nexport type RequiredConfig = Required<Config>;\n\n/**\n * Checks if the required FAL environment variables are set.\n *\n * @returns `true` if the required environment variables are set,\n * `false` otherwise.\n */\nfunction hasEnvVariables(): boolean {\n  return (\n    typeof process !== \"undefined\" &&\n    process.env &&\n    (typeof process.env.FAL_KEY !== \"undefined\" ||\n      (typeof process.env.FAL_KEY_ID !== \"undefined\" &&\n        typeof process.env.FAL_KEY_SECRET !== \"undefined\"))\n  );\n}\n\nexport const credentialsFromEnv: CredentialsResolver = () => {\n  if (!hasEnvVariables()) {\n    return undefined;\n  }\n\n  if (typeof process.env.FAL_KEY !== \"undefined\") {\n    return process.env.FAL_KEY;\n  }\n\n  return `${process.env.FAL_KEY_ID}:${process.env.FAL_KEY_SECRET}`;\n};\n\nconst DEFAULT_CONFIG: Partial<Config> = {\n  credentials: credentialsFromEnv,\n  suppressLocalCredentialsWarning: false,\n  requestMiddleware: (request) => Promise.resolve(request),\n  responseHandler: defaultResponseHandler,\n};\n\nlet configuration: RequiredConfig;\n\n/**\n * Configures the fal serverless client.\n *\n * @param config the new configuration.\n */\nexport function config(config: Config) {\n  configuration = {\n    ...DEFAULT_CONFIG,\n    ...config,\n    fetch: config.fetch ?? resolveDefaultFetch(),\n  } as RequiredConfig;\n  if (config.proxyUrl) {\n    configuration = {\n      ...configuration,\n      requestMiddleware: withMiddleware(\n        withProxy({ targetUrl: config.proxyUrl }),\n        configuration.requestMiddleware,\n      ),\n    };\n  }\n  const { credentials: resolveCredentials, suppressLocalCredentialsWarning } =\n    configuration;\n  const credentials =\n    typeof resolveCredentials === \"function\"\n      ? resolveCredentials()\n      : resolveCredentials;\n  if (\n    typeof window !== \"undefined\" &&\n    credentials &&\n    !suppressLocalCredentialsWarning\n  ) {\n    console.warn(\n      \"The fal credentials are exposed in the browser's environment. \" +\n        \"That's not recommended for production use cases.\",\n    );\n  }\n}\n\n/**\n * Get the current fal serverless client configuration.\n *\n * @returns the current client configuration.\n */\nexport function getConfig(): RequiredConfig {\n  if (!configuration) {\n    console.info(\"Using default configuration for the fal client\");\n    return {\n      ...DEFAULT_CONFIG,\n      fetch: resolveDefaultFetch(),\n    } as RequiredConfig;\n  }\n  return configuration;\n}\n\n/**\n * @returns the URL of the fal serverless rest api endpoint.\n */\nexport function getRestApiUrl(): string {\n  return \"https://rest.alpha.fal.ai\";\n}\n"]}