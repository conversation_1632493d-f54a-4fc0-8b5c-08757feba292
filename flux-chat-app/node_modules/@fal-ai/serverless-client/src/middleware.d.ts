/**
 * A request configuration object.
 *
 * **Note:** This is a simplified version of the `RequestConfig` type from the
 * `fetch` API. It contains only the properties that are relevant for the
 * fal client. It also works around the fact that the `fetch` API `Request`
 * does not support mutability, its clone method has critical limitations
 * to our use case.
 */
export type RequestConfig = {
    url: string;
    method: string;
    headers?: Record<string, string | string[]>;
};
export type RequestMiddleware = (request: RequestConfig) => Promise<RequestConfig>;
/**
 * Setup a execution chain of middleware functions.
 *
 * @param middlewares one or more middleware functions.
 * @returns a middleware function that executes the given middlewares in order.
 */
export declare function withMiddleware(...middlewares: RequestMiddleware[]): RequestMiddleware;
export type RequestProxyConfig = {
    targetUrl: string;
};
export declare const TARGET_URL_HEADER = "x-fal-target-url";
export declare function withProxy(config: RequestProxyConfig): RequestMiddleware;
