{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../../libs/client/src/types.ts"], "names": [], "mappings": ";;AA+CA,sCAEC;AAED,wDAEC;AAND,SAAgB,aAAa,CAAC,GAAQ;IACpC,OAAO,GAAG,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,YAAY,CAAC;AAC/C,CAAC;AAED,SAAgB,sBAAsB,CAAC,GAAQ;IAC7C,OAAO,aAAa,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,KAAK,WAAW,CAAC;AAC1D,CAAC", "sourcesContent": ["export type Result<T> = {\n  result: T;\n};\n\nexport type EnqueueResult = {\n  request_id: string;\n};\n\nexport type RequestLog = {\n  message: string;\n  level: \"STDERR\" | \"STDOUT\" | \"ERROR\" | \"INFO\" | \"WARN\" | \"DEBUG\";\n  source: \"USER\";\n  timestamp: string; // Using string to represent date-time format, but you could also use 'Date' type if you're going to construct Date objects.\n};\n\nexport type Metrics = {\n  inference_time: number | null;\n};\n\ninterface BaseQueueStatus {\n  status: \"IN_PROGRESS\" | \"COMPLETED\" | \"IN_QUEUE\";\n}\n\nexport interface InProgressQueueStatus extends BaseQueueStatus {\n  status: \"IN_PROGRESS\";\n  response_url: string;\n  logs: RequestLog[];\n}\n\nexport interface CompletedQueueStatus extends BaseQueueStatus {\n  status: \"COMPLETED\";\n  response_url: string;\n  logs: RequestLog[];\n  metrics: Metrics;\n}\n\nexport interface EnqueuedQueueStatus extends BaseQueueStatus {\n  status: \"IN_QUEUE\";\n  queue_position: number;\n  response_url: string;\n}\n\nexport type QueueStatus =\n  | InProgressQueueStatus\n  | CompletedQueueStatus\n  | EnqueuedQueueStatus;\n\nexport function isQueueStatus(obj: any): obj is QueueStatus {\n  return obj && obj.status && obj.response_url;\n}\n\nexport function isCompletedQueueStatus(obj: any): obj is CompletedQueueStatus {\n  return isQueueStatus(obj) && obj.status === \"COMPLETED\";\n}\n\nexport type ValidationErrorInfo = {\n  msg: string;\n  loc: Array<string | number>;\n  type: string;\n};\n\n/**\n * Represents the response from a WebHook request.\n * This is a union type that varies based on the `status` property.\n *\n * @template Payload - The type of the payload in the response. It defaults to `any`,\n * allowing for flexibility in specifying the structure of the payload.\n */\nexport type WebHookResponse<Payload = any> =\n  | {\n      /** Indicates a successful response. */\n      status: \"OK\";\n      /** The payload of the response, structure determined by the Payload type. */\n      payload: Payload;\n      /** Error is never present in a successful response. */\n      error: never;\n      /** The unique identifier for the request. */\n      request_id: string;\n    }\n  | {\n      /** Indicates an unsuccessful response. */\n      status: \"ERROR\";\n      /** The payload of the response, structure determined by the Payload type. */\n      payload: Payload;\n      /** Description of the error that occurred. */\n      error: string;\n      /** The unique identifier for the request. */\n      request_id: string;\n    };\n"]}