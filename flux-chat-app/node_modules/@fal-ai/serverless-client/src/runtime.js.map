{"version": 3, "file": "runtime.js", "sourceRoot": "", "sources": ["../../../../libs/client/src/runtime.ts"], "names": [], "mappings": ";AAAA,uDAAuD;;AAEvD,8BAIC;AAID,oCAOC;AAfD,SAAgB,SAAS;IACvB,OAAO,CACL,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC,QAAQ,KAAK,WAAW,CACxE,CAAC;AACJ,CAAC;AAED,IAAI,iBAAiB,GAAkB,IAAI,CAAC;AAE5C,SAAgB,YAAY;IAC1B,IAAI,iBAAiB,KAAK,IAAI,EAAE,CAAC;QAC/B,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IACD,MAAM,WAAW,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC;IAC/C,iBAAiB,GAAG,GAAG,WAAW,CAAC,IAAI,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;IACjE,OAAO,iBAAiB,CAAC;AAC3B,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-var-requires */\n\nexport function isBrowser(): boolean {\n  return (\n    typeof window !== \"undefined\" && typeof window.document !== \"undefined\"\n  );\n}\n\nlet memoizedUserAgent: string | null = null;\n\nexport function getUserAgent(): string {\n  if (memoizedUserAgent !== null) {\n    return memoizedUserAgent;\n  }\n  const packageInfo = require(\"../package.json\");\n  memoizedUserAgent = `${packageInfo.name}/${packageInfo.version}`;\n  return memoizedUserAgent;\n}\n"]}