"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.dispatchRequest = dispatchRequest;
const config_1 = require("./config");
const runtime_1 = require("./runtime");
const isCloudflareWorkers = typeof navigator !== "undefined" &&
    (navigator === null || navigator === void 0 ? void 0 : navigator.userAgent) === "Cloudflare-Workers";
function dispatchRequest(method_1, targetUrl_1, input_1) {
    return __awaiter(this, arguments, void 0, function* (method, targetUrl, input, options = {}) {
        var _a;
        const { credentials: credentialsValue, requestMiddleware, responseHandler, fetch, } = (0, config_1.getConfig)();
        const userAgent = (0, runtime_1.isBrowser)() ? {} : { "User-Agent": (0, runtime_1.getUserAgent)() };
        const credentials = typeof credentialsValue === "function"
            ? credentialsValue()
            : credentialsValue;
        const { url, headers } = yield requestMiddleware({
            url: targetUrl,
            method: method.toUpperCase(),
        });
        const authHeader = credentials ? { Authorization: `Key ${credentials}` } : {};
        const requestHeaders = Object.assign(Object.assign(Object.assign(Object.assign({}, authHeader), { Accept: "application/json", "Content-Type": "application/json" }), userAgent), (headers !== null && headers !== void 0 ? headers : {}));
        const { responseHandler: customResponseHandler } = options, requestInit = __rest(options, ["responseHandler"]);
        const response = yield fetch(url, Object.assign(Object.assign(Object.assign(Object.assign({}, requestInit), { method, headers: Object.assign(Object.assign({}, requestHeaders), ((_a = requestInit.headers) !== null && _a !== void 0 ? _a : {})) }), (!isCloudflareWorkers && { mode: "cors" })), { body: method.toLowerCase() !== "get" && input
                ? JSON.stringify(input)
                : undefined }));
        const handleResponse = customResponseHandler !== null && customResponseHandler !== void 0 ? customResponseHandler : responseHandler;
        return yield handleResponse(response);
    });
}
//# sourceMappingURL=request.js.map