{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../libs/client/src/index.ts"], "names": [], "mappings": ";;;AAAA,mCAA6C;AAApC,gGAAA,MAAM,OAAA;AAAE,mGAAA,SAAS,OAAA;AAC1B,uCAAmD;AAA1C,iGAAA,KAAK,OAAA;AAAE,+FAAA,GAAG,OAAA;AAAE,qGAAA,SAAS,OAAA;AAC9B,2CAAyD;AAAhD,4GAAA,cAAc,OAAA;AAAE,uGAAA,SAAS,OAAA;AAElC,uCAAsD;AAA7C,oGAAA,YAAY,OAAY;AACjC,uCAAuD;AAA9C,oGAAA,QAAQ,OAAA;AAAE,2GAAA,eAAe,OAAA;AAElC,qCAAmD;AAA1C,kGAAA,WAAW,OAAW;AAC/B,yCAAqC;AAA5B,mGAAA,MAAM,OAAA;AAMf,iCAAqC;AAA5B,mGAAA,UAAU,OAAA", "sourcesContent": ["export { config, getConfig } from \"./config\";\nexport { queue, run, subscribe } from \"./function\";\nexport { withMiddleware, withProxy } from \"./middleware\";\nexport type { RequestMiddleware } from \"./middleware\";\nexport { realtimeImpl as realtime } from \"./realtime\";\nexport { ApiError, ValidationError } from \"./response\";\nexport type { ResponseHandler } from \"./response\";\nexport { storageImpl as storage } from \"./storage\";\nexport { stream } from \"./streaming\";\nexport type {\n  QueueStatus,\n  ValidationErrorInfo,\n  WebHookResponse,\n} from \"./types\";\nexport { parseAppId } from \"./utils\";\n"]}