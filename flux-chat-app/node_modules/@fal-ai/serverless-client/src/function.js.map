{"version": 3, "file": "function.js", "sourceRoot": "", "sources": ["../../../../libs/client/src/function.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAiEA,4BA2BC;AAED,oBAaC;AAWD,kBAKC;AAmYD,8BAUC;AAxgBD,uCAA4C;AAC5C,uCAAwC;AACxC,2CAAiE;AAOjE,mCAAoE;AA+CpE;;;;;;;;GAQG;AACH,SAAgB,QAAQ,CACtB,EAAU,EACV,UAA4C,EAAE;;IAE9C,MAAM,MAAM,GAAG,CAAC,MAAA,OAAO,CAAC,MAAM,mCAAI,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;IACxD,MAAM,IAAI,GAAG,CAAC,MAAA,OAAO,CAAC,IAAI,mCAAI,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;IAC5E,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC5B,MAAM,MAAM,mCACP,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC,GACrB,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CACnC,CAAC;IAEF,MAAM,WAAW,GACf,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC;QAC5B,CAAC,CAAC,IAAI,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE;QAC9C,CAAC,CAAC,EAAE,CAAC;IAET,sCAAsC;IACtC,IAAI,IAAA,kBAAU,EAAC,EAAE,CAAC,EAAE,CAAC;QACnB,MAAM,GAAG,GAAG,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;QAC7C,OAAO,GAAG,GAAG,GAAG,IAAI,GAAG,WAAW,EAAE,CAAC;IACvC,CAAC;IAED,MAAM,KAAK,GAAG,IAAA,yBAAiB,EAAC,EAAE,CAAC,CAAC;IACpC,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;IACnE,MAAM,GAAG,GAAG,WAAW,SAAS,WAAW,KAAK,IAAI,IAAI,EAAE,CAAC;IAC3D,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC;AACnD,CAAC;AAED,SAAsB,IAAI;yDACxB,EAAU,EACV,UAA4C,EAAE;;QAE9C,MAAM,KAAK,GACT,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK;YAC3C,CAAC,CAAC,MAAM,qBAAW,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC;YACjD,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC;QACpB,OAAO,IAAA,yBAAe,EACpB,MAAA,OAAO,CAAC,MAAM,mCAAI,MAAM,EACxB,QAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,EACrB,KAAc,CACf,CAAC;IACJ,CAAC;CAAA;AAKD;;;;;GAKG;AACH,SAAsB,GAAG;yDACvB,EAAU,EACV,UAA6B,EAAE;QAE/B,OAAO,IAAI,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;IAC3B,CAAC;CAAA;AAID,MAAM,qBAAqB,GAAG,GAAG,CAAC;AAwLlC;;;;GAIG;AACU,QAAA,KAAK,GAAU;IACpB,MAAM,CACV,UAAkB,EAClB,OAA6B;;YAE7B,MAAM,EAAE,UAAU,EAAE,IAAI,GAAG,EAAE,KAAoB,OAAO,EAAtB,UAAU,UAAK,OAAO,EAAlD,sBAAwC,CAAU,CAAC;YACzD,OAAO,IAAI,CAAC,UAAU,kCACjB,UAAU,KACb,SAAS,EAAE,OAAO,EAClB,MAAM,EAAE,MAAM,EACd,IAAI,EAAE,IAAI,EACV,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC,SAAS,IAC3D,CAAC;QACL,CAAC;KAAA;IACK,MAAM;6DACV,UAAkB,EAClB,EAAE,SAAS,EAAE,IAAI,GAAG,KAAK,EAAsB;YAE/C,MAAM,KAAK,GAAG,IAAA,kBAAU,EAAC,UAAU,CAAC,CAAC;YACrC,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;YAC5D,OAAO,IAAI,CAAC,GAAG,MAAM,GAAG,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE,EAAE;gBACpD,SAAS,EAAE,OAAO;gBAClB,MAAM,EAAE,KAAK;gBACb,IAAI,EAAE,aAAa,SAAS,SAAS;gBACrC,KAAK,EAAE;oBACL,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;iBACvB;aACF,CAAC,CAAC;QACL,CAAC;KAAA;IAEK,YAAY;6DAChB,UAAkB,EAClB,EAAE,SAAS,EAAE,IAAI,GAAG,KAAK,EAAE,cAAc,EAA4B;YAErE,MAAM,KAAK,GAAG,IAAA,kBAAU,EAAC,UAAU,CAAC,CAAC;YACrC,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;YAE5D,MAAM,WAAW,GAAG;gBAClB,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;aACvB,CAAC;YAEF,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,MAAM,GAAG,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE,EAAE;gBAC7D,SAAS,EAAE,OAAO;gBAClB,IAAI,EAAE,aAAa,SAAS,gBAAgB;gBAC5C,KAAK,EAAE,WAAW;aACnB,CAAC,CAAC;YAEH,OAAO,IAAI,qBAAS,CAAuB,UAAU,EAAE;gBACrD,GAAG;gBACH,MAAM,EAAE,KAAK;gBACb,cAAc;gBACd,WAAW;aACZ,CAAC,CAAC;QACL,CAAC;KAAA;IAEK,iBAAiB,CAAC,UAAU,EAAE,OAAO;;YACzC,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;YACpC,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;YAChC,IAAI,SAAS,GAAc,SAAS,CAAC;YAErC,MAAM,iBAAiB,GAAG,GAAG,EAAE;gBAC7B,mEAAmE;gBACnE,mEAAmE;gBACnE,0EAA0E;YAC5E,CAAC,CAAC;YACF,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;gBACjC,MAAM,MAAM,GAAG,MAAM,aAAK,CAAC,YAAY,CAAC,UAAU,EAAE;oBAClD,SAAS;oBACT,IAAI,EAAE,OAAO,CAAC,IAAI;oBAClB,cAAc,EACZ,gBAAgB,IAAI,OAAO;wBACzB,CAAC,CAAE,OAAO,CAAC,cAA0C;wBACrD,CAAC,CAAC,SAAS;iBAChB,CAAC,CAAC;gBACH,MAAM,IAAI,GAAiB,EAAE,CAAC;gBAC9B,IAAI,OAAO,EAAE,CAAC;oBACZ,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;wBAC1B,MAAM,CAAC,KAAK,EAAE,CAAC;wBACf,aAAK,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;wBACjE,oEAAoE;wBACpE,4DAA4D;wBAC5D,kEAAkE;wBAClE,2CAA2C;wBAC3C,MAAM,IAAI,KAAK,CACb,8DAA8D,OAAO,IAAI,CAC1E,CAAC;oBACJ,CAAC,EAAE,OAAO,CAAC,CAAC;gBACd,CAAC;gBACD,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAiB,EAAE,EAAE;oBACtC,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;wBAC1B,qDAAqD;wBACrD,IACE,MAAM,IAAI,IAAI;4BACd,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;4BACxB,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EACpB,CAAC;4BACD,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;wBAC1B,CAAC;wBACD,OAAO,CAAC,aAAa,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC,iCAAM,IAAI,KAAE,IAAI,IAAG,CAAC,CAAC,IAAI,CAAC,CAAC;oBACnE,CAAC;gBACH,CAAC,CAAC,CAAC;gBACH,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;gBACvC,IAAI,SAAS,EAAE,CAAC;oBACd,YAAY,CAAC,SAAS,CAAC,CAAC;gBAC1B,CAAC;gBACD,OAAO,UAAkC,CAAC;YAC5C,CAAC;YACD,iEAAiE;YACjE,OAAO,IAAI,OAAO,CAAuB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;;gBAC3D,IAAI,gBAA2B,CAAC;gBAChC,sEAAsE;gBACtE,sDAAsD;gBACtD,MAAM,YAAY,GAChB,cAAc,IAAI,OAAO,IAAI,OAAO,OAAO,CAAC,YAAY,KAAK,QAAQ;oBACnE,CAAC,CAAC,CAAC,MAAA,OAAO,CAAC,YAAY,mCAAI,qBAAqB,CAAC;oBACjD,CAAC,CAAC,qBAAqB,CAAC;gBAE5B,MAAM,mBAAmB,GAAG,GAAG,EAAE;oBAC/B,IAAI,SAAS,EAAE,CAAC;wBACd,YAAY,CAAC,SAAS,CAAC,CAAC;oBAC1B,CAAC;oBACD,IAAI,gBAAgB,EAAE,CAAC;wBACrB,YAAY,CAAC,gBAAgB,CAAC,CAAC;oBACjC,CAAC;gBACH,CAAC,CAAC;gBACF,IAAI,OAAO,EAAE,CAAC;oBACZ,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;wBAC1B,mBAAmB,EAAE,CAAC;wBACtB,aAAK,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;wBACjE,MAAM,CACJ,IAAI,KAAK,CACP,8DAA8D,OAAO,IAAI,CAC1E,CACF,CAAC;oBACJ,CAAC,EAAE,OAAO,CAAC,CAAC;gBACd,CAAC;gBACD,MAAM,IAAI,GAAG,GAAS,EAAE;;oBACtB,IAAI,CAAC;wBACH,MAAM,aAAa,GAAG,MAAM,aAAK,CAAC,MAAM,CAAC,UAAU,EAAE;4BACnD,SAAS;4BACT,IAAI,EAAE,MAAA,OAAO,CAAC,IAAI,mCAAI,KAAK;yBAC5B,CAAC,CAAC;wBACH,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;4BAC1B,OAAO,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;wBACvC,CAAC;wBACD,IAAI,aAAa,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;4BACzC,mBAAmB,EAAE,CAAC;4BACtB,OAAO,CAAC,aAAa,CAAC,CAAC;4BACvB,OAAO;wBACT,CAAC;wBACD,gBAAgB,GAAG,UAAU,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;oBACpD,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,mBAAmB,EAAE,CAAC;wBACtB,MAAM,CAAC,KAAK,CAAC,CAAC;oBAChB,CAAC;gBACH,CAAC,CAAA,CAAC;gBACF,IAAI,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC;QACL,CAAC;KAAA;IAEK,MAAM;6DACV,UAAkB,EAClB,EAAE,SAAS,EAAoB;YAE/B,MAAM,KAAK,GAAG,IAAA,kBAAU,EAAC,UAAU,CAAC,CAAC;YACrC,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;YAC5D,OAAO,IAAI,CAAC,GAAG,MAAM,GAAG,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE,EAAE;gBACpD,SAAS,EAAE,OAAO;gBAClB,MAAM,EAAE,KAAK;gBACb,IAAI,EAAE,aAAa,SAAS,EAAE;aAC/B,CAAC,CAAC;QACL,CAAC;KAAA;IAEK,MAAM;6DACV,UAAkB,EAClB,EAAE,SAAS,EAAoB;YAE/B,MAAM,KAAK,GAAG,IAAA,kBAAU,EAAC,UAAU,CAAC,CAAC;YACrC,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;YAC5D,MAAM,IAAI,CAAC,GAAG,MAAM,GAAG,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE,EAAE;gBACnD,SAAS,EAAE,OAAO;gBAClB,MAAM,EAAE,KAAK;gBACb,IAAI,EAAE,aAAa,SAAS,SAAS;aACtC,CAAC,CAAC;QACL,CAAC;KAAA;CACF,CAAC;AAEF;;;;;;GAMG;AACH,SAAsB,SAAS;yDAC7B,UAAkB,EAClB,UAAqD,EAAE;QAEvD,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,MAAM,aAAK,CAAC,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAC1E,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAC/B,CAAC;QACD,MAAM,aAAK,CAAC,iBAAiB,CAAC,UAAU,kBAAI,SAAS,IAAK,OAAO,EAAG,CAAC;QACrE,OAAO,aAAK,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;IACjD,CAAC;CAAA", "sourcesContent": ["import { dispatchRequest } from \"./request\";\nimport { storageImpl } from \"./storage\";\nimport { FalStream, StreamingConnectionMode } from \"./streaming\";\nimport {\n  CompletedQueueStatus,\n  EnqueueResult,\n  QueueStatus,\n  RequestLog,\n} from \"./types\";\nimport { ensureAppIdFormat, isValidUrl, parseAppId } from \"./utils\";\n\n/**\n * The function input and other configuration when running\n * the function, such as the HTTP method to use.\n */\ntype RunOptions<Input> = {\n  /**\n   * The path to the function, if any. Defaults to ``.\n   * @deprecated Pass the path as part of the app id itself, e.g. `fal-ai/sdxl/image-to-image`\n   */\n  readonly path?: string;\n\n  /**\n   * The function input. It will be submitted either as query params\n   * or the body payload, depending on the `method`.\n   */\n  readonly input?: Input;\n\n  /**\n   * The HTTP method, defaults to `post`;\n   */\n  readonly method?: \"get\" | \"post\" | \"put\" | \"delete\" | string;\n\n  /**\n   * If `true`, the function will automatically upload any files\n   * (i.e. instances of `Blob`).\n   *\n   * This is enabled by default. You can disable it by setting it to `false`.\n   */\n  readonly autoUpload?: boolean;\n};\n\ntype ExtraOptions = {\n  /**\n   * If `true`, the function will use the queue to run the function\n   * asynchronously and return the result in a separate call. This\n   * influences how the URL is built.\n   */\n  readonly subdomain?: string;\n\n  /**\n   * The query parameters to include in the URL.\n   */\n  readonly query?: Record<string, string>;\n};\n\n/**\n * Builds the final url to run the function based on its `id` or alias and\n * a the options from `RunOptions<Input>`.\n *\n * @private\n * @param id the function id or alias\n * @param options the run options\n * @returns the final url to run the function\n */\nexport function buildUrl<Input>(\n  id: string,\n  options: RunOptions<Input> & ExtraOptions = {},\n): string {\n  const method = (options.method ?? \"post\").toLowerCase();\n  const path = (options.path ?? \"\").replace(/^\\//, \"\").replace(/\\/{2,}/, \"/\");\n  const input = options.input;\n  const params = {\n    ...(options.query || {}),\n    ...(method === \"get\" ? input : {}),\n  };\n\n  const queryParams =\n    Object.keys(params).length > 0\n      ? `?${new URLSearchParams(params).toString()}`\n      : \"\";\n\n  // if a fal url is passed, just use it\n  if (isValidUrl(id)) {\n    const url = id.endsWith(\"/\") ? id : `${id}/`;\n    return `${url}${path}${queryParams}`;\n  }\n\n  const appId = ensureAppIdFormat(id);\n  const subdomain = options.subdomain ? `${options.subdomain}.` : \"\";\n  const url = `https://${subdomain}fal.run/${appId}/${path}`;\n  return `${url.replace(/\\/$/, \"\")}${queryParams}`;\n}\n\nexport async function send<Input, Output>(\n  id: string,\n  options: RunOptions<Input> & ExtraOptions = {},\n): Promise<Output> {\n  const input =\n    options.input && options.autoUpload !== false\n      ? await storageImpl.transformInput(options.input)\n      : options.input;\n  return dispatchRequest<Input, Output>(\n    options.method ?? \"post\",\n    buildUrl(id, options),\n    input as Input,\n  );\n}\n\nexport type QueueStatusSubscriptionOptions = QueueStatusOptions &\n  Omit<QueueSubscribeOptions, \"onEnqueue\" | \"webhookUrl\">;\n\n/**\n * Runs a fal serverless function identified by its `id`.\n *\n * @param id the registered function revision id or alias.\n * @returns the remote function output\n */\nexport async function run<Input, Output>(\n  id: string,\n  options: RunOptions<Input> = {},\n): Promise<Output> {\n  return send(id, options);\n}\n\ntype TimeoutId = ReturnType<typeof setTimeout> | undefined;\n\nconst DEFAULT_POLL_INTERVAL = 500;\n\n/**\n * Options for subscribing to the request queue.\n */\ntype QueueSubscribeOptions = {\n  /**\n   * The mode to use for subscribing to updates. It defaults to `polling`.\n   * You can also use client-side streaming by setting it to `streaming`.\n   *\n   * **Note:** Streaming is currently experimental and once stable, it will\n   * be the default mode.\n   *\n   * @see pollInterval\n   */\n  mode?: \"polling\" | \"streaming\";\n\n  /**\n   * Callback function that is called when a request is enqueued.\n   * @param requestId - The unique identifier for the enqueued request.\n   */\n  onEnqueue?: (requestId: string) => void;\n\n  /**\n   * Callback function that is called when the status of the queue changes.\n   * @param status - The current status of the queue.\n   */\n  onQueueUpdate?: (status: QueueStatus) => void;\n\n  /**\n   * If `true`, the response will include the logs for the request.\n   * Defaults to `false`.\n   */\n  logs?: boolean;\n\n  /**\n   * The timeout (in milliseconds) for the request. If the request is not\n   * completed within this time, the subscription will be cancelled.\n   *\n   * Keep in mind that although the client resolves the function on a timeout,\n   * and will try to cancel the request on the server, the server might not be\n   * able to cancel the request if it's already running.\n   *\n   * Note: currently, the timeout is not enforced and the default is `undefined`.\n   * This behavior might change in the future.\n   */\n  timeout?: number;\n\n  /**\n   * The URL to send a webhook notification to when the request is completed.\n   * @see WebHookResponse\n   */\n  webhookUrl?: string;\n} & (\n  | {\n      mode?: \"polling\";\n      /**\n       * The interval (in milliseconds) at which to poll for updates.\n       * If not provided, a default value of `500` will be used.\n       *\n       * This value is ignored if `mode` is set to `streaming`.\n       */\n      pollInterval?: number;\n    }\n  | {\n      mode: \"streaming\";\n\n      /**\n       * The connection mode to use for streaming updates. It defaults to `server`.\n       * Set to `client` if your server proxy doesn't support streaming.\n       */\n      connectionMode?: StreamingConnectionMode;\n    }\n);\n\n/**\n * Options for submitting a request to the queue.\n */\ntype SubmitOptions<Input> = RunOptions<Input> & {\n  /**\n   * The URL to send a webhook notification to when the request is completed.\n   * @see WebHookResponse\n   */\n  webhookUrl?: string;\n};\n\ntype BaseQueueOptions = {\n  /**\n   * The unique identifier for the enqueued request.\n   */\n  requestId: string;\n};\n\ntype QueueStatusOptions = BaseQueueOptions & {\n  /**\n   * If `true`, the response will include the logs for the request.\n   * Defaults to `false`.\n   */\n  logs?: boolean;\n};\n\ntype QueueStatusStreamOptions = QueueStatusOptions & {\n  /**\n   * The connection mode to use for streaming updates. It defaults to `server`.\n   * Set to `client` if your server proxy doesn't support streaming.\n   */\n  connectionMode?: StreamingConnectionMode;\n};\n\n/**\n * Represents a request queue with methods for submitting requests,\n * checking their status, retrieving results, and subscribing to updates.\n */\ninterface Queue {\n  /**\n   * Submits a request to the queue.\n   *\n   * @param endpointId - The ID of the function web endpoint.\n   * @param options - Options to configure how the request is run.\n   * @returns A promise that resolves to the result of enqueuing the request.\n   */\n  submit<Input>(\n    endpointId: string,\n    options: SubmitOptions<Input>,\n  ): Promise<EnqueueResult>;\n\n  /**\n   * Retrieves the status of a specific request in the queue.\n   *\n   * @param endpointId - The ID of the function web endpoint.\n   * @param options - Options to configure how the request is run.\n   * @returns A promise that resolves to the status of the request.\n   */\n  status(endpointId: string, options: QueueStatusOptions): Promise<QueueStatus>;\n\n  /**\n   * Subscribes to updates for a specific request in the queue using HTTP streaming events.\n   *\n   * @param endpointId - The ID of the function web endpoint.\n   * @param options - Options to configure how the request is run and how updates are received.\n   * @returns The streaming object that can be used to listen for updates.\n   */\n  streamStatus(\n    endpointId: string,\n    options: QueueStatusStreamOptions,\n  ): Promise<FalStream<unknown, QueueStatus>>;\n\n  /**\n   * Subscribes to updates for a specific request in the queue using polling or streaming.\n   * See `options.mode` for more details.\n   *\n   * @param endpointId - The ID of the function web endpoint.\n   * @param options - Options to configure how the request is run and how updates are received.\n   * @returns A promise that resolves to the final status of the request.\n   */\n  subscribeToStatus(\n    endpointId: string,\n    options: QueueStatusSubscriptionOptions,\n  ): Promise<CompletedQueueStatus>;\n\n  /**\n   * Retrieves the result of a specific request from the queue.\n   *\n   * @param endpointId - The ID of the function web endpoint.\n   * @param options - Options to configure how the request is run.\n   * @returns A promise that resolves to the result of the request.\n   */\n  result<Output>(\n    endpointId: string,\n    options: BaseQueueOptions,\n  ): Promise<Output>;\n\n  /**\n   * Cancels a request in the queue.\n   *\n   * @param endpointId - The ID of the function web endpoint.\n   * @param options - Options to configure how the request\n   * is run and how updates are received.\n   * @returns A promise that resolves once the request is cancelled.\n   * @throws {Error} If the request cannot be cancelled.\n   */\n  cancel(endpointId: string, options: BaseQueueOptions): Promise<void>;\n}\n\n/**\n * The fal run queue module. It allows to submit a function to the queue and get its result\n * on a separate call. This is useful for long running functions that can be executed\n * asynchronously and not .\n */\nexport const queue: Queue = {\n  async submit<Input>(\n    endpointId: string,\n    options: SubmitOptions<Input>,\n  ): Promise<EnqueueResult> {\n    const { webhookUrl, path = \"\", ...runOptions } = options;\n    return send(endpointId, {\n      ...runOptions,\n      subdomain: \"queue\",\n      method: \"post\",\n      path: path,\n      query: webhookUrl ? { fal_webhook: webhookUrl } : undefined,\n    });\n  },\n  async status(\n    endpointId: string,\n    { requestId, logs = false }: QueueStatusOptions,\n  ): Promise<QueueStatus> {\n    const appId = parseAppId(endpointId);\n    const prefix = appId.namespace ? `${appId.namespace}/` : \"\";\n    return send(`${prefix}${appId.owner}/${appId.alias}`, {\n      subdomain: \"queue\",\n      method: \"get\",\n      path: `/requests/${requestId}/status`,\n      input: {\n        logs: logs ? \"1\" : \"0\",\n      },\n    });\n  },\n\n  async streamStatus(\n    endpointId: string,\n    { requestId, logs = false, connectionMode }: QueueStatusStreamOptions,\n  ): Promise<FalStream<unknown, QueueStatus>> {\n    const appId = parseAppId(endpointId);\n    const prefix = appId.namespace ? `${appId.namespace}/` : \"\";\n\n    const queryParams = {\n      logs: logs ? \"1\" : \"0\",\n    };\n\n    const url = buildUrl(`${prefix}${appId.owner}/${appId.alias}`, {\n      subdomain: \"queue\",\n      path: `/requests/${requestId}/status/stream`,\n      query: queryParams,\n    });\n\n    return new FalStream<unknown, QueueStatus>(endpointId, {\n      url,\n      method: \"get\",\n      connectionMode,\n      queryParams,\n    });\n  },\n\n  async subscribeToStatus(endpointId, options): Promise<CompletedQueueStatus> {\n    const requestId = options.requestId;\n    const timeout = options.timeout;\n    let timeoutId: TimeoutId = undefined;\n\n    const handleCancelError = () => {\n      // Ignore errors as the client will follow through with the timeout\n      // regardless of the server response. In case cancelation fails, we\n      // still want to reject the promise and consider the client call canceled.\n    };\n    if (options.mode === \"streaming\") {\n      const status = await queue.streamStatus(endpointId, {\n        requestId,\n        logs: options.logs,\n        connectionMode:\n          \"connectionMode\" in options\n            ? (options.connectionMode as StreamingConnectionMode)\n            : undefined,\n      });\n      const logs: RequestLog[] = [];\n      if (timeout) {\n        timeoutId = setTimeout(() => {\n          status.abort();\n          queue.cancel(endpointId, { requestId }).catch(handleCancelError);\n          // TODO this error cannot bubble up to the user since it's thrown in\n          // a closure in the global scope due to setTimeout behavior.\n          // User will get a platform error instead. We should find a way to\n          // make this behavior aligned with polling.\n          throw new Error(\n            `Client timed out waiting for the request to complete after ${timeout}ms`,\n          );\n        }, timeout);\n      }\n      status.on(\"data\", (data: QueueStatus) => {\n        if (options.onQueueUpdate) {\n          // accumulate logs to match previous polling behavior\n          if (\n            \"logs\" in data &&\n            Array.isArray(data.logs) &&\n            data.logs.length > 0\n          ) {\n            logs.push(...data.logs);\n          }\n          options.onQueueUpdate(\"logs\" in data ? { ...data, logs } : data);\n        }\n      });\n      const doneStatus = await status.done();\n      if (timeoutId) {\n        clearTimeout(timeoutId);\n      }\n      return doneStatus as CompletedQueueStatus;\n    }\n    // default to polling until status streaming is stable and faster\n    return new Promise<CompletedQueueStatus>((resolve, reject) => {\n      let pollingTimeoutId: TimeoutId;\n      // type resolution isn't great in this case, so check for its presence\n      // and and type so the typechecker behaves as expected\n      const pollInterval =\n        \"pollInterval\" in options && typeof options.pollInterval === \"number\"\n          ? (options.pollInterval ?? DEFAULT_POLL_INTERVAL)\n          : DEFAULT_POLL_INTERVAL;\n\n      const clearScheduledTasks = () => {\n        if (timeoutId) {\n          clearTimeout(timeoutId);\n        }\n        if (pollingTimeoutId) {\n          clearTimeout(pollingTimeoutId);\n        }\n      };\n      if (timeout) {\n        timeoutId = setTimeout(() => {\n          clearScheduledTasks();\n          queue.cancel(endpointId, { requestId }).catch(handleCancelError);\n          reject(\n            new Error(\n              `Client timed out waiting for the request to complete after ${timeout}ms`,\n            ),\n          );\n        }, timeout);\n      }\n      const poll = async () => {\n        try {\n          const requestStatus = await queue.status(endpointId, {\n            requestId,\n            logs: options.logs ?? false,\n          });\n          if (options.onQueueUpdate) {\n            options.onQueueUpdate(requestStatus);\n          }\n          if (requestStatus.status === \"COMPLETED\") {\n            clearScheduledTasks();\n            resolve(requestStatus);\n            return;\n          }\n          pollingTimeoutId = setTimeout(poll, pollInterval);\n        } catch (error) {\n          clearScheduledTasks();\n          reject(error);\n        }\n      };\n      poll().catch(reject);\n    });\n  },\n\n  async result<Output>(\n    endpointId: string,\n    { requestId }: BaseQueueOptions,\n  ): Promise<Output> {\n    const appId = parseAppId(endpointId);\n    const prefix = appId.namespace ? `${appId.namespace}/` : \"\";\n    return send(`${prefix}${appId.owner}/${appId.alias}`, {\n      subdomain: \"queue\",\n      method: \"get\",\n      path: `/requests/${requestId}`,\n    });\n  },\n\n  async cancel(\n    endpointId: string,\n    { requestId }: BaseQueueOptions,\n  ): Promise<void> {\n    const appId = parseAppId(endpointId);\n    const prefix = appId.namespace ? `${appId.namespace}/` : \"\";\n    await send(`${prefix}${appId.owner}/${appId.alias}`, {\n      subdomain: \"queue\",\n      method: \"put\",\n      path: `/requests/${requestId}/cancel`,\n    });\n  },\n};\n\n/**\n * Subscribes to updates for a specific request in the queue.\n *\n * @param endpointId - The ID of the function web endpoint.\n * @param options - Options to configure how the request is run and how updates are received.\n * @returns A promise that resolves to the result of the request once it's completed.\n */\nexport async function subscribe<Input, Output>(\n  endpointId: string,\n  options: RunOptions<Input> & QueueSubscribeOptions = {},\n): Promise<Output> {\n  const { request_id: requestId } = await queue.submit(endpointId, options);\n  if (options.onEnqueue) {\n    options.onEnqueue(requestId);\n  }\n  await queue.subscribeToStatus(endpointId, { requestId, ...options });\n  return queue.result(endpointId, { requestId });\n}\n"]}