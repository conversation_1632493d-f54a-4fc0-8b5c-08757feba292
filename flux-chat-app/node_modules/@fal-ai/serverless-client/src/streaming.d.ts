export type StreamingConnectionMode = "client" | "server";
/**
 * The stream API options. It requires the API input and also
 * offers configuration options.
 */
type StreamOptions<Input> = {
    /**
     * The endpoint URL. If not provided, it will be generated from the
     * `endpointId` and the `queryParams`.
     */
    readonly url?: string;
    /**
     * The API input payload.
     */
    readonly input?: Input;
    /**
     * The query parameters to be sent with the request.
     */
    readonly queryParams?: Record<string, string>;
    /**
     * The maximum time interval in milliseconds between stream chunks. Defaults to 15s.
     */
    readonly timeout?: number;
    /**
     * Whether it should auto-upload File-like types to fal's storage
     * or not.
     */
    readonly autoUpload?: boolean;
    /**
     * The HTTP method, defaults to `post`;
     */
    readonly method?: "get" | "post" | "put" | "delete" | string;
    /**
     * The content type the client accepts as response.
     * By default this is set to `text/event-stream`.
     */
    readonly accept?: string;
    /**
     * The streaming connection mode. This is used to determine
     * whether the streaming will be done from the browser itself (client)
     * or through your own server, either when running on NodeJS or when
     * using a proxy that supports streaming.
     *
     * It defaults to `server`. Set to `client` if your server proxy doesn't
     * support streaming.
     */
    readonly connectionMode?: StreamingConnectionMode;
};
type FalStreamEventType = "data" | "error" | "done";
type EventHandler<T = any> = (event: T) => void;
/**
 * The class representing a streaming response. With t
 */
export declare class FalStream<Input, Output> {
    endpointId: string;
    url: string;
    options: StreamOptions<Input>;
    private listeners;
    private buffer;
    private currentData;
    private lastEventTimestamp;
    private streamClosed;
    private donePromise;
    private abortController;
    constructor(endpointId: string, options: StreamOptions<Input>);
    private start;
    private handleResponse;
    private handleError;
    on: (type: FalStreamEventType, listener: EventHandler) => void;
    private emit;
    [Symbol.asyncIterator](): AsyncGenerator<Awaited<Output>, void, unknown>;
    /**
     * Gets a reference to the `Promise` that indicates whether the streaming
     * is done or not. Developers should always call this in their apps to ensure
     * the request is over.
     *
     * An alternative to this, is to use `on('done')` in case your application
     * architecture works best with event listeners.
     *
     * @returns the promise that resolves when the request is done.
     */
    done: () => Promise<Output>;
    /**
     * Aborts the streaming request.
     */
    abort: () => void;
}
/**
 * Calls a fal app that supports streaming and provides a streaming-capable
 * object as a result, that can be used to get partial results through either
 * `AsyncIterator` or through an event listener.
 *
 * @param endpointId the endpoint id, e.g. `fal-ai/llavav15-13b`.
 * @param options the request options, including the input payload.
 * @returns the `FalStream` instance.
 */
export declare function stream<Input = Record<string, any>, Output = any>(endpointId: string, options: StreamOptions<Input>): Promise<FalStream<Input, Output>>;
export {};
