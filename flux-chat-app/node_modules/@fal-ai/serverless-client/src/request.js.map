{"version": 3, "file": "request.js", "sourceRoot": "", "sources": ["../../../../libs/client/src/request.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAYA,0CA+CC;AA3DD,qCAAqC;AAErC,uCAAoD;AAEpD,MAAM,mBAAmB,GACvB,OAAO,SAAS,KAAK,WAAW;IAChC,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,SAAS,MAAK,oBAAoB,CAAC;AAMhD,SAAsB,eAAe;yDACnC,MAAc,EACd,SAAiB,EACjB,KAAY,EACZ,UAAwC,EAAE;;QAE1C,MAAM,EACJ,WAAW,EAAE,gBAAgB,EAC7B,iBAAiB,EACjB,eAAe,EACf,KAAK,GACN,GAAG,IAAA,kBAAS,GAAE,CAAC;QAChB,MAAM,SAAS,GAAG,IAAA,mBAAS,GAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,IAAA,sBAAY,GAAE,EAAE,CAAC;QACtE,MAAM,WAAW,GACf,OAAO,gBAAgB,KAAK,UAAU;YACpC,CAAC,CAAC,gBAAgB,EAAE;YACpB,CAAC,CAAC,gBAAgB,CAAC;QAEvB,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,MAAM,iBAAiB,CAAC;YAC/C,GAAG,EAAE,SAAS;YACd,MAAM,EAAE,MAAM,CAAC,WAAW,EAAE;SAC7B,CAAC,CAAC;QACH,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE,aAAa,EAAE,OAAO,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAC9E,MAAM,cAAc,GAAG,4DAClB,UAAU,KACb,MAAM,EAAE,kBAAkB,EAC1B,cAAc,EAAE,kBAAkB,KAC/B,SAAS,GACT,CAAC,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,EAAE,CAAC,CACJ,CAAC;QAEjB,MAAM,EAAE,eAAe,EAAE,qBAAqB,KAAqB,OAAO,EAAvB,WAAW,UAAK,OAAO,EAApE,mBAA0D,CAAU,CAAC;QAC3E,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,8DAC3B,WAAW,KACd,MAAM,EACN,OAAO,kCACF,cAAc,GACd,CAAC,MAAA,WAAW,CAAC,OAAO,mCAAI,EAAE,CAAC,MAE7B,CAAC,CAAC,mBAAmB,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,KAC7C,IAAI,EACF,MAAM,CAAC,WAAW,EAAE,KAAK,KAAK,IAAI,KAAK;gBACrC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;gBACvB,CAAC,CAAC,SAAS,IACf,CAAC;QACH,MAAM,cAAc,GAAG,qBAAqB,aAArB,qBAAqB,cAArB,qBAAqB,GAAI,eAAe,CAAC;QAChE,OAAO,MAAM,cAAc,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;CAAA", "sourcesContent": ["import { getConfig } from \"./config\";\nimport { ResponseHand<PERSON> } from \"./response\";\nimport { getUserAgent, isBrowser } from \"./runtime\";\n\nconst isCloudflareWorkers =\n  typeof navigator !== \"undefined\" &&\n  navigator?.userAgent === \"Cloudflare-Workers\";\n\ntype RequestOptions = {\n  responseHandler?: ResponseHandler<any>;\n};\n\nexport async function dispatchRequest<Input, Output>(\n  method: string,\n  targetUrl: string,\n  input: Input,\n  options: RequestOptions & RequestInit = {},\n): Promise<Output> {\n  const {\n    credentials: credentialsValue,\n    requestMiddleware,\n    responseHandler,\n    fetch,\n  } = getConfig();\n  const userAgent = isBrowser() ? {} : { \"User-Agent\": getUserAgent() };\n  const credentials =\n    typeof credentialsValue === \"function\"\n      ? credentialsValue()\n      : credentialsValue;\n\n  const { url, headers } = await requestMiddleware({\n    url: targetUrl,\n    method: method.toUpperCase(),\n  });\n  const authHeader = credentials ? { Authorization: `Key ${credentials}` } : {};\n  const requestHeaders = {\n    ...authHeader,\n    Accept: \"application/json\",\n    \"Content-Type\": \"application/json\",\n    ...userAgent,\n    ...(headers ?? {}),\n  } as HeadersInit;\n\n  const { responseHandler: customResponseHandler, ...requestInit } = options;\n  const response = await fetch(url, {\n    ...requestInit,\n    method,\n    headers: {\n      ...requestHeaders,\n      ...(requestInit.headers ?? {}),\n    },\n    ...(!isCloudflareWorkers && { mode: \"cors\" }),\n    body:\n      method.toLowerCase() !== \"get\" && input\n        ? JSON.stringify(input)\n        : undefined,\n  });\n  const handleResponse = customResponseHandler ?? responseHandler;\n  return await handleResponse(response);\n}\n"]}