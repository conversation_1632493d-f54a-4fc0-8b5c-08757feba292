{"version": 3, "file": "storage.js", "sourceRoot": "", "sources": ["../../../../libs/client/src/storage.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qCAAoD;AACpD,uCAA4C;AAC5C,mCAAwC;AAsCxC;;;;;;GAMG;AACH,SAAS,2BAA2B,CAAC,WAAmB;;IACtD,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC7C,OAAO,MAAA,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,mCAAI,KAAK,CAAC;AAC5C,CAAC;AAED;;;;;;GAMG;AACH,SAAe,cAAc,CAAC,IAAU;;QACtC,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,IAAI,0BAA0B,CAAC;QAC5D,MAAM,QAAQ,GACZ,IAAI,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,2BAA2B,CAAC,WAAW,CAAC,EAAE,CAAC;QAC3E,OAAO,MAAM,IAAA,yBAAe,EAC1B,MAAM,EACN,GAAG,IAAA,sBAAa,GAAE,0BAA0B,EAC5C;YACE,YAAY,EAAE,WAAW;YACzB,SAAS,EAAE,QAAQ;SACpB,CACF,CAAC;IACJ,CAAC;CAAA;AAKY,QAAA,WAAW,GAAmB;IACzC,MAAM,EAAE,CAAO,IAAU,EAAE,EAAE;QAC3B,MAAM,EAAE,KAAK,EAAE,GAAG,IAAA,kBAAS,GAAE,CAAC;QAC9B,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,MAAM,cAAc,CAAC,IAAI,CAAC,CAAC;QAC5E,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,SAAS,EAAE;YACtC,MAAM,EAAE,KAAK;YACb,IAAI,EAAE,IAAI;YACV,OAAO,EAAE;gBACP,cAAc,EAAE,IAAI,CAAC,IAAI,IAAI,0BAA0B;aACxD;SACF,CAAC,CAAC;QACH,MAAM,EAAE,eAAe,EAAE,GAAG,IAAA,kBAAS,GAAE,CAAC;QACxC,MAAM,eAAe,CAAC,QAAQ,CAAC,CAAC;QAChC,OAAO,GAAG,CAAC;IACb,CAAC,CAAA;IAED,8DAA8D;IAC9D,cAAc,EAAE,CAAO,KAAU,EAAgB,EAAE;QACjD,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,mBAAW,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5E,CAAC;aAAM,IAAI,KAAK,YAAY,IAAI,EAAE,CAAC;YACjC,OAAO,MAAM,mBAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACzC,CAAC;aAAM,IAAI,IAAA,qBAAa,EAAC,KAAK,CAAC,EAAE,CAAC;YAChC,MAAM,WAAW,GAAG,KAA4B,CAAC;YACjD,MAAM,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,GAAG,CAC9C,KAA4C,EAAE,4CAAvC,CAAC,GAAG,EAAE,KAAK,CAAC;gBACjB,OAAO,CAAC,GAAG,EAAE,MAAM,mBAAW,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;YACxD,CAAC,CAAA,CACF,CAAC;YACF,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC5C,OAAO,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACrC,CAAC;QACD,4EAA4E;QAC5E,OAAO,KAAK,CAAC;IACf,CAAC,CAAA;CACF,CAAC", "sourcesContent": ["import { getConfig, getRestApiUrl } from \"./config\";\nimport { dispatchRequest } from \"./request\";\nimport { isPlainObject } from \"./utils\";\n\n/**\n * File support for the client. This interface establishes the contract for\n * uploading files to the server and transforming the input to replace file\n * objects with URLs.\n */\nexport interface StorageSupport {\n  /**\n   * Upload a file to the server. Returns the URL of the uploaded file.\n   * @param file the file to upload\n   * @param options optional parameters, such as custom file name\n   * @returns the URL of the uploaded file\n   */\n  upload: (file: Blob) => Promise<string>;\n\n  /**\n   * Transform the input to replace file objects with URLs. This is used\n   * to transform the input before sending it to the server and ensures\n   * that the server receives URLs instead of file objects.\n   *\n   * @param input the input to transform.\n   * @returns the transformed input.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  transformInput: (input: Record<string, any>) => Promise<Record<string, any>>;\n}\n\ntype InitiateUploadResult = {\n  file_url: string;\n  upload_url: string;\n};\n\ntype InitiateUploadData = {\n  file_name: string;\n  content_type: string | null;\n};\n\n/**\n * Get the file extension from the content type. This is used to generate\n * a file name if the file name is not provided.\n *\n * @param contentType the content type of the file.\n * @returns the file extension or `bin` if the content type is not recognized.\n */\nfunction getExtensionFromContentType(contentType: string): string {\n  const [_, fileType] = contentType.split(\"/\");\n  return fileType.split(/[-;]/)[0] ?? \"bin\";\n}\n\n/**\n * Initiate the upload of a file to the server. This returns the URL to upload\n * the file to and the URL of the file once it is uploaded.\n *\n * @param file the file to upload\n * @returns the URL to upload the file to and the URL of the file once it is uploaded.\n */\nasync function initiateUpload(file: Blob): Promise<InitiateUploadResult> {\n  const contentType = file.type || \"application/octet-stream\";\n  const filename =\n    file.name || `${Date.now()}.${getExtensionFromContentType(contentType)}`;\n  return await dispatchRequest<InitiateUploadData, InitiateUploadResult>(\n    \"POST\",\n    `${getRestApiUrl()}/storage/upload/initiate`,\n    {\n      content_type: contentType,\n      file_name: filename,\n    },\n  );\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype KeyValuePair = [string, any];\n\nexport const storageImpl: StorageSupport = {\n  upload: async (file: Blob) => {\n    const { fetch } = getConfig();\n    const { upload_url: uploadUrl, file_url: url } = await initiateUpload(file);\n    const response = await fetch(uploadUrl, {\n      method: \"PUT\",\n      body: file,\n      headers: {\n        \"Content-Type\": file.type || \"application/octet-stream\",\n      },\n    });\n    const { responseHandler } = getConfig();\n    await responseHandler(response);\n    return url;\n  },\n\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  transformInput: async (input: any): Promise<any> => {\n    if (Array.isArray(input)) {\n      return Promise.all(input.map((item) => storageImpl.transformInput(item)));\n    } else if (input instanceof Blob) {\n      return await storageImpl.upload(input);\n    } else if (isPlainObject(input)) {\n      const inputObject = input as Record<string, any>;\n      const promises = Object.entries(inputObject).map(\n        async ([key, value]): Promise<KeyValuePair> => {\n          return [key, await storageImpl.transformInput(value)];\n        },\n      );\n      const results = await Promise.all(promises);\n      return Object.fromEntries(results);\n    }\n    // Return the input as is if it's neither an object nor a file/blob/data URI\n    return input;\n  },\n};\n"]}