{"version": 3, "file": "msgpack.js", "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;;UCVA;UACA;;;;;WCDA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACNO,SAAS,SAAS,CAAC,GAAW;IACnC,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC;IAE7B,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,IAAI,GAAG,GAAG,CAAC,CAAC;IACZ,OAAO,GAAG,GAAG,SAAS,EAAE,CAAC;QACvB,IAAI,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC;QAElC,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YAC/B,SAAS;YACT,UAAU,EAAE,CAAC;YACb,SAAS;QACX,CAAC;aAAM,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YACtC,UAAU;YACV,UAAU,IAAI,CAAC,CAAC;QAClB,CAAC;aAAM,CAAC;YACN,wBAAwB;YACxB,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,EAAE,CAAC;gBACvC,iBAAiB;gBACjB,IAAI,GAAG,GAAG,SAAS,EAAE,CAAC;oBACpB,MAAM,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;oBAClC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,MAAM,EAAE,CAAC;wBAChC,EAAE,GAAG,CAAC;wBACN,KAAK,GAAG,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC;oBAC9D,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC/B,SAAS;gBACT,UAAU,IAAI,CAAC,CAAC;YAClB,CAAC;iBAAM,CAAC;gBACN,SAAS;gBACT,UAAU,IAAI,CAAC,CAAC;YAClB,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,UAAU,CAAC;AACpB,CAAC;AAEM,SAAS,YAAY,CAAC,GAAW,EAAE,MAAkB,EAAE,YAAoB;IAChF,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC;IAC7B,IAAI,MAAM,GAAG,YAAY,CAAC;IAC1B,IAAI,GAAG,GAAG,CAAC,CAAC;IACZ,OAAO,GAAG,GAAG,SAAS,EAAE,CAAC;QACvB,IAAI,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC;QAElC,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YAC/B,SAAS;YACT,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,CAAC;YACzB,SAAS;QACX,CAAC;aAAM,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;YACtC,UAAU;YACV,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;QAClD,CAAC;aAAM,CAAC;YACN,wBAAwB;YACxB,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,EAAE,CAAC;gBACvC,iBAAiB;gBACjB,IAAI,GAAG,GAAG,SAAS,EAAE,CAAC;oBACpB,MAAM,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;oBAClC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,MAAM,EAAE,CAAC;wBAChC,EAAE,GAAG,CAAC;wBACN,KAAK,GAAG,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC;oBAC9D,CAAC;gBACH,CAAC;YACH,CAAC;YAED,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC/B,SAAS;gBACT,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;gBACjD,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;YAClD,CAAC;iBAAM,CAAC;gBACN,SAAS;gBACT,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;gBACjD,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;gBACjD,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;YAClD,CAAC;QACH,CAAC;QAED,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;IAC3C,CAAC;AACH,CAAC;AAED,mEAAmE;AACnE,oCAAoC;AACpC,4CAA4C;AAC5C,kCAAkC;AAClC,uDAAuD;AACvD,kDAAkD;AAElD,MAAM,iBAAiB,GAAG,IAAI,WAAW,EAAE,CAAC;AAE5C,mGAAmG;AACnG,4DAA4D;AAC5D,MAAM,sBAAsB,GAAG,EAAE,CAAC;AAE3B,SAAS,YAAY,CAAC,GAAW,EAAE,MAAkB,EAAE,YAAoB;IAChF,iBAAiB,CAAC,UAAU,CAAC,GAAG,EAAE,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC;AACnE,CAAC;AAEM,SAAS,UAAU,CAAC,GAAW,EAAE,MAAkB,EAAE,YAAoB;IAC9E,IAAI,GAAG,CAAC,MAAM,GAAG,sBAAsB,EAAE,CAAC;QACxC,YAAY,CAAC,GAAG,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;IAC1C,CAAC;SAAM,CAAC;QACN,YAAY,CAAC,GAAG,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;IAC1C,CAAC;AACH,CAAC;AAED,MAAM,UAAU,GAAG,IAAO,CAAC;AAEpB,SAAS,YAAY,CAAC,KAAiB,EAAE,WAAmB,EAAE,UAAkB;IACrF,IAAI,MAAM,GAAG,WAAW,CAAC;IACzB,MAAM,GAAG,GAAG,MAAM,GAAG,UAAU,CAAC;IAEhC,MAAM,KAAK,GAAkB,EAAE,CAAC;IAChC,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,OAAO,MAAM,GAAG,GAAG,EAAE,CAAC;QACpB,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,CAAE,CAAC;QAC/B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YACzB,SAAS;YACT,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpB,CAAC;aAAM,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;YACnC,UAAU;YACV,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,CAAE,GAAG,IAAI,CAAC;YACtC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAC5C,CAAC;aAAM,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;YACnC,UAAU;YACV,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,CAAE,GAAG,IAAI,CAAC;YACtC,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,CAAE,GAAG,IAAI,CAAC;YACtC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;QAC5D,CAAC;aAAM,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;YACnC,UAAU;YACV,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,CAAE,GAAG,IAAI,CAAC;YACtC,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,CAAE,GAAG,IAAI,CAAC;YACtC,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,CAAE,GAAG,IAAI,CAAC;YACtC,IAAI,IAAI,GAAG,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,GAAG,KAAK,CAAC;YAChF,IAAI,IAAI,GAAG,MAAM,EAAE,CAAC;gBAClB,IAAI,IAAI,OAAO,CAAC;gBAChB,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC;gBAC7C,IAAI,GAAG,MAAM,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;YACjC,CAAC;YACD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnB,CAAC;aAAM,CAAC;YACN,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACpB,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,IAAI,UAAU,EAAE,CAAC;YAC/B,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC,GAAG,KAAK,CAAC,CAAC;YACxC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QACnB,CAAC;IACH,CAAC;IAED,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACrB,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC,GAAG,KAAK,CAAC,CAAC;IAC1C,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,iBAAiB,GAAG,IAAI,WAAW,EAAE,CAAC;AAE5C,mGAAmG;AACnG,4DAA4D;AAC5D,MAAM,sBAAsB,GAAG,GAAG,CAAC;AAE5B,SAAS,YAAY,CAAC,KAAiB,EAAE,WAAmB,EAAE,UAAkB;IACrF,MAAM,WAAW,GAAG,KAAK,CAAC,QAAQ,CAAC,WAAW,EAAE,WAAW,GAAG,UAAU,CAAC,CAAC;IAC1E,OAAO,iBAAiB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;AAC/C,CAAC;AAEM,SAAS,UAAU,CAAC,KAAiB,EAAE,WAAmB,EAAE,UAAkB;IACnF,IAAI,UAAU,GAAG,sBAAsB,EAAE,CAAC;QACxC,OAAO,YAAY,CAAC,KAAK,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;IACtD,CAAC;SAAM,CAAC;QACN,OAAO,YAAY,CAAC,KAAK,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;IACtD,CAAC;AACH,CAAC;;;AChLD;;GAEG;AACI,MAAM,OAAO;IAIlB,YAAY,IAAY,EAAE,IAAgD;QACxE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;CACF;;;ACXM,MAAM,WAAY,SAAQ,KAAK;IACpC,YAAY,OAAe;QACzB,KAAK,CAAC,OAAO,CAAC,CAAC;QAEf,kDAAkD;QAClD,MAAM,KAAK,GAAiC,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;QACjF,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAEnC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE;YAClC,YAAY,EAAE,IAAI;YAClB,UAAU,EAAE,KAAK;YACjB,KAAK,EAAE,WAAW,CAAC,IAAI;SACxB,CAAC,CAAC;IACL,CAAC;CACF;;;ACdD,kBAAkB;AAEX,MAAM,UAAU,GAAG,UAAW,CAAC;AAEtC,+CAA+C;AAC/C,kEAAkE;AAE3D,SAAS,SAAS,CAAC,IAAc,EAAE,MAAc,EAAE,KAAa;IACrE,MAAM,IAAI,GAAG,KAAK,GAAG,UAAa,CAAC;IACnC,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC,sCAAsC;IACzD,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAC7B,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;AAClC,CAAC;AAEM,SAAS,QAAQ,CAAC,IAAc,EAAE,MAAc,EAAE,KAAa;IACpE,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,UAAa,CAAC,CAAC;IAC/C,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC,sCAAsC;IACzD,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAC7B,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;AAClC,CAAC;AAEM,SAAS,QAAQ,CAAC,IAAc,EAAE,MAAc;IACrD,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACnC,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACvC,OAAO,IAAI,GAAG,UAAa,GAAG,GAAG,CAAC;AACpC,CAAC;AAEM,SAAS,SAAS,CAAC,IAAc,EAAE,MAAc;IACtD,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IACpC,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACvC,OAAO,IAAI,GAAG,UAAa,GAAG,GAAG,CAAC;AACpC,CAAC;;;AC/BD,kFAAkF;AACnC;AACK;AAE7C,MAAM,aAAa,GAAG,CAAC,CAAC,CAAC;AAOhC,MAAM,mBAAmB,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,sBAAsB;AACnE,MAAM,mBAAmB,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,sBAAsB;AAE5D,SAAS,yBAAyB,CAAC,EAAE,GAAG,EAAE,IAAI,EAAY;IAC/D,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,mBAAmB,EAAE,CAAC;QACxD,6BAA6B;QAC7B,IAAI,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,mBAAmB,EAAE,CAAC;YAC7C,sCAAsC;YACtC,MAAM,EAAE,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;YACrC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;YACvB,OAAO,EAAE,CAAC;QACZ,CAAC;aAAM,CAAC;YACN,yDAAyD;YACzD,MAAM,OAAO,GAAG,GAAG,GAAG,WAAW,CAAC;YAClC,MAAM,MAAM,GAAG,GAAG,GAAG,UAAU,CAAC;YAChC,MAAM,EAAE,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;YACrC,oBAAoB;YACpB,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC;YACjD,WAAW;YACX,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;YAC1B,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;SAAM,CAAC;QACN,uDAAuD;QACvD,MAAM,EAAE,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;QAC9B,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QACrC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QACxB,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QACvB,OAAO,EAAE,CAAC;IACZ,CAAC;AACH,CAAC;AAEM,SAAS,oBAAoB,CAAC,IAAU;IAC7C,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAC5B,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;IACnC,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IAEtC,uDAAuD;IACvD,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;IACzC,OAAO;QACL,GAAG,EAAE,GAAG,GAAG,SAAS;QACpB,IAAI,EAAE,IAAI,GAAG,SAAS,GAAG,GAAG;KAC7B,CAAC;AACJ,CAAC;AAEM,SAAS,wBAAwB,CAAC,MAAe;IACtD,IAAI,MAAM,YAAY,IAAI,EAAE,CAAC;QAC3B,MAAM,QAAQ,GAAG,oBAAoB,CAAC,MAAM,CAAC,CAAC;QAC9C,OAAO,yBAAyB,CAAC,QAAQ,CAAC,CAAC;IAC7C,CAAC;SAAM,CAAC;QACN,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AAEM,SAAS,yBAAyB,CAAC,IAAgB;IACxD,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IAEzE,iCAAiC;IACjC,QAAQ,IAAI,CAAC,UAAU,EAAE,CAAC;QACxB,KAAK,CAAC,CAAC,CAAC,CAAC;YACP,2BAA2B;YAC3B,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,IAAI,GAAG,CAAC,CAAC;YACf,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;QACvB,CAAC;QACD,KAAK,CAAC,CAAC,CAAC,CAAC;YACP,mCAAmC;YACnC,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,GAAG,GAAG,CAAC,iBAAiB,GAAG,GAAG,CAAC,GAAG,WAAW,GAAG,QAAQ,CAAC;YAC/D,MAAM,IAAI,GAAG,iBAAiB,KAAK,CAAC,CAAC;YACrC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;QACvB,CAAC;QACD,KAAK,EAAE,CAAC,CAAC,CAAC;YACR,uDAAuD;YAEvD,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC/B,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;QACvB,CAAC;QACD;YACE,MAAM,IAAI,WAAW,CAAC,gEAAgE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;IACzG,CAAC;AACH,CAAC;AAEM,SAAS,wBAAwB,CAAC,IAAgB;IACvD,MAAM,QAAQ,GAAG,yBAAyB,CAAC,IAAI,CAAC,CAAC;IACjD,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,GAAG,GAAG,QAAQ,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;AAC5D,CAAC;AAEM,MAAM,kBAAkB,GAAG;IAChC,IAAI,EAAE,aAAa;IACnB,MAAM,EAAE,wBAAwB;IAChC,MAAM,EAAE,wBAAwB;CACjC,CAAC;;;AC3GF,kDAAkD;AAEX;AACa;AAqB7C,MAAM,cAAc;IAgBzB;QARA,sBAAsB;QACL,oBAAe,GAAgE,EAAE,CAAC;QAClF,oBAAe,GAAgE,EAAE,CAAC;QAEnG,oBAAoB;QACH,aAAQ,GAAgE,EAAE,CAAC;QAC3E,aAAQ,GAAgE,EAAE,CAAC;QAG1F,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;IACpC,CAAC;IAEM,QAAQ,CAAC,EACd,IAAI,EACJ,MAAM,EACN,MAAM,GAKP;QACC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC;YACd,oBAAoB;YACpB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;YAC7B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;QAC/B,CAAC;aAAM,CAAC;YACN,sBAAsB;YACtB,MAAM,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;YACxB,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;YACrC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;QACvC,CAAC;IACH,CAAC;IAEM,WAAW,CAAC,MAAe,EAAE,OAAoB;QACtD,sBAAsB;QACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACrD,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC1C,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;gBACtB,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBACxC,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;oBACjB,MAAM,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;oBACpB,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACjC,CAAC;YACH,CAAC;QACH,CAAC;QAED,oBAAoB;QACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACnC,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;gBACtB,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBACxC,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;oBACjB,MAAM,IAAI,GAAG,CAAC,CAAC;oBACf,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACjC,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,MAAM,YAAY,OAAO,EAAE,CAAC;YAC9B,wBAAwB;YACxB,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,MAAM,CAAC,IAAgB,EAAE,IAAY,EAAE,OAAoB;QAChE,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACnF,IAAI,SAAS,EAAE,CAAC;YACd,OAAO,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACxC,CAAC;aAAM,CAAC;YACN,mDAAmD;YACnD,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACjC,CAAC;IACH,CAAC;;AAhFsB,2BAAY,GAAkC,IAAI,cAAc,EAAE,CAAC;;;ACzB5F,SAAS,iBAAiB,CAAC,MAAe;IACxC,OAAO,CACL,MAAM,YAAY,WAAW,IAAI,CAAC,OAAO,iBAAiB,KAAK,WAAW,IAAI,MAAM,YAAY,iBAAiB,CAAC,CACnH,CAAC;AACJ,CAAC;AAEM,SAAS,gBAAgB,CAC9B,MAA2F;IAE3F,IAAI,MAAM,YAAY,UAAU,EAAE,CAAC;QACjC,OAAO,MAAM,CAAC;IAChB,CAAC;SAAM,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QACtC,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;IAC7E,CAAC;SAAM,IAAI,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC;QACrC,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;SAAM,CAAC;QACN,oBAAoB;QACpB,OAAO,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACjC,CAAC;AACH,CAAC;;;ACnBuD;AACH;AACA;AACK;AAKnD,MAAM,iBAAiB,GAAG,GAAG,CAAC;AAC9B,MAAM,2BAA2B,GAAG,IAAI,CAAC;AAiEzC,MAAM,OAAO;IAiBlB,YAAmB,OAAqC;QAFhD,YAAO,GAAG,KAAK,CAAC;QAGtB,IAAI,CAAC,cAAc,GAAG,OAAO,EAAE,cAAc,IAAK,cAAc,CAAC,YAAgD,CAAC;QAClH,IAAI,CAAC,OAAO,GAAI,OAAgD,EAAE,OAAsB,CAAC,CAAC,sGAAsG;QAEhM,IAAI,CAAC,WAAW,GAAG,OAAO,EAAE,WAAW,IAAI,KAAK,CAAC;QACjD,IAAI,CAAC,QAAQ,GAAG,OAAO,EAAE,QAAQ,IAAI,iBAAiB,CAAC;QACvD,IAAI,CAAC,iBAAiB,GAAG,OAAO,EAAE,iBAAiB,IAAI,2BAA2B,CAAC;QACnF,IAAI,CAAC,QAAQ,GAAG,OAAO,EAAE,QAAQ,IAAI,KAAK,CAAC;QAC3C,IAAI,CAAC,YAAY,GAAG,OAAO,EAAE,YAAY,IAAI,KAAK,CAAC;QACnD,IAAI,CAAC,eAAe,GAAG,OAAO,EAAE,eAAe,IAAI,KAAK,CAAC;QACzD,IAAI,CAAC,mBAAmB,GAAG,OAAO,EAAE,mBAAmB,IAAI,KAAK,CAAC;QAEjE,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACb,IAAI,CAAC,IAAI,GAAG,IAAI,QAAQ,CAAC,IAAI,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;QAClE,IAAI,CAAC,KAAK,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAChD,CAAC;IAEO,KAAK;QACX,kDAAkD;QAClD,4BAA4B;QAC5B,iEAAiE;QACjE,OAAO,IAAI,OAAO,CAAc;YAC9B,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;YACzC,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,eAAe,EAAE,IAAI,CAAC,eAAe;YACrC,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;SACvC,CAAC,CAAC;IACZ,CAAC;IAEO,iBAAiB;QACvB,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;IACf,CAAC;IAED;;;;OAIG;IACI,eAAe,CAAC,MAAe;QACpC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YAC9B,OAAO,QAAQ,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC;YACH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YAEpB,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YACzB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1C,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACvB,CAAC;IACH,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,MAAe;QAC3B,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YAC9B,OAAO,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACjC,CAAC;QAED,IAAI,CAAC;YACH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YAEpB,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YACzB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QACvC,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACvB,CAAC;IACH,CAAC;IAEO,QAAQ,CAAC,MAAe,EAAE,KAAa;QAC7C,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC1B,MAAM,IAAI,KAAK,CAAC,6BAA6B,KAAK,EAAE,CAAC,CAAC;QACxD,CAAC;QAED,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;YACnB,IAAI,CAAC,SAAS,EAAE,CAAC;QACnB,CAAC;aAAM,IAAI,OAAO,MAAM,KAAK,SAAS,EAAE,CAAC;YACvC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAC7B,CAAC;aAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YACtC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC9B,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;YAC5B,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YACnC,CAAC;QACH,CAAC;aAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YACtC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC5B,CAAC;aAAM,IAAI,IAAI,CAAC,WAAW,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC1D,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAC9B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IAEO,uBAAuB,CAAC,WAAmB;QACjD,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,GAAG,WAAW,CAAC;QAE5C,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,YAAY,EAAE,CAAC;YACxC,IAAI,CAAC,YAAY,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;QACtC,CAAC;IACH,CAAC;IAEO,YAAY,CAAC,OAAe;QAClC,MAAM,SAAS,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;QAC3C,MAAM,QAAQ,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,CAAC;QAC3C,MAAM,OAAO,GAAG,IAAI,QAAQ,CAAC,SAAS,CAAC,CAAC;QAExC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAEzB,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC;QACpB,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;IACxB,CAAC;IAEO,SAAS;QACf,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACrB,CAAC;IAEO,aAAa,CAAC,MAAe;QACnC,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;YACrB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC;IACH,CAAC;IAEO,YAAY,CAAC,MAAc;QACjC,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC;YAC9D,IAAI,MAAM,IAAI,CAAC,EAAE,CAAC;gBAChB,IAAI,MAAM,GAAG,IAAI,EAAE,CAAC;oBAClB,kBAAkB;oBAClB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACvB,CAAC;qBAAM,IAAI,MAAM,GAAG,KAAK,EAAE,CAAC;oBAC1B,SAAS;oBACT,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACnB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACvB,CAAC;qBAAM,IAAI,MAAM,GAAG,OAAO,EAAE,CAAC;oBAC5B,UAAU;oBACV,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACnB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBACxB,CAAC;qBAAM,IAAI,MAAM,GAAG,WAAW,EAAE,CAAC;oBAChC,UAAU;oBACV,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACnB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBACxB,CAAC;qBAAM,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;oBAC7B,UAAU;oBACV,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACnB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBACxB,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;gBACnC,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;oBACpB,kBAAkB;oBAClB,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;gBACvC,CAAC;qBAAM,IAAI,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;oBAC3B,QAAQ;oBACR,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACnB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACvB,CAAC;qBAAM,IAAI,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;oBAC7B,SAAS;oBACT,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACnB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBACxB,CAAC;qBAAM,IAAI,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;oBACjC,SAAS;oBACT,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACnB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBACxB,CAAC;qBAAM,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;oBAC7B,SAAS;oBACT,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACnB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBACxB,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;gBACnC,CAAC;YACH,CAAC;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IAEO,mBAAmB,CAAC,MAAc;QACxC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,WAAW;YACX,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACxB,CAAC;aAAM,CAAC;YACN,WAAW;YACX,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACxB,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,MAAc;QACnC,IAAI,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;YACxB,UAAU;YACV,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAC9B,CAAC;aAAM,CAAC;YACN,SAAS;YACT,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;IAEO,iBAAiB,CAAC,UAAkB;QAC1C,IAAI,UAAU,GAAG,EAAE,EAAE,CAAC;YACpB,SAAS;YACT,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,UAAU,CAAC,CAAC;QAClC,CAAC;aAAM,IAAI,UAAU,GAAG,KAAK,EAAE,CAAC;YAC9B,QAAQ;YACR,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAC3B,CAAC;aAAM,IAAI,UAAU,GAAG,OAAO,EAAE,CAAC;YAChC,SAAS;YACT,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC5B,CAAC;aAAM,IAAI,UAAU,GAAG,WAAW,EAAE,CAAC;YACpC,SAAS;YACT,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC5B,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,oBAAoB,UAAU,iBAAiB,CAAC,CAAC;QACnE,CAAC;IACH,CAAC;IAEO,YAAY,CAAC,MAAc;QACjC,MAAM,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC;QAE5B,MAAM,UAAU,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;QACrC,IAAI,CAAC,uBAAuB,CAAC,aAAa,GAAG,UAAU,CAAC,CAAC;QACzD,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;QACnC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QACzC,IAAI,CAAC,GAAG,IAAI,UAAU,CAAC;IACzB,CAAC;IAEO,YAAY,CAAC,MAAe,EAAE,KAAa;QACjD,kEAAkE;QAClE,MAAM,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAClE,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;YAChB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC;QAC5B,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAClC,CAAC;aAAM,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;YACtC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;QAC5B,CAAC;aAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YACtC,IAAI,CAAC,SAAS,CAAC,MAAiC,EAAE,KAAK,CAAC,CAAC;QAC3D,CAAC;aAAM,CAAC;YACN,0FAA0F;YAC1F,MAAM,IAAI,KAAK,CAAC,wBAAwB,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACrF,CAAC;IACH,CAAC;IAEO,YAAY,CAAC,MAAuB;QAC1C,MAAM,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC;QAC/B,IAAI,IAAI,GAAG,KAAK,EAAE,CAAC;YACjB,QAAQ;YACR,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC;aAAM,IAAI,IAAI,GAAG,OAAO,EAAE,CAAC;YAC1B,SAAS;YACT,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC;aAAM,IAAI,IAAI,GAAG,WAAW,EAAE,CAAC;YAC9B,SAAS;YACT,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,qBAAqB,IAAI,EAAE,CAAC,CAAC;QAC/C,CAAC;QACD,MAAM,KAAK,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;QACvC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACvB,CAAC;IAEO,WAAW,CAAC,MAAsB,EAAE,KAAa;QACvD,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC;QAC3B,IAAI,IAAI,GAAG,EAAE,EAAE,CAAC;YACd,WAAW;YACX,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;QAC5B,CAAC;aAAM,IAAI,IAAI,GAAG,OAAO,EAAE,CAAC;YAC1B,WAAW;YACX,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC;aAAM,IAAI,IAAI,GAAG,WAAW,EAAE,CAAC;YAC9B,WAAW;YACX,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,oBAAoB,IAAI,EAAE,CAAC,CAAC;QAC9C,CAAC;QACD,KAAK,MAAM,IAAI,IAAI,MAAM,EAAE,CAAC;YAC1B,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;QACjC,CAAC;IACH,CAAC;IAEO,qBAAqB,CAAC,MAA+B,EAAE,IAA2B;QACxF,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,IAAI,MAAM,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE,CAAC;gBAC9B,KAAK,EAAE,CAAC;YACV,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,SAAS,CAAC,MAA+B,EAAE,KAAa;QAC9D,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACjC,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC,IAAI,EAAE,CAAC;QACd,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;QAE3F,IAAI,IAAI,GAAG,EAAE,EAAE,CAAC;YACd,SAAS;YACT,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;QAC5B,CAAC;aAAM,IAAI,IAAI,GAAG,OAAO,EAAE,CAAC;YAC1B,SAAS;YACT,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC;aAAM,IAAI,IAAI,GAAG,WAAW,EAAE,CAAC;YAC9B,SAAS;YACT,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,yBAAyB,IAAI,EAAE,CAAC,CAAC;QACnD,CAAC;QAED,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;YAE1B,IAAI,CAAC,CAAC,IAAI,CAAC,eAAe,IAAI,KAAK,KAAK,SAAS,CAAC,EAAE,CAAC;gBACnD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;gBACvB,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;YAClC,CAAC;QACH,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,GAAY;QAClC,IAAI,OAAO,GAAG,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YACnC,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;YACpC,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;YAEzB,IAAI,IAAI,IAAI,WAAW,EAAE,CAAC;gBACxB,MAAM,IAAI,KAAK,CAAC,+BAA+B,IAAI,EAAE,CAAC,CAAC;YACzD,CAAC;YAED,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACpB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACvB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACpB,OAAO;QACT,CAAC;QAED,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;QAC7B,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;YACf,WAAW;YACX,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC;aAAM,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;YACtB,WAAW;YACX,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC;aAAM,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;YACtB,WAAW;YACX,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC;aAAM,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;YACtB,WAAW;YACX,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC;aAAM,IAAI,IAAI,KAAK,EAAE,EAAE,CAAC;YACvB,YAAY;YACZ,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC;aAAM,IAAI,IAAI,GAAG,KAAK,EAAE,CAAC;YACxB,QAAQ;YACR,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACrB,CAAC;aAAM,IAAI,IAAI,GAAG,OAAO,EAAE,CAAC;YAC1B,SAAS;YACT,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC;aAAM,IAAI,IAAI,GAAG,WAAW,EAAE,CAAC;YAC9B,SAAS;YACT,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,+BAA+B,IAAI,EAAE,CAAC,CAAC;QACzD,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACvB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAEO,OAAO,CAAC,KAAa;QAC3B,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACpC,IAAI,CAAC,GAAG,EAAE,CAAC;IACb,CAAC;IAEO,QAAQ,CAAC,MAAyB;QACxC,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;QAEnC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QACjC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC;IACnB,CAAC;IAEO,OAAO,CAAC,KAAa;QAC3B,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACnC,IAAI,CAAC,GAAG,EAAE,CAAC;IACb,CAAC;IAEO,QAAQ,CAAC,KAAa;QAC5B,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACrC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IAChB,CAAC;IAEO,QAAQ,CAAC,KAAa;QAC5B,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACpC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IAChB,CAAC;IAEO,QAAQ,CAAC,KAAa;QAC5B,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACrC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IAChB,CAAC;IAEO,QAAQ,CAAC,KAAa;QAC5B,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACpC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IAChB,CAAC;IAEO,QAAQ,CAAC,KAAa;QAC5B,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACtC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IAChB,CAAC;IAEO,QAAQ,CAAC,KAAa;QAC5B,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACtC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IAChB,CAAC;IAEO,QAAQ,CAAC,KAAa;QAC5B,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;QAEhC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACtC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IAChB,CAAC;IAEO,QAAQ,CAAC,KAAa;QAC5B,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;QAEhC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACrC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IAChB,CAAC;IAEO,cAAc,CAAC,KAAa;QAClC,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACxC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IAChB,CAAC;IAEO,aAAa,CAAC,KAAa;QACjC,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;QAEhC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACvC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;IAChB,CAAC;CACF;;;ACrkBsC;AAIvC;;;;;GAKG;AACI,SAAS,MAAM,CACpB,KAAc,EACd,OAAqD;IAErD,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC;IACrC,OAAO,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AACxC,CAAC;;;AChBM,SAAS,UAAU,CAAC,IAAY;IACrC,OAAO,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,CAAC;AACnF,CAAC;;;ACF8C;AAE/C,MAAM,sBAAsB,GAAG,EAAE,CAAC;AAClC,MAAM,0BAA0B,GAAG,EAAE,CAAC;AAW/B,MAAM,gBAAgB;IAO3B,YAAY,YAAY,GAAG,sBAAsB,EAAE,eAAe,GAAG,0BAA0B;QAN/F,QAAG,GAAG,CAAC,CAAC;QACR,SAAI,GAAG,CAAC,CAAC;QAMP,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QAEvC,oDAAoD;QACpD,sEAAsE;QACtE,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC;YAC3C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACvB,CAAC;IACH,CAAC;IAEM,WAAW,CAAC,UAAkB;QACnC,OAAO,UAAU,GAAG,CAAC,IAAI,UAAU,IAAI,IAAI,CAAC,YAAY,CAAC;IAC3D,CAAC;IAEO,IAAI,CAAC,KAAiB,EAAE,WAAmB,EAAE,UAAkB;QACrE,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAE,CAAC;QAE7C,UAAU,EAAE,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YACzC,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC;YAEjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;gBACpC,IAAI,WAAW,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE,CAAC;oBAC9C,SAAS,UAAU,CAAC;gBACtB,CAAC;YACH,CAAC;YACD,OAAO,MAAM,CAAC,GAAG,CAAC;QACpB,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,KAAiB,EAAE,KAAa;QAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAE,CAAC;QAC/C,MAAM,MAAM,GAAmB,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;QAErD,IAAI,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YAC3C,sBAAsB;YACtB,yCAAyC;YACzC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;QACzD,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvB,CAAC;IACH,CAAC;IAEM,MAAM,CAAC,KAAiB,EAAE,WAAmB,EAAE,UAAkB;QACtE,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;QAC9D,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;YACxB,IAAI,CAAC,GAAG,EAAE,CAAC;YACX,OAAO,WAAW,CAAC;QACrB,CAAC;QACD,IAAI,CAAC,IAAI,EAAE,CAAC;QAEZ,MAAM,GAAG,GAAG,YAAY,CAAC,KAAK,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;QACzD,+IAA+I;QAC/I,MAAM,iBAAiB,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,WAAW,EAAE,WAAW,GAAG,UAAU,CAAC,CAAC;QACxG,IAAI,CAAC,KAAK,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC;QACnC,OAAO,GAAG,CAAC;IACb,CAAC;CACF;;;AChFkD;AACE;AACY;AACpB;AACa;AACD;AACV;AA4E/C,MAAM,WAAW,GAAG,OAAO,CAAC;AAC5B,MAAM,aAAa,GAAG,SAAS,CAAC;AAChC,MAAM,eAAe,GAAG,WAAW,CAAC;AAIpC,MAAM,eAAe,GAAG,CAAC,GAAY,EAAc,EAAE;IACnD,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;QACvD,OAAO,GAAG,CAAC;IACb,CAAC;IACD,MAAM,IAAI,WAAW,CAAC,+CAA+C,GAAG,OAAO,GAAG,CAAC,CAAC;AACtF,CAAC,CAAC;AAiBF,MAAM,SAAS;IAAf;QACmB,UAAK,GAAsB,EAAE,CAAC;QACvC,sBAAiB,GAAG,CAAC,CAAC,CAAC;IA8EjC,CAAC;IA5EC,IAAW,MAAM;QACf,OAAO,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;IACpC,CAAC;IAEM,GAAG;QACR,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAC5C,CAAC;IAEM,cAAc,CAAC,IAAY;QAChC,MAAM,KAAK,GAAG,IAAI,CAAC,6BAA6B,EAAqB,CAAC;QAEtE,KAAK,CAAC,IAAI,GAAG,WAAW,CAAC;QACzB,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC;QACnB,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QAClB,KAAK,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAEM,YAAY,CAAC,IAAY;QAC9B,MAAM,KAAK,GAAG,IAAI,CAAC,6BAA6B,EAAmB,CAAC;QAEpE,KAAK,CAAC,IAAI,GAAG,aAAa,CAAC;QAC3B,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC;QACpB,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;QAClB,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC;IACjB,CAAC;IAEO,6BAA6B;QACnC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,IAAI,IAAI,CAAC,iBAAiB,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YACjD,MAAM,YAAY,GAAwB;gBACxC,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,SAAS;gBAChB,QAAQ,EAAE,CAAC;gBACX,SAAS,EAAE,CAAC;gBACZ,GAAG,EAAE,SAAS;gBACd,GAAG,EAAE,IAAI;aACV,CAAC;YAEF,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,YAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAC5C,CAAC;IAEM,OAAO,CAAC,KAAiB;QAC9B,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAEzD,IAAI,aAAa,KAAK,KAAK,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,iEAAiE,CAAC,CAAC;QACrF,CAAC;QAED,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;YAC/B,MAAM,YAAY,GAAG,KAAiC,CAAC;YACvD,YAAY,CAAC,IAAI,GAAG,CAAC,CAAC;YACtB,YAAY,CAAC,KAAK,GAAG,SAAS,CAAC;YAC/B,YAAY,CAAC,QAAQ,GAAG,CAAC,CAAC;YAC1B,YAAY,CAAC,IAAI,GAAG,SAAS,CAAC;QAChC,CAAC;QAED,IAAI,KAAK,CAAC,IAAI,KAAK,aAAa,IAAI,KAAK,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;YACnE,MAAM,YAAY,GAAG,KAA+B,CAAC;YACrD,YAAY,CAAC,IAAI,GAAG,CAAC,CAAC;YACtB,YAAY,CAAC,GAAG,GAAG,SAAS,CAAC;YAC7B,YAAY,CAAC,SAAS,GAAG,CAAC,CAAC;YAC3B,YAAY,CAAC,IAAI,GAAG,SAAS,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAEM,KAAK;QACV,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QACtB,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;IAC9B,CAAC;CACF;AAID,MAAM,kBAAkB,GAAG,CAAC,CAAC,CAAC;AAE9B,MAAM,UAAU,GAAG,IAAI,QAAQ,CAAkB,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;AACrE,MAAM,WAAW,GAAG,IAAI,UAAU,CAAkB,UAAU,CAAC,MAAM,CAAC,CAAC;AAEvE,IAAI,CAAC;IACH,kDAAkD;IAClD,yCAAyC;IACzC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACxB,CAAC;AAAC,OAAO,CAAC,EAAE,CAAC;IACX,IAAI,CAAC,CAAC,CAAC,YAAY,UAAU,CAAC,EAAE,CAAC;QAC/B,MAAM,IAAI,KAAK,CACb,kIAAkI,CACnI,CAAC;IACJ,CAAC;AACH,CAAC;AAED,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,mBAAmB,CAAC,CAAC;AAEtD,MAAM,sBAAsB,GAAG,IAAI,gBAAgB,EAAE,CAAC;AAE/C,MAAM,OAAO;IAuBlB,YAAmB,OAAqC;QAVhD,aAAQ,GAAG,CAAC,CAAC;QACb,QAAG,GAAG,CAAC,CAAC;QAER,SAAI,GAAG,UAAU,CAAC;QAClB,UAAK,GAAG,WAAW,CAAC;QACpB,aAAQ,GAAG,kBAAkB,CAAC;QACrB,UAAK,GAAG,IAAI,SAAS,EAAE,CAAC;QAEjC,YAAO,GAAG,KAAK,CAAC;QAGtB,IAAI,CAAC,cAAc,GAAG,OAAO,EAAE,cAAc,IAAK,cAAc,CAAC,YAAgD,CAAC;QAClH,IAAI,CAAC,OAAO,GAAI,OAAgD,EAAE,OAAsB,CAAC,CAAC,sGAAsG;QAEhM,IAAI,CAAC,WAAW,GAAG,OAAO,EAAE,WAAW,IAAI,KAAK,CAAC;QACjD,IAAI,CAAC,UAAU,GAAG,OAAO,EAAE,UAAU,IAAI,KAAK,CAAC;QAC/C,IAAI,CAAC,YAAY,GAAG,OAAO,EAAE,YAAY,IAAI,UAAU,CAAC;QACxD,IAAI,CAAC,YAAY,GAAG,OAAO,EAAE,YAAY,IAAI,UAAU,CAAC;QACxD,IAAI,CAAC,cAAc,GAAG,OAAO,EAAE,cAAc,IAAI,UAAU,CAAC;QAC5D,IAAI,CAAC,YAAY,GAAG,OAAO,EAAE,YAAY,IAAI,UAAU,CAAC;QACxD,IAAI,CAAC,YAAY,GAAG,OAAO,EAAE,YAAY,IAAI,UAAU,CAAC;QACxD,IAAI,CAAC,UAAU,GAAG,OAAO,EAAE,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,sBAAsB,CAAC;QAClG,IAAI,CAAC,eAAe,GAAG,OAAO,EAAE,eAAe,IAAI,eAAe,CAAC;IACrE,CAAC;IAEO,KAAK;QACX,iEAAiE;QACjE,OAAO,IAAI,OAAO,CAAC;YACjB,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,cAAc,EAAE,IAAI,CAAC,cAAc;YACnC,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,YAAY,EAAE,IAAI,CAAC,YAAY;YAC/B,UAAU,EAAE,IAAI,CAAC,UAAU;SACrB,CAAC,CAAC;IACZ,CAAC;IAEO,iBAAiB;QACvB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;QAClB,IAAI,CAAC,QAAQ,GAAG,kBAAkB,CAAC;QACnC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QAEnB,6DAA6D;IAC/D,CAAC;IAEO,SAAS,CAAC,MAA6D;QAC7E,MAAM,KAAK,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;QACvC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;QAC3E,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;IACf,CAAC;IAEO,YAAY,CAAC,MAA6D;QAChF,IAAI,IAAI,CAAC,QAAQ,KAAK,kBAAkB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;YAClE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpD,MAAM,OAAO,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAEzC,iCAAiC;YACjC,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,aAAa,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;YACxE,SAAS,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YAC7B,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;YAC7C,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;IAEO,YAAY,CAAC,IAAY;QAC/B,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC;IACjD,CAAC;IAEO,oBAAoB,CAAC,SAAiB;QAC5C,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAC3B,OAAO,IAAI,UAAU,CAAC,SAAS,IAAI,CAAC,UAAU,GAAG,GAAG,OAAO,IAAI,CAAC,UAAU,4BAA4B,SAAS,GAAG,CAAC,CAAC;IACtH,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,MAA6D;QACzE,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YAC9B,OAAO,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACjC,CAAC;QAED,IAAI,CAAC;YACH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YAEpB,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAEvB,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YACnC,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;gBACzB,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC5C,CAAC;YACD,OAAO,MAAM,CAAC;QAChB,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACvB,CAAC;IACH,CAAC;IAEM,CAAC,WAAW,CAAC,MAA6D;QAC/E,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YAC9B,KAAK,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YACpC,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YAEpB,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;YAEvB,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC5B,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;YAC5B,CAAC;QACH,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACvB,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,MAA4E;QACnG,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YAC9B,OAAO,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QACtC,CAAC;QAED,IAAI,CAAC;YACH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YAEpB,IAAI,OAAO,GAAG,KAAK,CAAC;YACpB,IAAI,MAAe,CAAC;YACpB,IAAI,KAAK,EAAE,MAAM,MAAM,IAAI,MAAM,EAAE,CAAC;gBAClC,IAAI,OAAO,EAAE,CAAC;oBACZ,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;oBACrB,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACjD,CAAC;gBAED,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;gBAE1B,IAAI,CAAC;oBACH,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;oBAC7B,OAAO,GAAG,IAAI,CAAC;gBACjB,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACX,IAAI,CAAC,CAAC,CAAC,YAAY,UAAU,CAAC,EAAE,CAAC;wBAC/B,MAAM,CAAC,CAAC,CAAC,UAAU;oBACrB,CAAC;oBACD,cAAc;gBAChB,CAAC;gBACD,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,GAAG,CAAC;YAC5B,CAAC;YAED,IAAI,OAAO,EAAE,CAAC;gBACZ,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;oBACzB,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACjD,CAAC;gBACD,OAAO,MAAM,CAAC;YAChB,CAAC;YAED,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;YACzC,MAAM,IAAI,UAAU,CAClB,gCAAgC,UAAU,CAAC,QAAQ,CAAC,OAAO,QAAQ,KAAK,GAAG,yBAAyB,CACrG,CAAC;QACJ,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACvB,CAAC;IACH,CAAC;IAEM,iBAAiB,CACtB,MAA4E;QAE5E,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAC7C,CAAC;IAEM,YAAY,CAAC,MAA4E;QAC9F,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC9C,CAAC;IAEO,KAAK,CAAC,CAAC,gBAAgB,CAAC,MAA4E,EAAE,OAAgB;QAC5H,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;YAC9B,KAAK,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAClD,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YAEpB,IAAI,qBAAqB,GAAG,OAAO,CAAC;YACpC,IAAI,cAAc,GAAG,CAAC,CAAC,CAAC;YAExB,IAAI,KAAK,EAAE,MAAM,MAAM,IAAI,MAAM,EAAE,CAAC;gBAClC,IAAI,OAAO,IAAI,cAAc,KAAK,CAAC,EAAE,CAAC;oBACpC,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACjD,CAAC;gBAED,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;gBAE1B,IAAI,qBAAqB,EAAE,CAAC;oBAC1B,cAAc,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;oBACtC,qBAAqB,GAAG,KAAK,CAAC;oBAC9B,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,CAAC;gBAED,IAAI,CAAC;oBACH,OAAO,IAAI,EAAE,CAAC;wBACZ,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;wBAC1B,IAAI,EAAE,cAAc,KAAK,CAAC,EAAE,CAAC;4BAC3B,MAAM;wBACR,CAAC;oBACH,CAAC;gBACH,CAAC;gBAAC,OAAO,CAAC,EAAE,CAAC;oBACX,IAAI,CAAC,CAAC,CAAC,YAAY,UAAU,CAAC,EAAE,CAAC;wBAC/B,MAAM,CAAC,CAAC,CAAC,UAAU;oBACrB,CAAC;oBACD,cAAc;gBAChB,CAAC;gBACD,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,GAAG,CAAC;YAC5B,CAAC;QACH,CAAC;gBAAS,CAAC;YACT,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACvB,CAAC;IACH,CAAC;IAEO,YAAY;QAClB,MAAM,EAAE,OAAO,IAAI,EAAE,CAAC;YACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YACrC,IAAI,MAAe,CAAC;YAEpB,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;gBACrB,0CAA0C;gBAC1C,MAAM,GAAG,QAAQ,GAAG,KAAK,CAAC;YAC5B,CAAC;iBAAM,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;gBAC3B,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;oBACpB,0CAA0C;oBAC1C,MAAM,GAAG,QAAQ,CAAC;gBACpB,CAAC;qBAAM,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;oBAC3B,iCAAiC;oBACjC,MAAM,IAAI,GAAG,QAAQ,GAAG,IAAI,CAAC;oBAC7B,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;wBACf,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;wBACxB,IAAI,CAAC,QAAQ,EAAE,CAAC;wBAChB,SAAS,MAAM,CAAC;oBAClB,CAAC;yBAAM,CAAC;wBACN,MAAM,GAAG,EAAE,CAAC;oBACd,CAAC;gBACH,CAAC;qBAAM,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;oBAC3B,mCAAmC;oBACnC,MAAM,IAAI,GAAG,QAAQ,GAAG,IAAI,CAAC;oBAC7B,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;wBACf,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;wBAC1B,IAAI,CAAC,QAAQ,EAAE,CAAC;wBAChB,SAAS,MAAM,CAAC;oBAClB,CAAC;yBAAM,CAAC;wBACN,MAAM,GAAG,EAAE,CAAC;oBACd,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,iCAAiC;oBACjC,MAAM,UAAU,GAAG,QAAQ,GAAG,IAAI,CAAC;oBACnC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;gBAC5C,CAAC;YACH,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,MAAM;gBACN,MAAM,GAAG,IAAI,CAAC;YAChB,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,QAAQ;gBACR,MAAM,GAAG,KAAK,CAAC;YACjB,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,OAAO;gBACP,MAAM,GAAG,IAAI,CAAC;YAChB,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,WAAW;gBACX,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAC1B,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,WAAW;gBACX,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAC1B,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,SAAS;gBACT,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YACzB,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,UAAU;gBACV,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAC1B,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,UAAU;gBACV,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAC1B,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,UAAU;gBACV,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;oBACrB,MAAM,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;gBAClC,CAAC;qBAAM,CAAC;oBACN,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC1B,CAAC;YACH,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,QAAQ;gBACR,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YACzB,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,SAAS;gBACT,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAC1B,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,SAAS;gBACT,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;YAC1B,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,SAAS;gBACT,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;oBACrB,MAAM,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;gBAClC,CAAC;qBAAM,CAAC;oBACN,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC1B,CAAC;YACH,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,QAAQ;gBACR,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;gBACjC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;YAC5C,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,SAAS;gBACT,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAClC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;YAC5C,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,SAAS;gBACT,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAClC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;YAC5C,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,WAAW;gBACX,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC5B,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;oBACf,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;oBAC1B,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAChB,SAAS,MAAM,CAAC;gBAClB,CAAC;qBAAM,CAAC;oBACN,MAAM,GAAG,EAAE,CAAC;gBACd,CAAC;YACH,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,WAAW;gBACX,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC5B,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;oBACf,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;oBAC1B,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAChB,SAAS,MAAM,CAAC;gBAClB,CAAC;qBAAM,CAAC;oBACN,MAAM,GAAG,EAAE,CAAC;gBACd,CAAC;YACH,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,SAAS;gBACT,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC5B,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;oBACf,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;oBACxB,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAChB,SAAS,MAAM,CAAC;gBAClB,CAAC;qBAAM,CAAC;oBACN,MAAM,GAAG,EAAE,CAAC;gBACd,CAAC;YACH,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,SAAS;gBACT,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC5B,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;oBACf,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;oBACxB,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAChB,SAAS,MAAM,CAAC;gBAClB,CAAC;qBAAM,CAAC;oBACN,MAAM,GAAG,EAAE,CAAC;gBACd,CAAC;YACH,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,QAAQ;gBACR,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;gBAC3B,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YACtC,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,SAAS;gBACT,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC5B,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YACtC,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,SAAS;gBACT,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC5B,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YACtC,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,WAAW;gBACX,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACtC,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,WAAW;gBACX,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACtC,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,WAAW;gBACX,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACtC,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,WAAW;gBACX,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACtC,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,YAAY;gBACZ,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;YACvC,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,QAAQ;gBACR,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;gBAC3B,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YACzC,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,SAAS;gBACT,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC5B,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YACzC,CAAC;iBAAM,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBAC7B,SAAS;gBACT,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;gBAC5B,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YACzC,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,WAAW,CAAC,2BAA2B,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAC3E,CAAC;YAED,IAAI,CAAC,QAAQ,EAAE,CAAC;YAEhB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;YACzB,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxB,kBAAkB;gBAClB,MAAM,KAAK,GAAG,KAAK,CAAC,GAAG,EAAG,CAAC;gBAC3B,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;oBAC/B,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;oBACrC,KAAK,CAAC,QAAQ,EAAE,CAAC;oBACjB,IAAI,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,EAAE,CAAC;wBAClC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC;wBACrB,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBACvB,CAAC;yBAAM,CAAC;wBACN,SAAS,MAAM,CAAC;oBAClB,CAAC;gBACH,CAAC;qBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;oBACxC,IAAI,MAAM,KAAK,WAAW,EAAE,CAAC;wBAC3B,MAAM,IAAI,WAAW,CAAC,kCAAkC,CAAC,CAAC;oBAC5D,CAAC;oBAED,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;oBACzC,KAAK,CAAC,IAAI,GAAG,eAAe,CAAC;oBAC7B,SAAS,MAAM,CAAC;gBAClB,CAAC;qBAAM,CAAC;oBACN,mDAAmD;oBAEnD,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAI,CAAC,GAAG,MAAM,CAAC;oBAC/B,KAAK,CAAC,SAAS,EAAE,CAAC;oBAElB,IAAI,KAAK,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,EAAE,CAAC;wBACnC,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC;wBACnB,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBACvB,CAAC;yBAAM,CAAC;wBACN,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;wBACjB,KAAK,CAAC,IAAI,GAAG,aAAa,CAAC;wBAC3B,SAAS,MAAM,CAAC;oBAClB,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;IAEO,YAAY;QAClB,IAAI,IAAI,CAAC,QAAQ,KAAK,kBAAkB,EAAE,CAAC;YACzC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;YAC9B,sDAAsD;QACxD,CAAC;QAED,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAEO,QAAQ;QACd,IAAI,CAAC,QAAQ,GAAG,kBAAkB,CAAC;IACrC,CAAC;IAEO,aAAa;QACnB,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAErC,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,IAAI;gBACP,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;YACxB,KAAK,IAAI;gBACP,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;YACxB,OAAO,CAAC,CAAC,CAAC;gBACR,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC;oBACpB,OAAO,QAAQ,GAAG,IAAI,CAAC;gBACzB,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,WAAW,CAAC,iCAAiC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBACjF,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,YAAY,CAAC,IAAY;QAC/B,IAAI,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YAC7B,MAAM,IAAI,WAAW,CAAC,oCAAoC,IAAI,2BAA2B,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACjH,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC;IAEO,cAAc,CAAC,IAAY;QACjC,IAAI,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YAC/B,MAAM,IAAI,WAAW,CAAC,sCAAsC,IAAI,uBAAuB,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC;QACjH,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAEO,YAAY,CAAC,UAAkB,EAAE,YAAoB;QAC3D,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,aAAa,EAAE,EAAE,CAAC;YAC7C,OAAO,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;QACzD,CAAC;QACD,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,UAAkB,EAAE,YAAoB;QAC/D,IAAI,UAAU,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YACnC,MAAM,IAAI,WAAW,CACnB,2CAA2C,UAAU,qBAAqB,IAAI,CAAC,YAAY,GAAG,CAC/F,CAAC;QACJ,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,GAAG,YAAY,GAAG,UAAU,EAAE,CAAC;YACjE,MAAM,SAAS,CAAC;QAClB,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,GAAG,YAAY,CAAC;QACvC,IAAI,MAAc,CAAC;QACnB,IAAI,IAAI,CAAC,aAAa,EAAE,IAAI,IAAI,CAAC,UAAU,EAAE,WAAW,CAAC,UAAU,CAAC,EAAE,CAAC;YACrE,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;QAClE,CAAC;aAAM,CAAC;YACN,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;QACtD,CAAC;QACD,IAAI,CAAC,GAAG,IAAI,YAAY,GAAG,UAAU,CAAC;QACtC,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,aAAa;QACnB,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,EAAG,CAAC;YAChC,OAAO,KAAK,CAAC,IAAI,KAAK,aAAa,CAAC;QACtC,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,UAAkB,EAAE,UAAkB;QACzD,IAAI,UAAU,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YACnC,MAAM,IAAI,WAAW,CAAC,oCAAoC,UAAU,qBAAqB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACjH,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,UAAU,CAAC,EAAE,CAAC;YAChD,MAAM,SAAS,CAAC;QAClB,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,GAAG,UAAU,CAAC;QACrC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,UAAU,CAAC,CAAC;QAChE,IAAI,CAAC,GAAG,IAAI,UAAU,GAAG,UAAU,CAAC;QACpC,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,eAAe,CAAC,IAAY,EAAE,UAAkB;QACtD,IAAI,IAAI,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;YAC7B,MAAM,IAAI,WAAW,CAAC,oCAAoC,IAAI,qBAAqB,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QAC3G,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,GAAG,UAAU,CAAC,CAAC;QACzD,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,UAAU,GAAG,CAAC,CAAC,aAAa,CAAC,CAAC;QACnE,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;IACjE,CAAC;IAEO,MAAM;QACZ,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACtC,CAAC;IAEO,OAAO;QACb,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACvC,CAAC;IAEO,OAAO;QACb,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACvC,CAAC;IAEO,MAAM;QACZ,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,MAAM;QACZ,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAI,CAAC,GAAG,EAAE,CAAC;QACX,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,OAAO;QACb,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC5C,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACd,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,OAAO;QACb,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACd,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,OAAO;QACb,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC5C,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACd,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,OAAO;QACb,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACd,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,OAAO;QACb,MAAM,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7C,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACd,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,OAAO;QACb,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAC5C,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACd,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,eAAe;QACrB,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC/C,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACd,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,eAAe;QACrB,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC9C,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACd,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,OAAO;QACb,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7C,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACd,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,OAAO;QACb,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7C,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QACd,OAAO,KAAK,CAAC;IACf,CAAC;CACF;;;AC/2BsC;AAIvC;;;;;;;;GAQG;AACI,SAAS,MAAM,CACpB,MAA6D,EAC7D,OAAqD;IAErD,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC;IACrC,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAChC,CAAC;AAED;;;;;;GAMG;AACI,SAAS,WAAW,CACzB,MAAwC,EACxC,OAAqD;IAErD,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC;IACrC,OAAO,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACrC,CAAC;;;AClCD,6BAA6B;AAQtB,SAAS,eAAe,CAAI,MAA6B;IAC9D,OAAQ,MAAc,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC;AACvD,CAAC;AAEM,KAAK,SAAS,CAAC,CAAC,uBAAuB,CAAI,MAAyB;IACzE,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;IAElC,IAAI,CAAC;QACH,OAAO,IAAI,EAAE,CAAC;YACZ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;YAC5C,IAAI,IAAI,EAAE,CAAC;gBACT,OAAO;YACT,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;YAAS,CAAC;QACT,MAAM,CAAC,WAAW,EAAE,CAAC;IACvB,CAAC;AACH,CAAC;AAEM,SAAS,mBAAmB,CAAI,UAAiC;IACtE,IAAI,eAAe,CAAC,UAAU,CAAC,EAAE,CAAC;QAChC,OAAO,UAAU,CAAC;IACpB,CAAC;SAAM,CAAC;QACN,OAAO,uBAAuB,CAAC,UAAU,CAAC,CAAC;IAC7C,CAAC;AACH,CAAC;;;AClCsC;AACiB;AAKxD;;;GAGG;AACI,KAAK,UAAU,WAAW,CAC/B,UAAgE,EAChE,OAAqD;IAErD,MAAM,MAAM,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;IAC/C,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC;IACrC,OAAO,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACrC,CAAC;AAED;;;GAGG;AACI,SAAS,iBAAiB,CAC/B,UAAgE,EAChE,OAAqD;IAErD,MAAM,MAAM,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;IAC/C,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC;IACrC,OAAO,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;AAC3C,CAAC;AAED;;;GAGG;AACI,SAAS,iBAAiB,CAC/B,UAAgE,EAChE,OAAqD;IAErD,MAAM,MAAM,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;IAC/C,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC;IACrC,OAAO,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;AACtC,CAAC;;;AC3CD,kBAAkB;AAEmB;AACnB;AAEgC;AACnB;AAEsD;AACxB;AAEtB;AACpB;AAG4B;AACxB;AAEgB;AACpB;AAInB,iCAAiC;AAEoB;AAC3B;AAGa;AACpB;AASK;AAQtB", "sources": ["webpack://MessagePack/webpack/universalModuleDefinition", "webpack://MessagePack/webpack/bootstrap", "webpack://MessagePack/webpack/runtime/define property getters", "webpack://MessagePack/webpack/runtime/hasOwnProperty shorthand", "webpack://MessagePack/webpack/runtime/make namespace object", "webpack://MessagePack/./src/utils/utf8.ts", "webpack://MessagePack/./src/ExtData.ts", "webpack://MessagePack/./src/DecodeError.ts", "webpack://MessagePack/./src/utils/int.ts", "webpack://MessagePack/./src/timestamp.ts", "webpack://MessagePack/./src/ExtensionCodec.ts", "webpack://MessagePack/./src/utils/typedArrays.ts", "webpack://MessagePack/./src/Encoder.ts", "webpack://MessagePack/./src/encode.ts", "webpack://MessagePack/./src/utils/prettyByte.ts", "webpack://MessagePack/./src/CachedKeyDecoder.ts", "webpack://MessagePack/./src/Decoder.ts", "webpack://MessagePack/./src/decode.ts", "webpack://MessagePack/./src/utils/stream.ts", "webpack://MessagePack/./src/decodeAsync.ts", "webpack://MessagePack/./src/index.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"MessagePack\"] = factory();\n\telse\n\t\troot[\"MessagePack\"] = factory();\n})(this, () => {\nreturn ", "// The require scope\nvar __webpack_require__ = {};\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "export function utf8Count(str: string): number {\n  const strLength = str.length;\n\n  let byteLength = 0;\n  let pos = 0;\n  while (pos < strLength) {\n    let value = str.charCodeAt(pos++);\n\n    if ((value & 0xffffff80) === 0) {\n      // 1-byte\n      byteLength++;\n      continue;\n    } else if ((value & 0xfffff800) === 0) {\n      // 2-bytes\n      byteLength += 2;\n    } else {\n      // handle surrogate pair\n      if (value >= 0xd800 && value <= 0xdbff) {\n        // high surrogate\n        if (pos < strLength) {\n          const extra = str.charCodeAt(pos);\n          if ((extra & 0xfc00) === 0xdc00) {\n            ++pos;\n            value = ((value & 0x3ff) << 10) + (extra & 0x3ff) + 0x10000;\n          }\n        }\n      }\n\n      if ((value & 0xffff0000) === 0) {\n        // 3-byte\n        byteLength += 3;\n      } else {\n        // 4-byte\n        byteLength += 4;\n      }\n    }\n  }\n  return byteLength;\n}\n\nexport function utf8EncodeJs(str: string, output: Uint8Array, outputOffset: number): void {\n  const strLength = str.length;\n  let offset = outputOffset;\n  let pos = 0;\n  while (pos < strLength) {\n    let value = str.charCodeAt(pos++);\n\n    if ((value & 0xffffff80) === 0) {\n      // 1-byte\n      output[offset++] = value;\n      continue;\n    } else if ((value & 0xfffff800) === 0) {\n      // 2-bytes\n      output[offset++] = ((value >> 6) & 0x1f) | 0xc0;\n    } else {\n      // handle surrogate pair\n      if (value >= 0xd800 && value <= 0xdbff) {\n        // high surrogate\n        if (pos < strLength) {\n          const extra = str.charCodeAt(pos);\n          if ((extra & 0xfc00) === 0xdc00) {\n            ++pos;\n            value = ((value & 0x3ff) << 10) + (extra & 0x3ff) + 0x10000;\n          }\n        }\n      }\n\n      if ((value & 0xffff0000) === 0) {\n        // 3-byte\n        output[offset++] = ((value >> 12) & 0x0f) | 0xe0;\n        output[offset++] = ((value >> 6) & 0x3f) | 0x80;\n      } else {\n        // 4-byte\n        output[offset++] = ((value >> 18) & 0x07) | 0xf0;\n        output[offset++] = ((value >> 12) & 0x3f) | 0x80;\n        output[offset++] = ((value >> 6) & 0x3f) | 0x80;\n      }\n    }\n\n    output[offset++] = (value & 0x3f) | 0x80;\n  }\n}\n\n// TextEncoder and TextDecoder are standardized in whatwg encoding:\n// https://encoding.spec.whatwg.org/\n// and available in all the modern browsers:\n// https://caniuse.com/textencoder\n// They are available in Node.js since v12 LTS as well:\n// https://nodejs.org/api/globals.html#textencoder\n\nconst sharedTextEncoder = new TextEncoder();\n\n// This threshold should be determined by benchmarking, which might vary in engines and input data.\n// Run `npx ts-node benchmark/encode-string.ts` for details.\nconst TEXT_ENCODER_THRESHOLD = 50;\n\nexport function utf8EncodeTE(str: string, output: Uint8Array, outputOffset: number): void {\n  sharedTextEncoder.encodeInto(str, output.subarray(outputOffset));\n}\n\nexport function utf8Encode(str: string, output: Uint8Array, outputOffset: number): void {\n  if (str.length > TEXT_ENCODER_THRESHOLD) {\n    utf8EncodeTE(str, output, outputOffset);\n  } else {\n    utf8EncodeJs(str, output, outputOffset);\n  }\n}\n\nconst CHUNK_SIZE = 0x1_000;\n\nexport function utf8DecodeJs(bytes: Uint8Array, inputOffset: number, byteLength: number): string {\n  let offset = inputOffset;\n  const end = offset + byteLength;\n\n  const units: Array<number> = [];\n  let result = \"\";\n  while (offset < end) {\n    const byte1 = bytes[offset++]!;\n    if ((byte1 & 0x80) === 0) {\n      // 1 byte\n      units.push(byte1);\n    } else if ((byte1 & 0xe0) === 0xc0) {\n      // 2 bytes\n      const byte2 = bytes[offset++]! & 0x3f;\n      units.push(((byte1 & 0x1f) << 6) | byte2);\n    } else if ((byte1 & 0xf0) === 0xe0) {\n      // 3 bytes\n      const byte2 = bytes[offset++]! & 0x3f;\n      const byte3 = bytes[offset++]! & 0x3f;\n      units.push(((byte1 & 0x1f) << 12) | (byte2 << 6) | byte3);\n    } else if ((byte1 & 0xf8) === 0xf0) {\n      // 4 bytes\n      const byte2 = bytes[offset++]! & 0x3f;\n      const byte3 = bytes[offset++]! & 0x3f;\n      const byte4 = bytes[offset++]! & 0x3f;\n      let unit = ((byte1 & 0x07) << 0x12) | (byte2 << 0x0c) | (byte3 << 0x06) | byte4;\n      if (unit > 0xffff) {\n        unit -= 0x10000;\n        units.push(((unit >>> 10) & 0x3ff) | 0xd800);\n        unit = 0xdc00 | (unit & 0x3ff);\n      }\n      units.push(unit);\n    } else {\n      units.push(byte1);\n    }\n\n    if (units.length >= CHUNK_SIZE) {\n      result += String.fromCharCode(...units);\n      units.length = 0;\n    }\n  }\n\n  if (units.length > 0) {\n    result += String.fromCharCode(...units);\n  }\n\n  return result;\n}\n\nconst sharedTextDecoder = new TextDecoder();\n\n// This threshold should be determined by benchmarking, which might vary in engines and input data.\n// Run `npx ts-node benchmark/decode-string.ts` for details.\nconst TEXT_DECODER_THRESHOLD = 200;\n\nexport function utf8DecodeTD(bytes: Uint8Array, inputOffset: number, byteLength: number): string {\n  const stringBytes = bytes.subarray(inputOffset, inputOffset + byteLength);\n  return sharedTextDecoder.decode(stringBytes);\n}\n\nexport function utf8Decode(bytes: Uint8Array, inputOffset: number, byteLength: number): string {\n  if (byteLength > TEXT_DECODER_THRESHOLD) {\n    return utf8DecodeTD(bytes, inputOffset, byteLength);\n  } else {\n    return utf8DecodeJs(bytes, inputOffset, byteLength);\n  }\n}\n", "/**\n * ExtData is used to handle Extension Types that are not registered to ExtensionCodec.\n */\nexport class ExtData {\n  readonly type: number;\n  readonly data: Uint8Array | ((pos: number) => Uint8Array);\n\n  constructor(type: number, data: Uint8Array | ((pos: number) => Uint8Array)) {\n    this.type = type;\n    this.data = data;\n  }\n}\n", "export class DecodeError extends Error {\n  constructor(message: string) {\n    super(message);\n\n    // fix the prototype chain in a cross-platform way\n    const proto: typeof DecodeError.prototype = Object.create(DecodeError.prototype);\n    Object.setPrototypeOf(this, proto);\n\n    Object.defineProperty(this, \"name\", {\n      configurable: true,\n      enumerable: false,\n      value: DecodeError.name,\n    });\n  }\n}\n", "// Integer Utility\n\nexport const UINT32_MAX = 0xffff_ffff;\n\n// DataView extension to handle int64 / uint64,\n// where the actual range is 53-bits integer (a.k.a. safe integer)\n\nexport function setUint64(view: DataView, offset: number, value: number): void {\n  const high = value / 0x1_0000_0000;\n  const low = value; // high bits are truncated by DataView\n  view.setUint32(offset, high);\n  view.setUint32(offset + 4, low);\n}\n\nexport function setInt64(view: DataView, offset: number, value: number): void {\n  const high = Math.floor(value / 0x1_0000_0000);\n  const low = value; // high bits are truncated by DataView\n  view.setUint32(offset, high);\n  view.setUint32(offset + 4, low);\n}\n\nexport function getInt64(view: DataView, offset: number): number {\n  const high = view.getInt32(offset);\n  const low = view.getUint32(offset + 4);\n  return high * 0x1_0000_0000 + low;\n}\n\nexport function getUint64(view: <PERSON>View, offset: number): number {\n  const high = view.getUint32(offset);\n  const low = view.getUint32(offset + 4);\n  return high * 0x1_0000_0000 + low;\n}\n", "// https://github.com/msgpack/msgpack/blob/master/spec.md#timestamp-extension-type\nimport { DecodeError } from \"./DecodeError.ts\";\nimport { getInt64, setInt64 } from \"./utils/int.ts\";\n\nexport const EXT_TIMESTAMP = -1;\n\nexport type TimeSpec = {\n  sec: number;\n  nsec: number;\n};\n\nconst TIMESTAMP32_MAX_SEC = 0x100000000 - 1; // 32-bit unsigned int\nconst TIMESTAMP64_MAX_SEC = 0x400000000 - 1; // 34-bit unsigned int\n\nexport function encodeTimeSpecToTimestamp({ sec, nsec }: TimeSpec): Uint8Array {\n  if (sec >= 0 && nsec >= 0 && sec <= TIMESTAMP64_MAX_SEC) {\n    // Here sec >= 0 && nsec >= 0\n    if (nsec === 0 && sec <= TIMESTAMP32_MAX_SEC) {\n      // timestamp 32 = { sec32 (unsigned) }\n      const rv = new Uint8Array(4);\n      const view = new DataView(rv.buffer);\n      view.setUint32(0, sec);\n      return rv;\n    } else {\n      // timestamp 64 = { nsec30 (unsigned), sec34 (unsigned) }\n      const secHigh = sec / 0x100000000;\n      const secLow = sec & 0xffffffff;\n      const rv = new Uint8Array(8);\n      const view = new DataView(rv.buffer);\n      // nsec30 | secHigh2\n      view.setUint32(0, (nsec << 2) | (secHigh & 0x3));\n      // secLow32\n      view.setUint32(4, secLow);\n      return rv;\n    }\n  } else {\n    // timestamp 96 = { nsec32 (unsigned), sec64 (signed) }\n    const rv = new Uint8Array(12);\n    const view = new DataView(rv.buffer);\n    view.setUint32(0, nsec);\n    setInt64(view, 4, sec);\n    return rv;\n  }\n}\n\nexport function encodeDateToTimeSpec(date: Date): TimeSpec {\n  const msec = date.getTime();\n  const sec = Math.floor(msec / 1e3);\n  const nsec = (msec - sec * 1e3) * 1e6;\n\n  // Normalizes { sec, nsec } to ensure nsec is unsigned.\n  const nsecInSec = Math.floor(nsec / 1e9);\n  return {\n    sec: sec + nsecInSec,\n    nsec: nsec - nsecInSec * 1e9,\n  };\n}\n\nexport function encodeTimestampExtension(object: unknown): Uint8Array | null {\n  if (object instanceof Date) {\n    const timeSpec = encodeDateToTimeSpec(object);\n    return encodeTimeSpecToTimestamp(timeSpec);\n  } else {\n    return null;\n  }\n}\n\nexport function decodeTimestampToTimeSpec(data: Uint8Array): TimeSpec {\n  const view = new DataView(data.buffer, data.byteOffset, data.byteLength);\n\n  // data may be 32, 64, or 96 bits\n  switch (data.byteLength) {\n    case 4: {\n      // timestamp 32 = { sec32 }\n      const sec = view.getUint32(0);\n      const nsec = 0;\n      return { sec, nsec };\n    }\n    case 8: {\n      // timestamp 64 = { nsec30, sec34 }\n      const nsec30AndSecHigh2 = view.getUint32(0);\n      const secLow32 = view.getUint32(4);\n      const sec = (nsec30AndSecHigh2 & 0x3) * 0x100000000 + secLow32;\n      const nsec = nsec30AndSecHigh2 >>> 2;\n      return { sec, nsec };\n    }\n    case 12: {\n      // timestamp 96 = { nsec32 (unsigned), sec64 (signed) }\n\n      const sec = getInt64(view, 4);\n      const nsec = view.getUint32(0);\n      return { sec, nsec };\n    }\n    default:\n      throw new DecodeError(`Unrecognized data size for timestamp (expected 4, 8, or 12): ${data.length}`);\n  }\n}\n\nexport function decodeTimestampExtension(data: Uint8Array): Date {\n  const timeSpec = decodeTimestampToTimeSpec(data);\n  return new Date(timeSpec.sec * 1e3 + timeSpec.nsec / 1e6);\n}\n\nexport const timestampExtension = {\n  type: EXT_TIMESTAMP,\n  encode: encodeTimestampExtension,\n  decode: decodeTimestampExtension,\n};\n", "// ExtensionCodec to handle MessagePack extensions\n\nimport { ExtData } from \"./ExtData.ts\";\nimport { timestampExtension } from \"./timestamp.ts\";\n\nexport type ExtensionDecoderType<ContextType> = (\n  data: Uint8Array,\n  extensionType: number,\n  context: ContextType,\n) => unknown;\n\nexport type ExtensionEncoderType<ContextType> = (\n  input: unknown,\n  context: ContextType,\n) => Uint8Array | ((dataPos: number) => Uint8Array) | null;\n\n// immutable interface to ExtensionCodec\nexport type ExtensionCodecType<ContextType> = {\n  // eslint-disable-next-line @typescript-eslint/naming-convention\n  __brand?: ContextType;\n  tryToEncode(object: unknown, context: ContextType): ExtData | null;\n  decode(data: Uint8Array, extType: number, context: ContextType): unknown;\n};\n\nexport class ExtensionCodec<ContextType = undefined> implements ExtensionCodecType<ContextType> {\n  public static readonly defaultCodec: ExtensionCodecType<undefined> = new ExtensionCodec();\n\n  // ensures ExtensionCodecType<X> matches ExtensionCodec<X>\n  // this will make type errors a lot more clear\n  // eslint-disable-next-line @typescript-eslint/naming-convention\n  __brand?: ContextType;\n\n  // built-in extensions\n  private readonly builtInEncoders: Array<ExtensionEncoderType<ContextType> | undefined | null> = [];\n  private readonly builtInDecoders: Array<ExtensionDecoderType<ContextType> | undefined | null> = [];\n\n  // custom extensions\n  private readonly encoders: Array<ExtensionEncoderType<ContextType> | undefined | null> = [];\n  private readonly decoders: Array<ExtensionDecoderType<ContextType> | undefined | null> = [];\n\n  public constructor() {\n    this.register(timestampExtension);\n  }\n\n  public register({\n    type,\n    encode,\n    decode,\n  }: {\n    type: number;\n    encode: ExtensionEncoderType<ContextType>;\n    decode: ExtensionDecoderType<ContextType>;\n  }): void {\n    if (type >= 0) {\n      // custom extensions\n      this.encoders[type] = encode;\n      this.decoders[type] = decode;\n    } else {\n      // built-in extensions\n      const index = -1 - type;\n      this.builtInEncoders[index] = encode;\n      this.builtInDecoders[index] = decode;\n    }\n  }\n\n  public tryToEncode(object: unknown, context: ContextType): ExtData | null {\n    // built-in extensions\n    for (let i = 0; i < this.builtInEncoders.length; i++) {\n      const encodeExt = this.builtInEncoders[i];\n      if (encodeExt != null) {\n        const data = encodeExt(object, context);\n        if (data != null) {\n          const type = -1 - i;\n          return new ExtData(type, data);\n        }\n      }\n    }\n\n    // custom extensions\n    for (let i = 0; i < this.encoders.length; i++) {\n      const encodeExt = this.encoders[i];\n      if (encodeExt != null) {\n        const data = encodeExt(object, context);\n        if (data != null) {\n          const type = i;\n          return new ExtData(type, data);\n        }\n      }\n    }\n\n    if (object instanceof ExtData) {\n      // to keep ExtData as is\n      return object;\n    }\n    return null;\n  }\n\n  public decode(data: Uint8Array, type: number, context: ContextType): unknown {\n    const decodeExt = type < 0 ? this.builtInDecoders[-1 - type] : this.decoders[type];\n    if (decodeExt) {\n      return decodeExt(data, type, context);\n    } else {\n      // decode() does not fail, returns ExtData instead.\n      return new ExtData(type, data);\n    }\n  }\n}\n", "function isArrayBufferLike(buffer: unknown): buffer is ArrayBuffer<PERSON>ike {\n  return (\n    buffer instanceof ArrayBuffer || (typeof SharedArrayBuffer !== \"undefined\" && buffer instanceof SharedArrayBuffer)\n  );\n}\n\nexport function ensureUint8Array(\n  buffer: ArrayLike<number> | Uint8Array<ArrayBufferLike> | ArrayBufferView | ArrayBufferLike,\n): Uint8Array<ArrayBufferLike> {\n  if (buffer instanceof Uint8Array) {\n    return buffer;\n  } else if (ArrayBuffer.isView(buffer)) {\n    return new Uint8Array(buffer.buffer, buffer.byteOffset, buffer.byteLength);\n  } else if (isArrayBuffer<PERSON>ike(buffer)) {\n    return new Uint8Array(buffer);\n  } else {\n    // ArrayLike<number>\n    return Uint8Array.from(buffer);\n  }\n}\n", "import { utf8Count, utf8Encode } from \"./utils/utf8.ts\";\nimport { ExtensionCodec } from \"./ExtensionCodec.ts\";\nimport { setInt64, setUint64 } from \"./utils/int.ts\";\nimport { ensureUint8Array } from \"./utils/typedArrays.ts\";\nimport type { ExtData } from \"./ExtData.ts\";\nimport type { ContextOf } from \"./context.ts\";\nimport type { ExtensionCodecType } from \"./ExtensionCodec.ts\";\n\nexport const DEFAULT_MAX_DEPTH = 100;\nexport const DEFAULT_INITIAL_BUFFER_SIZE = 2048;\n\nexport type EncoderOptions<ContextType = undefined> = Partial<\n  Readonly<{\n    extensionCodec: ExtensionCodecType<ContextType>;\n\n    /**\n     * Encodes bigint as Int64 or Uint64 if it's set to true.\n     * {@link forceIntegerToFloat} does not affect bigint.\n     * Depends on ES2020's {@link DataView#setBigInt64} and\n     * {@link DataView#setBigUint64}.\n     *\n     * Defaults to false.\n     */\n    useBigInt64: boolean;\n\n    /**\n     * The maximum depth in nested objects and arrays.\n     *\n     * Defaults to 100.\n     */\n    maxDepth: number;\n\n    /**\n     * The initial size of the internal buffer.\n     *\n     * Defaults to 2048.\n     */\n    initialBufferSize: number;\n\n    /**\n     * If `true`, the keys of an object is sorted. In other words, the encoded\n     * binary is canonical and thus comparable to another encoded binary.\n     *\n     * Defaults to `false`. If enabled, it spends more time in encoding objects.\n     */\n    sortKeys: boolean;\n    /**\n     * If `true`, non-integer numbers are encoded in float32, not in float64 (the default).\n     *\n     * Only use it if precisions don't matter.\n     *\n     * Defaults to `false`.\n     */\n    forceFloat32: boolean;\n\n    /**\n     * If `true`, an object property with `undefined` value are ignored.\n     * e.g. `{ foo: undefined }` will be encoded as `{}`, as `JSON.stringify()` does.\n     *\n     * Defaults to `false`. If enabled, it spends more time in encoding objects.\n     */\n    ignoreUndefined: boolean;\n\n    /**\n     * If `true`, integer numbers are encoded as floating point numbers,\n     * with the `forceFloat32` option taken into account.\n     *\n     * Defaults to `false`.\n     */\n    forceIntegerToFloat: boolean;\n  }>\n> &\n  ContextOf<ContextType>;\n\nexport class Encoder<ContextType = undefined> {\n  private readonly extensionCodec: ExtensionCodecType<ContextType>;\n  private readonly context: ContextType;\n  private readonly useBigInt64: boolean;\n  private readonly maxDepth: number;\n  private readonly initialBufferSize: number;\n  private readonly sortKeys: boolean;\n  private readonly forceFloat32: boolean;\n  private readonly ignoreUndefined: boolean;\n  private readonly forceIntegerToFloat: boolean;\n\n  private pos: number;\n  private view: DataView;\n  private bytes: Uint8Array;\n\n  private entered = false;\n\n  public constructor(options?: EncoderOptions<ContextType>) {\n    this.extensionCodec = options?.extensionCodec ?? (ExtensionCodec.defaultCodec as ExtensionCodecType<ContextType>);\n    this.context = (options as { context: ContextType } | undefined)?.context as ContextType; // needs a type assertion because EncoderOptions has no context property when ContextType is undefined\n\n    this.useBigInt64 = options?.useBigInt64 ?? false;\n    this.maxDepth = options?.maxDepth ?? DEFAULT_MAX_DEPTH;\n    this.initialBufferSize = options?.initialBufferSize ?? DEFAULT_INITIAL_BUFFER_SIZE;\n    this.sortKeys = options?.sortKeys ?? false;\n    this.forceFloat32 = options?.forceFloat32 ?? false;\n    this.ignoreUndefined = options?.ignoreUndefined ?? false;\n    this.forceIntegerToFloat = options?.forceIntegerToFloat ?? false;\n\n    this.pos = 0;\n    this.view = new DataView(new ArrayBuffer(this.initialBufferSize));\n    this.bytes = new Uint8Array(this.view.buffer);\n  }\n\n  private clone() {\n    // Because of slightly special argument `context`,\n    // type assertion is needed.\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n    return new Encoder<ContextType>({\n      extensionCodec: this.extensionCodec,\n      context: this.context,\n      useBigInt64: this.useBigInt64,\n      maxDepth: this.maxDepth,\n      initialBufferSize: this.initialBufferSize,\n      sortKeys: this.sortKeys,\n      forceFloat32: this.forceFloat32,\n      ignoreUndefined: this.ignoreUndefined,\n      forceIntegerToFloat: this.forceIntegerToFloat,\n    } as any);\n  }\n\n  private reinitializeState() {\n    this.pos = 0;\n  }\n\n  /**\n   * This is almost equivalent to {@link Encoder#encode}, but it returns an reference of the encoder's internal buffer and thus much faster than {@link Encoder#encode}.\n   *\n   * @returns Encodes the object and returns a shared reference the encoder's internal buffer.\n   */\n  public encodeSharedRef(object: unknown): Uint8Array {\n    if (this.entered) {\n      const instance = this.clone();\n      return instance.encodeSharedRef(object);\n    }\n\n    try {\n      this.entered = true;\n\n      this.reinitializeState();\n      this.doEncode(object, 1);\n      return this.bytes.subarray(0, this.pos);\n    } finally {\n      this.entered = false;\n    }\n  }\n\n  /**\n   * @returns Encodes the object and returns a copy of the encoder's internal buffer.\n   */\n  public encode(object: unknown): Uint8Array {\n    if (this.entered) {\n      const instance = this.clone();\n      return instance.encode(object);\n    }\n\n    try {\n      this.entered = true;\n\n      this.reinitializeState();\n      this.doEncode(object, 1);\n      return this.bytes.slice(0, this.pos);\n    } finally {\n      this.entered = false;\n    }\n  }\n\n  private doEncode(object: unknown, depth: number): void {\n    if (depth > this.maxDepth) {\n      throw new Error(`Too deep objects in depth ${depth}`);\n    }\n\n    if (object == null) {\n      this.encodeNil();\n    } else if (typeof object === \"boolean\") {\n      this.encodeBoolean(object);\n    } else if (typeof object === \"number\") {\n      if (!this.forceIntegerToFloat) {\n        this.encodeNumber(object);\n      } else {\n        this.encodeNumberAsFloat(object);\n      }\n    } else if (typeof object === \"string\") {\n      this.encodeString(object);\n    } else if (this.useBigInt64 && typeof object === \"bigint\") {\n      this.encodeBigInt64(object);\n    } else {\n      this.encodeObject(object, depth);\n    }\n  }\n\n  private ensureBufferSizeToWrite(sizeToWrite: number) {\n    const requiredSize = this.pos + sizeToWrite;\n\n    if (this.view.byteLength < requiredSize) {\n      this.resizeBuffer(requiredSize * 2);\n    }\n  }\n\n  private resizeBuffer(newSize: number) {\n    const newBuffer = new ArrayBuffer(newSize);\n    const newBytes = new Uint8Array(newBuffer);\n    const newView = new DataView(newBuffer);\n\n    newBytes.set(this.bytes);\n\n    this.view = newView;\n    this.bytes = newBytes;\n  }\n\n  private encodeNil() {\n    this.writeU8(0xc0);\n  }\n\n  private encodeBoolean(object: boolean) {\n    if (object === false) {\n      this.writeU8(0xc2);\n    } else {\n      this.writeU8(0xc3);\n    }\n  }\n\n  private encodeNumber(object: number): void {\n    if (!this.forceIntegerToFloat && Number.isSafeInteger(object)) {\n      if (object >= 0) {\n        if (object < 0x80) {\n          // positive fixint\n          this.writeU8(object);\n        } else if (object < 0x100) {\n          // uint 8\n          this.writeU8(0xcc);\n          this.writeU8(object);\n        } else if (object < 0x10000) {\n          // uint 16\n          this.writeU8(0xcd);\n          this.writeU16(object);\n        } else if (object < 0x100000000) {\n          // uint 32\n          this.writeU8(0xce);\n          this.writeU32(object);\n        } else if (!this.useBigInt64) {\n          // uint 64\n          this.writeU8(0xcf);\n          this.writeU64(object);\n        } else {\n          this.encodeNumberAsFloat(object);\n        }\n      } else {\n        if (object >= -0x20) {\n          // negative fixint\n          this.writeU8(0xe0 | (object + 0x20));\n        } else if (object >= -0x80) {\n          // int 8\n          this.writeU8(0xd0);\n          this.writeI8(object);\n        } else if (object >= -0x8000) {\n          // int 16\n          this.writeU8(0xd1);\n          this.writeI16(object);\n        } else if (object >= -0x80000000) {\n          // int 32\n          this.writeU8(0xd2);\n          this.writeI32(object);\n        } else if (!this.useBigInt64) {\n          // int 64\n          this.writeU8(0xd3);\n          this.writeI64(object);\n        } else {\n          this.encodeNumberAsFloat(object);\n        }\n      }\n    } else {\n      this.encodeNumberAsFloat(object);\n    }\n  }\n\n  private encodeNumberAsFloat(object: number): void {\n    if (this.forceFloat32) {\n      // float 32\n      this.writeU8(0xca);\n      this.writeF32(object);\n    } else {\n      // float 64\n      this.writeU8(0xcb);\n      this.writeF64(object);\n    }\n  }\n\n  private encodeBigInt64(object: bigint): void {\n    if (object >= BigInt(0)) {\n      // uint 64\n      this.writeU8(0xcf);\n      this.writeBigUint64(object);\n    } else {\n      // int 64\n      this.writeU8(0xd3);\n      this.writeBigInt64(object);\n    }\n  }\n\n  private writeStringHeader(byteLength: number) {\n    if (byteLength < 32) {\n      // fixstr\n      this.writeU8(0xa0 + byteLength);\n    } else if (byteLength < 0x100) {\n      // str 8\n      this.writeU8(0xd9);\n      this.writeU8(byteLength);\n    } else if (byteLength < 0x10000) {\n      // str 16\n      this.writeU8(0xda);\n      this.writeU16(byteLength);\n    } else if (byteLength < 0x100000000) {\n      // str 32\n      this.writeU8(0xdb);\n      this.writeU32(byteLength);\n    } else {\n      throw new Error(`Too long string: ${byteLength} bytes in UTF-8`);\n    }\n  }\n\n  private encodeString(object: string) {\n    const maxHeaderSize = 1 + 4;\n\n    const byteLength = utf8Count(object);\n    this.ensureBufferSizeToWrite(maxHeaderSize + byteLength);\n    this.writeStringHeader(byteLength);\n    utf8Encode(object, this.bytes, this.pos);\n    this.pos += byteLength;\n  }\n\n  private encodeObject(object: unknown, depth: number) {\n    // try to encode objects with custom codec first of non-primitives\n    const ext = this.extensionCodec.tryToEncode(object, this.context);\n    if (ext != null) {\n      this.encodeExtension(ext);\n    } else if (Array.isArray(object)) {\n      this.encodeArray(object, depth);\n    } else if (ArrayBuffer.isView(object)) {\n      this.encodeBinary(object);\n    } else if (typeof object === \"object\") {\n      this.encodeMap(object as Record<string, unknown>, depth);\n    } else {\n      // symbol, function and other special object come here unless extensionCodec handles them.\n      throw new Error(`Unrecognized object: ${Object.prototype.toString.apply(object)}`);\n    }\n  }\n\n  private encodeBinary(object: ArrayBufferView) {\n    const size = object.byteLength;\n    if (size < 0x100) {\n      // bin 8\n      this.writeU8(0xc4);\n      this.writeU8(size);\n    } else if (size < 0x10000) {\n      // bin 16\n      this.writeU8(0xc5);\n      this.writeU16(size);\n    } else if (size < 0x100000000) {\n      // bin 32\n      this.writeU8(0xc6);\n      this.writeU32(size);\n    } else {\n      throw new Error(`Too large binary: ${size}`);\n    }\n    const bytes = ensureUint8Array(object);\n    this.writeU8a(bytes);\n  }\n\n  private encodeArray(object: Array<unknown>, depth: number) {\n    const size = object.length;\n    if (size < 16) {\n      // fixarray\n      this.writeU8(0x90 + size);\n    } else if (size < 0x10000) {\n      // array 16\n      this.writeU8(0xdc);\n      this.writeU16(size);\n    } else if (size < 0x100000000) {\n      // array 32\n      this.writeU8(0xdd);\n      this.writeU32(size);\n    } else {\n      throw new Error(`Too large array: ${size}`);\n    }\n    for (const item of object) {\n      this.doEncode(item, depth + 1);\n    }\n  }\n\n  private countWithoutUndefined(object: Record<string, unknown>, keys: ReadonlyArray<string>): number {\n    let count = 0;\n\n    for (const key of keys) {\n      if (object[key] !== undefined) {\n        count++;\n      }\n    }\n\n    return count;\n  }\n\n  private encodeMap(object: Record<string, unknown>, depth: number) {\n    const keys = Object.keys(object);\n    if (this.sortKeys) {\n      keys.sort();\n    }\n\n    const size = this.ignoreUndefined ? this.countWithoutUndefined(object, keys) : keys.length;\n\n    if (size < 16) {\n      // fixmap\n      this.writeU8(0x80 + size);\n    } else if (size < 0x10000) {\n      // map 16\n      this.writeU8(0xde);\n      this.writeU16(size);\n    } else if (size < 0x100000000) {\n      // map 32\n      this.writeU8(0xdf);\n      this.writeU32(size);\n    } else {\n      throw new Error(`Too large map object: ${size}`);\n    }\n\n    for (const key of keys) {\n      const value = object[key];\n\n      if (!(this.ignoreUndefined && value === undefined)) {\n        this.encodeString(key);\n        this.doEncode(value, depth + 1);\n      }\n    }\n  }\n\n  private encodeExtension(ext: ExtData) {\n    if (typeof ext.data === \"function\") {\n      const data = ext.data(this.pos + 6);\n      const size = data.length;\n\n      if (size >= 0x100000000) {\n        throw new Error(`Too large extension object: ${size}`);\n      }\n\n      this.writeU8(0xc9);\n      this.writeU32(size);\n      this.writeI8(ext.type);\n      this.writeU8a(data);\n      return;\n    }\n\n    const size = ext.data.length;\n    if (size === 1) {\n      // fixext 1\n      this.writeU8(0xd4);\n    } else if (size === 2) {\n      // fixext 2\n      this.writeU8(0xd5);\n    } else if (size === 4) {\n      // fixext 4\n      this.writeU8(0xd6);\n    } else if (size === 8) {\n      // fixext 8\n      this.writeU8(0xd7);\n    } else if (size === 16) {\n      // fixext 16\n      this.writeU8(0xd8);\n    } else if (size < 0x100) {\n      // ext 8\n      this.writeU8(0xc7);\n      this.writeU8(size);\n    } else if (size < 0x10000) {\n      // ext 16\n      this.writeU8(0xc8);\n      this.writeU16(size);\n    } else if (size < 0x100000000) {\n      // ext 32\n      this.writeU8(0xc9);\n      this.writeU32(size);\n    } else {\n      throw new Error(`Too large extension object: ${size}`);\n    }\n    this.writeI8(ext.type);\n    this.writeU8a(ext.data);\n  }\n\n  private writeU8(value: number) {\n    this.ensureBufferSizeToWrite(1);\n\n    this.view.setUint8(this.pos, value);\n    this.pos++;\n  }\n\n  private writeU8a(values: ArrayLike<number>) {\n    const size = values.length;\n    this.ensureBufferSizeToWrite(size);\n\n    this.bytes.set(values, this.pos);\n    this.pos += size;\n  }\n\n  private writeI8(value: number) {\n    this.ensureBufferSizeToWrite(1);\n\n    this.view.setInt8(this.pos, value);\n    this.pos++;\n  }\n\n  private writeU16(value: number) {\n    this.ensureBufferSizeToWrite(2);\n\n    this.view.setUint16(this.pos, value);\n    this.pos += 2;\n  }\n\n  private writeI16(value: number) {\n    this.ensureBufferSizeToWrite(2);\n\n    this.view.setInt16(this.pos, value);\n    this.pos += 2;\n  }\n\n  private writeU32(value: number) {\n    this.ensureBufferSizeToWrite(4);\n\n    this.view.setUint32(this.pos, value);\n    this.pos += 4;\n  }\n\n  private writeI32(value: number) {\n    this.ensureBufferSizeToWrite(4);\n\n    this.view.setInt32(this.pos, value);\n    this.pos += 4;\n  }\n\n  private writeF32(value: number) {\n    this.ensureBufferSizeToWrite(4);\n\n    this.view.setFloat32(this.pos, value);\n    this.pos += 4;\n  }\n\n  private writeF64(value: number) {\n    this.ensureBufferSizeToWrite(8);\n\n    this.view.setFloat64(this.pos, value);\n    this.pos += 8;\n  }\n\n  private writeU64(value: number) {\n    this.ensureBufferSizeToWrite(8);\n\n    setUint64(this.view, this.pos, value);\n    this.pos += 8;\n  }\n\n  private writeI64(value: number) {\n    this.ensureBufferSizeToWrite(8);\n\n    setInt64(this.view, this.pos, value);\n    this.pos += 8;\n  }\n\n  private writeBigUint64(value: bigint) {\n    this.ensureBufferSizeToWrite(8);\n\n    this.view.setBigUint64(this.pos, value);\n    this.pos += 8;\n  }\n\n  private writeBigInt64(value: bigint) {\n    this.ensureBufferSizeToWrite(8);\n\n    this.view.setBigInt64(this.pos, value);\n    this.pos += 8;\n  }\n}\n", "import { Encoder } from \"./Encoder.ts\";\nimport type { EncoderOptions } from \"./Encoder.ts\";\nimport type { SplitUndefined } from \"./context.ts\";\n\n/**\n * It encodes `value` in the MessagePack format and\n * returns a byte buffer.\n *\n * The returned buffer is a slice of a larger `ArrayBuffer`, so you have to use its `#byteOffset` and `#byteLength` in order to convert it to another typed arrays including NodeJS `Buffer`.\n */\nexport function encode<ContextType = undefined>(\n  value: unknown,\n  options?: EncoderOptions<SplitUndefined<ContextType>>,\n): Uint8Array {\n  const encoder = new Encoder(options);\n  return encoder.encodeSharedRef(value);\n}\n", "export function prettyByte(byte: number): string {\n  return `${byte < 0 ? \"-\" : \"\"}0x${Math.abs(byte).toString(16).padStart(2, \"0\")}`;\n}\n", "import { utf8DecodeJs } from \"./utils/utf8.ts\";\n\nconst DEFAULT_MAX_KEY_LENGTH = 16;\nconst DEFAULT_MAX_LENGTH_PER_KEY = 16;\n\nexport interface KeyDecoder {\n  canBeCached(byteLength: number): boolean;\n  decode(bytes: Uint8Array, inputOffset: number, byteLength: number): string;\n}\ninterface KeyCacheRecord {\n  readonly bytes: Uint8Array;\n  readonly str: string;\n}\n\nexport class CachedKeyDecoder implements KeyDecoder {\n  hit = 0;\n  miss = 0;\n  private readonly caches: Array<Array<KeyCacheRecord>>;\n  readonly maxKeyLength: number;\n  readonly maxLengthPerKey: number;\n\n  constructor(maxKeyLength = DEFAULT_MAX_KEY_LENGTH, maxLengthPerKey = DEFAULT_MAX_LENGTH_PER_KEY) {\n    this.maxKeyLength = maxKeyLength;\n    this.maxLengthPerKey = maxLengthPerKey;\n\n    // avoid `new Array(N)`, which makes a sparse array,\n    // because a sparse array is typically slower than a non-sparse array.\n    this.caches = [];\n    for (let i = 0; i < this.maxKeyLength; i++) {\n      this.caches.push([]);\n    }\n  }\n\n  public canBeCached(byteLength: number): boolean {\n    return byteLength > 0 && byteLength <= this.maxKeyLength;\n  }\n\n  private find(bytes: Uint8Array, inputOffset: number, byteLength: number): string | null {\n    const records = this.caches[byteLength - 1]!;\n\n    FIND_CHUNK: for (const record of records) {\n      const recordBytes = record.bytes;\n\n      for (let j = 0; j < byteLength; j++) {\n        if (recordBytes[j] !== bytes[inputOffset + j]) {\n          continue FIND_CHUNK;\n        }\n      }\n      return record.str;\n    }\n    return null;\n  }\n\n  private store(bytes: Uint8Array, value: string) {\n    const records = this.caches[bytes.length - 1]!;\n    const record: KeyCacheRecord = { bytes, str: value };\n\n    if (records.length >= this.maxLengthPerKey) {\n      // `records` are full!\n      // Set `record` to an arbitrary position.\n      records[(Math.random() * records.length) | 0] = record;\n    } else {\n      records.push(record);\n    }\n  }\n\n  public decode(bytes: Uint8Array, inputOffset: number, byteLength: number): string {\n    const cachedValue = this.find(bytes, inputOffset, byteLength);\n    if (cachedValue != null) {\n      this.hit++;\n      return cachedValue;\n    }\n    this.miss++;\n\n    const str = utf8DecodeJs(bytes, inputOffset, byteLength);\n    // Ensure to copy a slice of bytes because the bytes may be a NodeJS Buffer and Buffer#slice() returns a reference to its internal ArrayBuffer.\n    const slicedCopyOfBytes = Uint8Array.prototype.slice.call(bytes, inputOffset, inputOffset + byteLength);\n    this.store(slicedCopyOfBytes, str);\n    return str;\n  }\n}\n", "import { prettyByte } from \"./utils/prettyByte.ts\";\nimport { ExtensionCodec } from \"./ExtensionCodec.ts\";\nimport { getInt64, getUint64, UINT32_MAX } from \"./utils/int.ts\";\nimport { utf8Decode } from \"./utils/utf8.ts\";\nimport { ensureUint8Array } from \"./utils/typedArrays.ts\";\nimport { CachedKeyDecoder } from \"./CachedKeyDecoder.ts\";\nimport { DecodeError } from \"./DecodeError.ts\";\nimport type { ContextOf } from \"./context.ts\";\nimport type { ExtensionCodecType } from \"./ExtensionCodec.ts\";\nimport type { KeyDecoder } from \"./CachedKeyDecoder.ts\";\n\nexport type DecoderOptions<ContextType = undefined> = Readonly<\n  Partial<{\n    extensionCodec: ExtensionCodecType<ContextType>;\n\n    /**\n     * Decodes Int64 and Uint64 as bigint if it's set to true.\n     * Depends on ES2020's {@link DataView#getBigInt64} and\n     * {@link DataView#getBigUint64}.\n     *\n     * Defaults to false.\n     */\n    useBigInt64: boolean;\n\n    /**\n     * By default, string values will be decoded as UTF-8 strings. However, if this option is true,\n     * string values will be returned as Uint8Arrays without additional decoding.\n     *\n     * This is useful if the strings may contain invalid UTF-8 sequences.\n     *\n     * Note that this option only applies to string values, not map keys. Additionally, when\n     * enabled, raw string length is limited by the maxBinLength option.\n     */\n    rawStrings: boolean;\n\n    /**\n     * Maximum string length.\n     *\n     * Defaults to 4_294_967_295 (UINT32_MAX).\n     */\n    maxStrLength: number;\n    /**\n     * Maximum binary length.\n     *\n     * Defaults to 4_294_967_295 (UINT32_MAX).\n     */\n    maxBinLength: number;\n    /**\n     * Maximum array length.\n     *\n     * Defaults to 4_294_967_295 (UINT32_MAX).\n     */\n    maxArrayLength: number;\n    /**\n     * Maximum map length.\n     *\n     * Defaults to 4_294_967_295 (UINT32_MAX).\n     */\n    maxMapLength: number;\n    /**\n     * Maximum extension length.\n     *\n     * Defaults to 4_294_967_295 (UINT32_MAX).\n     */\n    maxExtLength: number;\n\n    /**\n     * An object key decoder. Defaults to the shared instance of {@link CachedKeyDecoder}.\n     * `null` is a special value to disable the use of the key decoder at all.\n     */\n    keyDecoder: KeyDecoder | null;\n\n    /**\n     * A function to convert decoded map key to a valid JS key type.\n     *\n     * Defaults to a function that throws an error if the key is not a string or a number.\n     */\n    mapKeyConverter: (key: unknown) => MapKeyType;\n  }>\n> &\n  ContextOf<ContextType>;\n\nconst STATE_ARRAY = \"array\";\nconst STATE_MAP_KEY = \"map_key\";\nconst STATE_MAP_VALUE = \"map_value\";\n\ntype MapKeyType = string | number;\n\nconst mapKeyConverter = (key: unknown): MapKeyType => {\n  if (typeof key === \"string\" || typeof key === \"number\") {\n    return key;\n  }\n  throw new DecodeError(\"The type of key must be string or number but \" + typeof key);\n};\n\ntype StackMapState = {\n  type: typeof STATE_MAP_KEY | typeof STATE_MAP_VALUE;\n  size: number;\n  key: MapKeyType | null;\n  readCount: number;\n  map: Record<string, unknown>;\n};\n\ntype StackArrayState = {\n  type: typeof STATE_ARRAY;\n  size: number;\n  array: Array<unknown>;\n  position: number;\n};\n\nclass StackPool {\n  private readonly stack: Array<StackState> = [];\n  private stackHeadPosition = -1;\n\n  public get length(): number {\n    return this.stackHeadPosition + 1;\n  }\n\n  public top(): StackState | undefined {\n    return this.stack[this.stackHeadPosition];\n  }\n\n  public pushArrayState(size: number) {\n    const state = this.getUninitializedStateFromPool() as StackArrayState;\n\n    state.type = STATE_ARRAY;\n    state.position = 0;\n    state.size = size;\n    state.array = new Array(size);\n  }\n\n  public pushMapState(size: number) {\n    const state = this.getUninitializedStateFromPool() as StackMapState;\n\n    state.type = STATE_MAP_KEY;\n    state.readCount = 0;\n    state.size = size;\n    state.map = {};\n  }\n\n  private getUninitializedStateFromPool() {\n    this.stackHeadPosition++;\n\n    if (this.stackHeadPosition === this.stack.length) {\n      const partialState: Partial<StackState> = {\n        type: undefined,\n        size: 0,\n        array: undefined,\n        position: 0,\n        readCount: 0,\n        map: undefined,\n        key: null,\n      };\n\n      this.stack.push(partialState as StackState);\n    }\n\n    return this.stack[this.stackHeadPosition];\n  }\n\n  public release(state: StackState): void {\n    const topStackState = this.stack[this.stackHeadPosition];\n\n    if (topStackState !== state) {\n      throw new Error(\"Invalid stack state. Released state is not on top of the stack.\");\n    }\n\n    if (state.type === STATE_ARRAY) {\n      const partialState = state as Partial<StackArrayState>;\n      partialState.size = 0;\n      partialState.array = undefined;\n      partialState.position = 0;\n      partialState.type = undefined;\n    }\n\n    if (state.type === STATE_MAP_KEY || state.type === STATE_MAP_VALUE) {\n      const partialState = state as Partial<StackMapState>;\n      partialState.size = 0;\n      partialState.map = undefined;\n      partialState.readCount = 0;\n      partialState.type = undefined;\n    }\n\n    this.stackHeadPosition--;\n  }\n\n  public reset(): void {\n    this.stack.length = 0;\n    this.stackHeadPosition = -1;\n  }\n}\n\ntype StackState = StackArrayState | StackMapState;\n\nconst HEAD_BYTE_REQUIRED = -1;\n\nconst EMPTY_VIEW = new DataView<ArrayBufferLike>(new ArrayBuffer(0));\nconst EMPTY_BYTES = new Uint8Array<ArrayBufferLike>(EMPTY_VIEW.buffer);\n\ntry {\n  // IE11: The spec says it should throw RangeError,\n  // IE11: but in IE11 it throws TypeError.\n  EMPTY_VIEW.getInt8(0);\n} catch (e) {\n  if (!(e instanceof RangeError)) {\n    throw new Error(\n      \"This module is not supported in the current JavaScript engine because DataView does not throw RangeError on out-of-bounds access\",\n    );\n  }\n}\n\nconst MORE_DATA = new RangeError(\"Insufficient data\");\n\nconst sharedCachedKeyDecoder = new CachedKeyDecoder();\n\nexport class Decoder<ContextType = undefined> {\n  private readonly extensionCodec: ExtensionCodecType<ContextType>;\n  private readonly context: ContextType;\n  private readonly useBigInt64: boolean;\n  private readonly rawStrings: boolean;\n  private readonly maxStrLength: number;\n  private readonly maxBinLength: number;\n  private readonly maxArrayLength: number;\n  private readonly maxMapLength: number;\n  private readonly maxExtLength: number;\n  private readonly keyDecoder: KeyDecoder | null;\n  private readonly mapKeyConverter: (key: unknown) => MapKeyType;\n\n  private totalPos = 0;\n  private pos = 0;\n\n  private view = EMPTY_VIEW;\n  private bytes = EMPTY_BYTES;\n  private headByte = HEAD_BYTE_REQUIRED;\n  private readonly stack = new StackPool();\n\n  private entered = false;\n\n  public constructor(options?: DecoderOptions<ContextType>) {\n    this.extensionCodec = options?.extensionCodec ?? (ExtensionCodec.defaultCodec as ExtensionCodecType<ContextType>);\n    this.context = (options as { context: ContextType } | undefined)?.context as ContextType; // needs a type assertion because EncoderOptions has no context property when ContextType is undefined\n\n    this.useBigInt64 = options?.useBigInt64 ?? false;\n    this.rawStrings = options?.rawStrings ?? false;\n    this.maxStrLength = options?.maxStrLength ?? UINT32_MAX;\n    this.maxBinLength = options?.maxBinLength ?? UINT32_MAX;\n    this.maxArrayLength = options?.maxArrayLength ?? UINT32_MAX;\n    this.maxMapLength = options?.maxMapLength ?? UINT32_MAX;\n    this.maxExtLength = options?.maxExtLength ?? UINT32_MAX;\n    this.keyDecoder = options?.keyDecoder !== undefined ? options.keyDecoder : sharedCachedKeyDecoder;\n    this.mapKeyConverter = options?.mapKeyConverter ?? mapKeyConverter;\n  }\n\n  private clone(): Decoder<ContextType> {\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n    return new Decoder({\n      extensionCodec: this.extensionCodec,\n      context: this.context,\n      useBigInt64: this.useBigInt64,\n      rawStrings: this.rawStrings,\n      maxStrLength: this.maxStrLength,\n      maxBinLength: this.maxBinLength,\n      maxArrayLength: this.maxArrayLength,\n      maxMapLength: this.maxMapLength,\n      maxExtLength: this.maxExtLength,\n      keyDecoder: this.keyDecoder,\n    } as any);\n  }\n\n  private reinitializeState() {\n    this.totalPos = 0;\n    this.headByte = HEAD_BYTE_REQUIRED;\n    this.stack.reset();\n\n    // view, bytes, and pos will be re-initialized in setBuffer()\n  }\n\n  private setBuffer(buffer: ArrayLike<number> | ArrayBufferView | ArrayBufferLike): void {\n    const bytes = ensureUint8Array(buffer);\n    this.bytes = bytes;\n    this.view = new DataView(bytes.buffer, bytes.byteOffset, bytes.byteLength);\n    this.pos = 0;\n  }\n\n  private appendBuffer(buffer: ArrayLike<number> | ArrayBufferView | ArrayBufferLike): void {\n    if (this.headByte === HEAD_BYTE_REQUIRED && !this.hasRemaining(1)) {\n      this.setBuffer(buffer);\n    } else {\n      const remainingData = this.bytes.subarray(this.pos);\n      const newData = ensureUint8Array(buffer);\n\n      // concat remainingData + newData\n      const newBuffer = new Uint8Array(remainingData.length + newData.length);\n      newBuffer.set(remainingData);\n      newBuffer.set(newData, remainingData.length);\n      this.setBuffer(newBuffer);\n    }\n  }\n\n  private hasRemaining(size: number) {\n    return this.view.byteLength - this.pos >= size;\n  }\n\n  private createExtraByteError(posToShow: number): Error {\n    const { view, pos } = this;\n    return new RangeError(`Extra ${view.byteLength - pos} of ${view.byteLength} byte(s) found at buffer[${posToShow}]`);\n  }\n\n  /**\n   * @throws {@link DecodeError}\n   * @throws {@link RangeError}\n   */\n  public decode(buffer: ArrayLike<number> | ArrayBufferView | ArrayBufferLike): unknown {\n    if (this.entered) {\n      const instance = this.clone();\n      return instance.decode(buffer);\n    }\n\n    try {\n      this.entered = true;\n\n      this.reinitializeState();\n      this.setBuffer(buffer);\n\n      const object = this.doDecodeSync();\n      if (this.hasRemaining(1)) {\n        throw this.createExtraByteError(this.pos);\n      }\n      return object;\n    } finally {\n      this.entered = false;\n    }\n  }\n\n  public *decodeMulti(buffer: ArrayLike<number> | ArrayBufferView | ArrayBufferLike): Generator<unknown, void, unknown> {\n    if (this.entered) {\n      const instance = this.clone();\n      yield* instance.decodeMulti(buffer);\n      return;\n    }\n\n    try {\n      this.entered = true;\n\n      this.reinitializeState();\n      this.setBuffer(buffer);\n\n      while (this.hasRemaining(1)) {\n        yield this.doDecodeSync();\n      }\n    } finally {\n      this.entered = false;\n    }\n  }\n\n  public async decodeAsync(stream: AsyncIterable<ArrayLike<number> | ArrayBufferView | ArrayBufferLike>): Promise<unknown> {\n    if (this.entered) {\n      const instance = this.clone();\n      return instance.decodeAsync(stream);\n    }\n\n    try {\n      this.entered = true;\n\n      let decoded = false;\n      let object: unknown;\n      for await (const buffer of stream) {\n        if (decoded) {\n          this.entered = false;\n          throw this.createExtraByteError(this.totalPos);\n        }\n\n        this.appendBuffer(buffer);\n\n        try {\n          object = this.doDecodeSync();\n          decoded = true;\n        } catch (e) {\n          if (!(e instanceof RangeError)) {\n            throw e; // rethrow\n          }\n          // fallthrough\n        }\n        this.totalPos += this.pos;\n      }\n\n      if (decoded) {\n        if (this.hasRemaining(1)) {\n          throw this.createExtraByteError(this.totalPos);\n        }\n        return object;\n      }\n\n      const { headByte, pos, totalPos } = this;\n      throw new RangeError(\n        `Insufficient data in parsing ${prettyByte(headByte)} at ${totalPos} (${pos} in the current buffer)`,\n      );\n    } finally {\n      this.entered = false;\n    }\n  }\n\n  public decodeArrayStream(\n    stream: AsyncIterable<ArrayLike<number> | ArrayBufferView | ArrayBufferLike>,\n  ): AsyncGenerator<unknown, void, unknown> {\n    return this.decodeMultiAsync(stream, true);\n  }\n\n  public decodeStream(stream: AsyncIterable<ArrayLike<number> | ArrayBufferView | ArrayBufferLike>): AsyncGenerator<unknown, void, unknown> {\n    return this.decodeMultiAsync(stream, false);\n  }\n\n  private async *decodeMultiAsync(stream: AsyncIterable<ArrayLike<number> | ArrayBufferView | ArrayBufferLike>, isArray: boolean): AsyncGenerator<unknown, void, unknown> {\n    if (this.entered) {\n      const instance = this.clone();\n      yield* instance.decodeMultiAsync(stream, isArray);\n      return;\n    }\n\n    try {\n      this.entered = true;\n\n      let isArrayHeaderRequired = isArray;\n      let arrayItemsLeft = -1;\n\n      for await (const buffer of stream) {\n        if (isArray && arrayItemsLeft === 0) {\n          throw this.createExtraByteError(this.totalPos);\n        }\n\n        this.appendBuffer(buffer);\n\n        if (isArrayHeaderRequired) {\n          arrayItemsLeft = this.readArraySize();\n          isArrayHeaderRequired = false;\n          this.complete();\n        }\n\n        try {\n          while (true) {\n            yield this.doDecodeSync();\n            if (--arrayItemsLeft === 0) {\n              break;\n            }\n          }\n        } catch (e) {\n          if (!(e instanceof RangeError)) {\n            throw e; // rethrow\n          }\n          // fallthrough\n        }\n        this.totalPos += this.pos;\n      }\n    } finally {\n      this.entered = false;\n    }\n  }\n\n  private doDecodeSync(): unknown {\n    DECODE: while (true) {\n      const headByte = this.readHeadByte();\n      let object: unknown;\n\n      if (headByte >= 0xe0) {\n        // negative fixint (111x xxxx) 0xe0 - 0xff\n        object = headByte - 0x100;\n      } else if (headByte < 0xc0) {\n        if (headByte < 0x80) {\n          // positive fixint (0xxx xxxx) 0x00 - 0x7f\n          object = headByte;\n        } else if (headByte < 0x90) {\n          // fixmap (1000 xxxx) 0x80 - 0x8f\n          const size = headByte - 0x80;\n          if (size !== 0) {\n            this.pushMapState(size);\n            this.complete();\n            continue DECODE;\n          } else {\n            object = {};\n          }\n        } else if (headByte < 0xa0) {\n          // fixarray (1001 xxxx) 0x90 - 0x9f\n          const size = headByte - 0x90;\n          if (size !== 0) {\n            this.pushArrayState(size);\n            this.complete();\n            continue DECODE;\n          } else {\n            object = [];\n          }\n        } else {\n          // fixstr (101x xxxx) 0xa0 - 0xbf\n          const byteLength = headByte - 0xa0;\n          object = this.decodeString(byteLength, 0);\n        }\n      } else if (headByte === 0xc0) {\n        // nil\n        object = null;\n      } else if (headByte === 0xc2) {\n        // false\n        object = false;\n      } else if (headByte === 0xc3) {\n        // true\n        object = true;\n      } else if (headByte === 0xca) {\n        // float 32\n        object = this.readF32();\n      } else if (headByte === 0xcb) {\n        // float 64\n        object = this.readF64();\n      } else if (headByte === 0xcc) {\n        // uint 8\n        object = this.readU8();\n      } else if (headByte === 0xcd) {\n        // uint 16\n        object = this.readU16();\n      } else if (headByte === 0xce) {\n        // uint 32\n        object = this.readU32();\n      } else if (headByte === 0xcf) {\n        // uint 64\n        if (this.useBigInt64) {\n          object = this.readU64AsBigInt();\n        } else {\n          object = this.readU64();\n        }\n      } else if (headByte === 0xd0) {\n        // int 8\n        object = this.readI8();\n      } else if (headByte === 0xd1) {\n        // int 16\n        object = this.readI16();\n      } else if (headByte === 0xd2) {\n        // int 32\n        object = this.readI32();\n      } else if (headByte === 0xd3) {\n        // int 64\n        if (this.useBigInt64) {\n          object = this.readI64AsBigInt();\n        } else {\n          object = this.readI64();\n        }\n      } else if (headByte === 0xd9) {\n        // str 8\n        const byteLength = this.lookU8();\n        object = this.decodeString(byteLength, 1);\n      } else if (headByte === 0xda) {\n        // str 16\n        const byteLength = this.lookU16();\n        object = this.decodeString(byteLength, 2);\n      } else if (headByte === 0xdb) {\n        // str 32\n        const byteLength = this.lookU32();\n        object = this.decodeString(byteLength, 4);\n      } else if (headByte === 0xdc) {\n        // array 16\n        const size = this.readU16();\n        if (size !== 0) {\n          this.pushArrayState(size);\n          this.complete();\n          continue DECODE;\n        } else {\n          object = [];\n        }\n      } else if (headByte === 0xdd) {\n        // array 32\n        const size = this.readU32();\n        if (size !== 0) {\n          this.pushArrayState(size);\n          this.complete();\n          continue DECODE;\n        } else {\n          object = [];\n        }\n      } else if (headByte === 0xde) {\n        // map 16\n        const size = this.readU16();\n        if (size !== 0) {\n          this.pushMapState(size);\n          this.complete();\n          continue DECODE;\n        } else {\n          object = {};\n        }\n      } else if (headByte === 0xdf) {\n        // map 32\n        const size = this.readU32();\n        if (size !== 0) {\n          this.pushMapState(size);\n          this.complete();\n          continue DECODE;\n        } else {\n          object = {};\n        }\n      } else if (headByte === 0xc4) {\n        // bin 8\n        const size = this.lookU8();\n        object = this.decodeBinary(size, 1);\n      } else if (headByte === 0xc5) {\n        // bin 16\n        const size = this.lookU16();\n        object = this.decodeBinary(size, 2);\n      } else if (headByte === 0xc6) {\n        // bin 32\n        const size = this.lookU32();\n        object = this.decodeBinary(size, 4);\n      } else if (headByte === 0xd4) {\n        // fixext 1\n        object = this.decodeExtension(1, 0);\n      } else if (headByte === 0xd5) {\n        // fixext 2\n        object = this.decodeExtension(2, 0);\n      } else if (headByte === 0xd6) {\n        // fixext 4\n        object = this.decodeExtension(4, 0);\n      } else if (headByte === 0xd7) {\n        // fixext 8\n        object = this.decodeExtension(8, 0);\n      } else if (headByte === 0xd8) {\n        // fixext 16\n        object = this.decodeExtension(16, 0);\n      } else if (headByte === 0xc7) {\n        // ext 8\n        const size = this.lookU8();\n        object = this.decodeExtension(size, 1);\n      } else if (headByte === 0xc8) {\n        // ext 16\n        const size = this.lookU16();\n        object = this.decodeExtension(size, 2);\n      } else if (headByte === 0xc9) {\n        // ext 32\n        const size = this.lookU32();\n        object = this.decodeExtension(size, 4);\n      } else {\n        throw new DecodeError(`Unrecognized type byte: ${prettyByte(headByte)}`);\n      }\n\n      this.complete();\n\n      const stack = this.stack;\n      while (stack.length > 0) {\n        // arrays and maps\n        const state = stack.top()!;\n        if (state.type === STATE_ARRAY) {\n          state.array[state.position] = object;\n          state.position++;\n          if (state.position === state.size) {\n            object = state.array;\n            stack.release(state);\n          } else {\n            continue DECODE;\n          }\n        } else if (state.type === STATE_MAP_KEY) {\n          if (object === \"__proto__\") {\n            throw new DecodeError(\"The key __proto__ is not allowed\");\n          }\n\n          state.key = this.mapKeyConverter(object);\n          state.type = STATE_MAP_VALUE;\n          continue DECODE;\n        } else {\n          // it must be `state.type === State.MAP_VALUE` here\n\n          state.map[state.key!] = object;\n          state.readCount++;\n\n          if (state.readCount === state.size) {\n            object = state.map;\n            stack.release(state);\n          } else {\n            state.key = null;\n            state.type = STATE_MAP_KEY;\n            continue DECODE;\n          }\n        }\n      }\n\n      return object;\n    }\n  }\n\n  private readHeadByte(): number {\n    if (this.headByte === HEAD_BYTE_REQUIRED) {\n      this.headByte = this.readU8();\n      // console.log(\"headByte\", prettyByte(this.headByte));\n    }\n\n    return this.headByte;\n  }\n\n  private complete(): void {\n    this.headByte = HEAD_BYTE_REQUIRED;\n  }\n\n  private readArraySize(): number {\n    const headByte = this.readHeadByte();\n\n    switch (headByte) {\n      case 0xdc:\n        return this.readU16();\n      case 0xdd:\n        return this.readU32();\n      default: {\n        if (headByte < 0xa0) {\n          return headByte - 0x90;\n        } else {\n          throw new DecodeError(`Unrecognized array type byte: ${prettyByte(headByte)}`);\n        }\n      }\n    }\n  }\n\n  private pushMapState(size: number) {\n    if (size > this.maxMapLength) {\n      throw new DecodeError(`Max length exceeded: map length (${size}) > maxMapLengthLength (${this.maxMapLength})`);\n    }\n\n    this.stack.pushMapState(size);\n  }\n\n  private pushArrayState(size: number) {\n    if (size > this.maxArrayLength) {\n      throw new DecodeError(`Max length exceeded: array length (${size}) > maxArrayLength (${this.maxArrayLength})`);\n    }\n\n    this.stack.pushArrayState(size);\n  }\n\n  private decodeString(byteLength: number, headerOffset: number): string | Uint8Array {\n    if (!this.rawStrings || this.stateIsMapKey()) {\n      return this.decodeUtf8String(byteLength, headerOffset);\n    }\n    return this.decodeBinary(byteLength, headerOffset);\n  }\n\n  /**\n   * @throws {@link RangeError}\n   */\n  private decodeUtf8String(byteLength: number, headerOffset: number): string {\n    if (byteLength > this.maxStrLength) {\n      throw new DecodeError(\n        `Max length exceeded: UTF-8 byte length (${byteLength}) > maxStrLength (${this.maxStrLength})`,\n      );\n    }\n\n    if (this.bytes.byteLength < this.pos + headerOffset + byteLength) {\n      throw MORE_DATA;\n    }\n\n    const offset = this.pos + headerOffset;\n    let object: string;\n    if (this.stateIsMapKey() && this.keyDecoder?.canBeCached(byteLength)) {\n      object = this.keyDecoder.decode(this.bytes, offset, byteLength);\n    } else {\n      object = utf8Decode(this.bytes, offset, byteLength);\n    }\n    this.pos += headerOffset + byteLength;\n    return object;\n  }\n\n  private stateIsMapKey(): boolean {\n    if (this.stack.length > 0) {\n      const state = this.stack.top()!;\n      return state.type === STATE_MAP_KEY;\n    }\n    return false;\n  }\n\n  /**\n   * @throws {@link RangeError}\n   */\n  private decodeBinary(byteLength: number, headOffset: number): Uint8Array {\n    if (byteLength > this.maxBinLength) {\n      throw new DecodeError(`Max length exceeded: bin length (${byteLength}) > maxBinLength (${this.maxBinLength})`);\n    }\n\n    if (!this.hasRemaining(byteLength + headOffset)) {\n      throw MORE_DATA;\n    }\n\n    const offset = this.pos + headOffset;\n    const object = this.bytes.subarray(offset, offset + byteLength);\n    this.pos += headOffset + byteLength;\n    return object;\n  }\n\n  private decodeExtension(size: number, headOffset: number): unknown {\n    if (size > this.maxExtLength) {\n      throw new DecodeError(`Max length exceeded: ext length (${size}) > maxExtLength (${this.maxExtLength})`);\n    }\n\n    const extType = this.view.getInt8(this.pos + headOffset);\n    const data = this.decodeBinary(size, headOffset + 1 /* extType */);\n    return this.extensionCodec.decode(data, extType, this.context);\n  }\n\n  private lookU8() {\n    return this.view.getUint8(this.pos);\n  }\n\n  private lookU16() {\n    return this.view.getUint16(this.pos);\n  }\n\n  private lookU32() {\n    return this.view.getUint32(this.pos);\n  }\n\n  private readU8(): number {\n    const value = this.view.getUint8(this.pos);\n    this.pos++;\n    return value;\n  }\n\n  private readI8(): number {\n    const value = this.view.getInt8(this.pos);\n    this.pos++;\n    return value;\n  }\n\n  private readU16(): number {\n    const value = this.view.getUint16(this.pos);\n    this.pos += 2;\n    return value;\n  }\n\n  private readI16(): number {\n    const value = this.view.getInt16(this.pos);\n    this.pos += 2;\n    return value;\n  }\n\n  private readU32(): number {\n    const value = this.view.getUint32(this.pos);\n    this.pos += 4;\n    return value;\n  }\n\n  private readI32(): number {\n    const value = this.view.getInt32(this.pos);\n    this.pos += 4;\n    return value;\n  }\n\n  private readU64(): number {\n    const value = getUint64(this.view, this.pos);\n    this.pos += 8;\n    return value;\n  }\n\n  private readI64(): number {\n    const value = getInt64(this.view, this.pos);\n    this.pos += 8;\n    return value;\n  }\n\n  private readU64AsBigInt(): bigint {\n    const value = this.view.getBigUint64(this.pos);\n    this.pos += 8;\n    return value;\n  }\n\n  private readI64AsBigInt(): bigint {\n    const value = this.view.getBigInt64(this.pos);\n    this.pos += 8;\n    return value;\n  }\n\n  private readF32() {\n    const value = this.view.getFloat32(this.pos);\n    this.pos += 4;\n    return value;\n  }\n\n  private readF64() {\n    const value = this.view.getFloat64(this.pos);\n    this.pos += 8;\n    return value;\n  }\n}\n", "import { Decoder } from \"./Decoder.ts\";\nimport type { DecoderOptions } from \"./Decoder.ts\";\nimport type { SplitUndefined } from \"./context.ts\";\n\n/**\n * It decodes a single MessagePack object in a buffer.\n *\n * This is a synchronous decoding function.\n * See other variants for asynchronous decoding: {@link decodeAsync}, {@link decodeMultiStream}, or {@link decodeArrayStream}.\n *\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\nexport function decode<ContextType = undefined>(\n  buffer: ArrayLike<number> | ArrayBufferView | ArrayBufferLike,\n  options?: DecoderOptions<SplitUndefined<ContextType>>,\n): unknown {\n  const decoder = new Decoder(options);\n  return decoder.decode(buffer);\n}\n\n/**\n * It decodes multiple MessagePack objects in a buffer.\n * This is corresponding to {@link decodeMultiStream}.\n *\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\nexport function decodeMulti<ContextType = undefined>(\n  buffer: ArrayLike<number> | BufferSource,\n  options?: DecoderOptions<SplitUndefined<ContextType>>,\n): Generator<unknown, void, unknown> {\n  const decoder = new Decoder(options);\n  return decoder.decodeMulti(buffer);\n}\n", "// utility for whatwg streams\n\n// The living standard of whatwg streams says\n// ReadableStream is also AsyncIterable, but\n// as of June 2019, no browser implements it.\n// See https://streams.spec.whatwg.org/ for details\nexport type ReadableStreamLike<T> = AsyncIterable<T> | ReadableStream<T>;\n\nexport function isAsyncIterable<T>(object: ReadableStreamLike<T>): object is AsyncIterable<T> {\n  return (object as any)[Symbol.asyncIterator] != null;\n}\n\nexport async function* asyncIterableFromStream<T>(stream: ReadableStream<T>): AsyncIterable<T> {\n  const reader = stream.getReader();\n\n  try {\n    while (true) {\n      const { done, value } = await reader.read();\n      if (done) {\n        return;\n      }\n      yield value;\n    }\n  } finally {\n    reader.releaseLock();\n  }\n}\n\nexport function ensureAsyncIterable<T>(streamLike: ReadableStreamLike<T>): AsyncIterable<T> {\n  if (isAsyncIterable(streamLike)) {\n    return streamLike;\n  } else {\n    return asyncIterableFromStream(streamLike);\n  }\n}\n", "import { Decoder } from \"./Decoder.ts\";\nimport { ensureAsyncIterable } from \"./utils/stream.ts\";\nimport type { DecoderOptions } from \"./Decoder.ts\";\nimport type { ReadableStreamLike } from \"./utils/stream.ts\";\nimport type { SplitUndefined } from \"./context.ts\";\n\n/**\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\nexport async function decodeAsync<ContextType = undefined>(\n  streamLike: ReadableStreamLike<ArrayLike<number> | BufferSource>,\n  options?: DecoderOptions<SplitUndefined<ContextType>>,\n): Promise<unknown> {\n  const stream = ensureAsyncIterable(streamLike);\n  const decoder = new Decoder(options);\n  return decoder.decodeAsync(stream);\n}\n\n/**\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\nexport function decodeArrayStream<ContextType>(\n  streamLike: ReadableStreamLike<ArrayLike<number> | BufferSource>,\n  options?: DecoderOptions<SplitUndefined<ContextType>>,\n): AsyncGenerator<unknown, void, unknown> {\n  const stream = ensureAsyncIterable(streamLike);\n  const decoder = new Decoder(options);\n  return decoder.decodeArrayStream(stream);\n}\n\n/**\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\nexport function decodeMultiStream<ContextType>(\n  streamLike: ReadableStreamLike<ArrayLike<number> | BufferSource>,\n  options?: DecoderOptions<SplitUndefined<ContextType>>,\n): AsyncGenerator<unknown, void, unknown> {\n  const stream = ensureAsyncIterable(streamLike);\n  const decoder = new Decoder(options);\n  return decoder.decodeStream(stream);\n}\n", "// Main Functions:\n\nimport { encode } from \"./encode.ts\";\nexport { encode };\n\nimport { decode, decodeMulti } from \"./decode.ts\";\nexport { decode, decodeMulti };\n\nimport { decodeAsync, decodeArrayStream, decodeMultiStream } from \"./decodeAsync.ts\";\nexport { decodeAsync, decodeArrayStream, decodeMultiStream };\n\nimport { Decoder } from \"./Decoder.ts\";\nexport { Decoder };\nimport type { DecoderOptions } from \"./Decoder.ts\";\nexport type { DecoderOptions };\nimport { DecodeError } from \"./DecodeError.ts\";\nexport { DecodeError };\n\nimport { Encoder } from \"./Encoder.ts\";\nexport { Encoder };\nimport type { EncoderOptions } from \"./Encoder.ts\";\nexport type { EncoderOptions };\n\n// Utilities for Extension Types:\n\nimport { ExtensionCodec } from \"./ExtensionCodec.ts\";\nexport { ExtensionCodec };\nimport type { ExtensionCodecType, ExtensionDecoderType, ExtensionEncoderType } from \"./ExtensionCodec.ts\";\nexport type { ExtensionCodecType, ExtensionDecoderType, ExtensionEncoderType };\nimport { ExtData } from \"./ExtData.ts\";\nexport { ExtData };\n\nimport {\n  EXT_TIMESTAMP,\n  encodeDateToTimeSpec,\n  encodeTimeSpecToTimestamp,\n  decodeTimestampToTimeSpec,\n  encodeTimestampExtension,\n  decodeTimestampExtension,\n} from \"./timestamp.ts\";\nexport {\n  EXT_TIMESTAMP,\n  encodeDateToTimeSpec,\n  encodeTimeSpecToTimestamp,\n  decodeTimestampToTimeSpec,\n  encodeTimestampExtension,\n  decodeTimestampExtension,\n};\n"], "names": [], "sourceRoot": ""}