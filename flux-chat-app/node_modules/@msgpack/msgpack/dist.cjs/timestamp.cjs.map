{"version": 3, "file": "timestampcjs", "sourceRoot": "", "sources": ["../src/timestamp.ts"], "names": [], "mappings": ";;;AAcA,8DA6BC;AAED,oDAWC;AAED,4DAOC;AAED,8DA6BC;AAED,4DAGC;AArGD,kFAAkF;AAClF,qDAA+C;AAC/C,2CAAoD;AAEvC,QAAA,aAAa,GAAG,CAAC,CAAC,CAAC;AAOhC,MAAM,mBAAmB,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,sBAAsB;AACnE,MAAM,mBAAmB,GAAG,WAAW,GAAG,CAAC,CAAC,CAAC,sBAAsB;AAEnE,SAAgB,yBAAyB,CAAC,EAAE,GAAG,EAAE,IAAI,EAAY;IAC/D,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,mBAAmB,EAAE,CAAC;QACxD,6BAA6B;QAC7B,IAAI,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,mBAAmB,EAAE,CAAC;YAC7C,sCAAsC;YACtC,MAAM,EAAE,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;YACrC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;YACvB,OAAO,EAAE,CAAC;QACZ,CAAC;aAAM,CAAC;YACN,yDAAyD;YACzD,MAAM,OAAO,GAAG,GAAG,GAAG,WAAW,CAAC;YAClC,MAAM,MAAM,GAAG,GAAG,GAAG,UAAU,CAAC;YAChC,MAAM,EAAE,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;YACrC,oBAAoB;YACpB,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,OAAO,GAAG,GAAG,CAAC,CAAC,CAAC;YACjD,WAAW;YACX,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;YAC1B,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;SAAM,CAAC;QACN,uDAAuD;QACvD,MAAM,EAAE,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;QAC9B,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QACrC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QACxB,IAAA,iBAAQ,EAAC,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;QACvB,OAAO,EAAE,CAAC;IACZ,CAAC;AACH,CAAC;AAED,SAAgB,oBAAoB,CAAC,IAAU;IAC7C,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;IAC5B,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;IACnC,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IAEtC,uDAAuD;IACvD,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;IACzC,OAAO;QACL,GAAG,EAAE,GAAG,GAAG,SAAS;QACpB,IAAI,EAAE,IAAI,GAAG,SAAS,GAAG,GAAG;KAC7B,CAAC;AACJ,CAAC;AAED,SAAgB,wBAAwB,CAAC,MAAe;IACtD,IAAI,MAAM,YAAY,IAAI,EAAE,CAAC;QAC3B,MAAM,QAAQ,GAAG,oBAAoB,CAAC,MAAM,CAAC,CAAC;QAC9C,OAAO,yBAAyB,CAAC,QAAQ,CAAC,CAAC;IAC7C,CAAC;SAAM,CAAC;QACN,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AAED,SAAgB,yBAAyB,CAAC,IAAgB;IACxD,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IAEzE,iCAAiC;IACjC,QAAQ,IAAI,CAAC,UAAU,EAAE,CAAC;QACxB,KAAK,CAAC,CAAC,CAAC,CAAC;YACP,2BAA2B;YAC3B,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,IAAI,GAAG,CAAC,CAAC;YACf,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;QACvB,CAAC;QACD,KAAK,CAAC,CAAC,CAAC,CAAC;YACP,mCAAmC;YACnC,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC5C,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YACnC,MAAM,GAAG,GAAG,CAAC,iBAAiB,GAAG,GAAG,CAAC,GAAG,WAAW,GAAG,QAAQ,CAAC;YAC/D,MAAM,IAAI,GAAG,iBAAiB,KAAK,CAAC,CAAC;YACrC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;QACvB,CAAC;QACD,KAAK,EAAE,CAAC,CAAC,CAAC;YACR,uDAAuD;YAEvD,MAAM,GAAG,GAAG,IAAA,iBAAQ,EAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC/B,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;QACvB,CAAC;QACD;YACE,MAAM,IAAI,4BAAW,CAAC,gEAAgE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;IACzG,CAAC;AACH,CAAC;AAED,SAAgB,wBAAwB,CAAC,IAAgB;IACvD,MAAM,QAAQ,GAAG,yBAAyB,CAAC,IAAI,CAAC,CAAC;IACjD,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,GAAG,GAAG,QAAQ,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;AAC5D,CAAC;AAEY,QAAA,kBAAkB,GAAG;IAChC,IAAI,EAAE,qBAAa;IACnB,MAAM,EAAE,wBAAwB;IAChC,MAAM,EAAE,wBAAwB;CACjC,CAAC"}