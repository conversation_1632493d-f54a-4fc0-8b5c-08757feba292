# FAL.AI 图像生成配置指南

## 🎨 关于FAL.AI

FAL.AI是一个专业的AI模型托管平台，提供最新的图像生成模型，包括FLUX、Stable Diffusion等。

### ✨ 支持的模型

#### FLUX系列 (推荐)
- **FLUX Pro**: 最高质量，适合专业用途
- **FLUX Dev**: 开发版本，平衡质量和速度
- **FLUX Schnell**: 快速生成，4步即可完成

#### Stable Diffusion系列
- **SDXL**: 经典高质量模型
- **SD3**: 最新版本，更好的文本理解

#### 其他专业模型
- **Playground v2.5**: 艺术风格优化
- **Kandinsky 3**: 独特的艺术风格

## 📋 配置步骤

### 1. 获取FAL.AI API密钥

1. 访问 [FAL.AI官网](https://fal.ai/)
2. 注册账号并登录
3. 进入 [API Keys页面](https://fal.ai/dashboard/keys)
4. 创建新的API密钥
5. 复制生成的API密钥

### 2. 配置环境变量

在项目根目录的 `.env.local` 文件中添加：

```bash
# FAL.AI API Key
FAL_API_KEY=your_actual_fal_api_key_here
```

### 3. 重启应用

```bash
npm run dev
```

## 🚀 功能特色

### 1. 多模型支持
- 🎯 **智能选择**: 根据需求自动推荐最适合的模型
- ⚡ **速度优化**: FLUX Schnell 4步快速生成
- 🎨 **质量保证**: FLUX Pro 专业级图像质量

### 2. 丰富的参数配置
- 📐 **多种尺寸**: 方形、横向、纵向、宽屏等
- 🎛️ **精细控制**: 引导强度、推理步数、随机种子
- 🎨 **风格预设**: 写实、艺术、动漫、卡通、电影等

### 3. 智能工作流
- 🤖 **自动优化**: DeepSeek优化提示词后自动生成图像
- 🔄 **批量生成**: 一次生成多张图像
- 🎯 **重新生成**: 使用相同种子重新生成

## 🎯 使用流程

### 1. 对话式需求收集
```
用户: "我想要一只可爱的小猫"
AI: "什么风格？在什么环境中？"
用户: "写实风格，坐在花园里，黄昏时分"
```

### 2. 自动提示词优化
```
原始: "一只可爱的小猫坐在花园里，写实风格，黄昏时分"
优化: "adorable cat, expressive eyes, fluffy fur texture, sitting pose in blooming garden, natural sunlight filtering through leaves, warm color palette, cozy outdoor environment, professional pet photography, high resolution, detailed whiskers, soft focus background, 8k quality, award-winning composition"
```

### 3. 一键图像生成
- 🎨 选择模型和参数
- ⚡ 快速生成高质量图像
- 🖼️ 实时预览和下载

## ⚙️ 模型选择指南

### 速度优先 → FLUX Schnell
- ⚡ 生成时间: 5-10秒
- 🎯 适用场景: 快速预览、概念验证
- 💰 成本: 最低

### 平衡选择 → FLUX Dev
- ⚖️ 生成时间: 15-30秒
- 🎯 适用场景: 日常使用、内容创作
- 💰 成本: 中等

### 质量优先 → FLUX Pro
- 🏆 生成时间: 30-60秒
- 🎯 适用场景: 专业设计、商业用途
- 💰 成本: 较高

## 🎨 风格预设说明

### 写实风格 (Photorealistic)
```json
{
  "negative_prompt": "cartoon, anime, painting, drawing, sketch, low quality, blurry",
  "guidance_scale": 7.5
}
```

### 艺术风格 (Artistic)
```json
{
  "negative_prompt": "low quality, blurry, distorted",
  "guidance_scale": 8.0
}
```

### 动漫风格 (Anime)
```json
{
  "negative_prompt": "realistic, photograph, low quality, blurry",
  "guidance_scale": 7.0
}
```

### 电影风格 (Cinematic)
```json
{
  "negative_prompt": "low quality, amateur, snapshot, casual",
  "guidance_scale": 8.5
}
```

## 📊 参数说明

### 引导强度 (Guidance Scale)
- **1-5**: 更有创意，可能偏离提示词
- **6-10**: 平衡创意和准确性 (推荐)
- **11-20**: 严格遵循提示词，可能过度拟合

### 推理步数 (Inference Steps)
- **FLUX Schnell**: 4步 (固定)
- **FLUX Dev/Pro**: 20-50步
- **Stable Diffusion**: 20-30步

### 随机种子 (Seed)
- **空白**: 随机生成
- **固定数字**: 可重现相同结果
- **用途**: 微调和变体生成

## 🔍 故障排除

### 常见问题

**Q: 图像生成失败？**
A: 检查FAL API密钥是否正确配置，确保账户有足够余额

**Q: 生成速度很慢？**
A: 尝试使用FLUX Schnell模型，或减少推理步数

**Q: 图像质量不满意？**
A: 尝试使用FLUX Pro模型，增加引导强度，或优化提示词

**Q: 如何获得更好的提示词？**
A: 使用DeepSeek对话功能，让AI帮您优化提示词

### 调试信息
查看浏览器控制台和服务器日志：
```
Generating image with model: flux-schnell (fal-ai/flux/schnell)
Queue update: {...}
Image generation completed in 8.5s
```

## 💰 成本优化建议

### 1. 模型选择
- 🧪 **测试阶段**: 使用FLUX Schnell
- 📝 **内容创作**: 使用FLUX Dev
- 🏢 **商业用途**: 使用FLUX Pro

### 2. 参数优化
- 📐 **合适尺寸**: 避免不必要的大尺寸
- 🔢 **适当步数**: 不要盲目增加推理步数
- 🎯 **批量生成**: 一次生成多张图像更经济

### 3. 提示词优化
- 🎯 **精确描述**: 减少重新生成次数
- 🚫 **负面提示**: 避免不想要的元素
- 🎨 **风格预设**: 使用预设提高成功率

## 🎉 开始使用

配置完成后，您就可以享受：
- 🤖 DeepSeek智能对话优化提示词
- 🎨 FLUX等顶级模型生成高质量图像
- ⚡ 从对话到图像的完整工作流
- 🎯 专业级的图像生成体验

立即配置您的FAL.AI API密钥，体验完整的AI图像生成工作流！
