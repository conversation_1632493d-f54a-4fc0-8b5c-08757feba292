// 简单的API测试脚本
const testChatAPI = async () => {
  try {
    console.log('测试聊天API...');
    
    const response = await fetch('http://localhost:3000/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: '我想要一只可爱的小猫',
        conversationHistory: []
      }),
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ 聊天API测试成功');
      console.log('响应:', data);
    } else {
      console.log('❌ 聊天API测试失败:', response.status);
      const error = await response.text();
      console.log('错误:', error);
    }
  } catch (error) {
    console.log('❌ 聊天API测试出错:', error.message);
  }
};

const testOptimizeAPI = async () => {
  try {
    console.log('\n测试提示词优化API...');
    
    const response = await fetch('http://localhost:3000/api/optimize-prompt', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        requirement: '一只可爱的小猫坐在花园里',
        conversationHistory: ['我想要一只可爱的小猫', '请描述更多细节', '小猫坐在花园里'],
        useAI: false // 使用本地优化避免需要API密钥
      }),
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ 提示词优化API测试成功');
      console.log('优化结果:', data.optimization);
    } else {
      console.log('❌ 提示词优化API测试失败:', response.status);
      const error = await response.text();
      console.log('错误:', error);
    }
  } catch (error) {
    console.log('❌ 提示词优化API测试出错:', error.message);
  }
};

// 运行测试
const runTests = async () => {
  console.log('🚀 开始API测试...\n');
  
  // 等待服务器启动
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  await testOptimizeAPI(); // 先测试不需要OpenAI API的功能
  await testChatAPI();
  
  console.log('\n✨ 测试完成！');
};

// 如果直接运行此文件
if (typeof window === 'undefined') {
  runTests();
}

module.exports = { testChatAPI, testOptimizeAPI };
