# 使用指南

## 🎯 应用概述

这是一个智能的对话式图像生成助手，可以帮助您：
- 通过自然对话理解您的图像需求
- 自动优化提示词以获得更好的生成效果
- 提供专业的图像生成建议

## 🚀 快速开始

### 1. 启动应用
```bash
npm run dev
```
访问 http://localhost:3000

### 2. 开始对话
在输入框中描述您想要的图像，例如：
- "我想要一只可爱的小猫"
- "画一个未来城市的风景"
- "创作一幅油画风格的花朵"

### 3. 与AI对话
AI会询问更多细节来完善您的需求：
- 艺术风格偏好
- 色彩方案
- 构图方式
- 光照效果
- 情绪氛围

### 4. 获得优化提示词
当收集到足够信息后，系统会自动生成优化的提示词，您可以：
- 查看优化说明
- 复制提示词
- 手动编辑调整
- 查看建议参数

## 💡 使用技巧

### 描述技巧
- **具体而非抽象**: "一只橙色的小猫坐在木椅上" 比 "一只猫" 更好
- **包含情感**: "温暖的阳光下" "神秘的月夜" 等
- **指定风格**: "写实摄影" "卡通风格" "油画艺术" 等

### 对话策略
- **逐步细化**: 先说大概想法，然后根据AI的问题补充细节
- **使用快捷提示**: 点击预设的快捷提示词快速开始
- **多轮交流**: 不要害怕多次交流来完善想法

### 提示词优化
- **查看改进说明**: 了解AI做了哪些优化
- **手动调整**: 根据需要编辑生成的提示词
- **参数建议**: 注意AI建议的宽高比、风格等参数

## 🎨 支持的风格类型

### 艺术风格
- **写实风格**: photorealistic, hyperrealistic
- **卡通风格**: cartoon, animated, stylized
- **油画风格**: oil painting, classical art
- **水彩风格**: watercolor, soft brushstrokes
- **数字艺术**: digital art, concept art
- **像素艺术**: pixel art, 8-bit style

### 情绪氛围
- **温暖**: warm, cozy, inviting
- **冷酷**: cold, stark, dramatic
- **神秘**: mysterious, enigmatic
- **浪漫**: romantic, dreamy
- **史诗**: epic, grand, cinematic

### 光照效果
- **自然光**: natural lighting, daylight
- **金色时光**: golden hour, sunset
- **戏剧性**: dramatic lighting, high contrast
- **柔和光**: soft lighting, diffused

## 🔧 配置选项

### 环境变量
```bash
# .env.local
OPENAI_API_KEY=your_api_key_here  # 可选，用于AI增强功能
```

### 演示模式
- 无需API密钥即可体验基本功能
- 使用内置的智能响应系统
- 本地提示词优化算法

## 📱 界面功能

### 聊天界面
- **消息历史**: 查看完整的对话记录
- **实时输入**: 支持多行文本和快捷键
- **状态指示**: 显示AI处理状态

### 提示词预览
- **优化对比**: 查看原始vs优化后的提示词
- **改进说明**: 了解具体的优化内容
- **参数建议**: 获得生成参数建议
- **一键复制**: 快速复制优化后的提示词

### 快捷功能
- **快捷提示**: 预设的常用提示词
- **重新优化**: 基于当前对话重新生成提示词
- **新对话**: 清空历史开始新的对话

## 🎯 最佳实践

### 1. 渐进式描述
```
用户: "我想要一只猫"
AI: "什么风格的猫？在什么环境中？"
用户: "写实风格，坐在花园里"
AI: "什么时间？什么光照？"
用户: "黄昏时分，温暖的阳光"
```

### 2. 风格一致性
- 确定主要风格后保持一致
- 避免混合冲突的风格元素
- 考虑整体视觉和谐

### 3. 细节平衡
- 提供足够细节但不过度复杂
- 重点突出最重要的元素
- 保持描述的逻辑性

## 🔍 故障排除

### 常见问题

**Q: AI回复很慢或没有回复？**
A: 检查网络连接，或者使用演示模式（无需API密钥）

**Q: 提示词优化效果不理想？**
A: 尝试提供更多具体细节，或手动编辑优化结果

**Q: 如何获得更好的图像生成效果？**
A: 使用优化后的提示词，注意建议的参数设置

### 技术支持
- 查看浏览器控制台的错误信息
- 确保使用现代浏览器
- 检查网络连接状态

## 🚀 进阶使用

### 自定义风格
可以在对话中描述特定的艺术家风格或技术要求：
- "梵高风格的星空"
- "宫崎骏动画风格"
- "赛博朋克风格的城市"

### 技术参数
可以指定具体的技术要求：
- "4K高清分辨率"
- "16:9宽屏比例"
- "景深效果"
- "HDR光照"

### 组合元素
可以组合多个元素创造复杂场景：
- 主体 + 环境 + 风格 + 光照 + 情绪

## 📈 更新日志

### v1.0.0
- ✅ 基础对话功能
- ✅ 提示词优化
- ✅ 演示模式
- ✅ 响应式界面
- ✅ 多风格支持
