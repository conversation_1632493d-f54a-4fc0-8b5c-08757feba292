#!/usr/bin/env node

// 演示脚本 - 展示AI图像生成助手的功能
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('🎨 AI图像生成助手演示');
console.log('=====================================');
console.log('');
console.log('这个演示将展示如何通过对话优化图像生成提示词');
console.log('');

// 模拟对话场景
const demoScenarios = [
  {
    title: '场景1: 可爱动物',
    userInput: '我想要一只可爱的小猫',
    aiResponse: '听起来很有趣！您想要什么风格的小猫图像呢？比如：\n• 写实风格的摄影作品\n• 可爱的卡通风格\n• 油画艺术风格\n• 水彩画风格\n\n另外，您希望小猫在什么环境中呢？',
    userFollow: '写实风格，坐在花园里，黄昏时分',
    optimizedPrompt: 'a cute cat sitting in a garden, photorealistic, hyperrealistic, golden hour lighting, warm sunset colors, detailed fur texture, professional photography, shallow depth of field, cozy atmosphere, highly detailed, sharp focus, 8k resolution, award winning'
  },
  {
    title: '场景2: 风景画',
    userInput: '我想要一个未来城市的风景',
    aiResponse: '未来城市很有创意！请告诉我更多细节：\n• 您想要什么时间？（白天、夜晚、黄昏等）\n• 希望是什么风格？（赛博朋克、简约未来、科幻等）\n• 想要什么样的色彩氛围？\n• 有特定的建筑特征吗？',
    userFollow: '夜晚，赛博朋克风格，霓虹灯，高楼大厦',
    optimizedPrompt: 'futuristic cyberpunk cityscape at night, neon lights, towering skyscrapers, holographic advertisements, flying cars, rain-soaked streets reflecting neon colors, dramatic lighting, cinematic composition, highly detailed, digital art, concept art, trending on artstation, 8k resolution'
  },
  {
    title: '场景3: 艺术创作',
    userInput: '我想要一幅油画风格的花朵',
    aiResponse: '油画风格很经典！为了创作出最佳效果，请告诉我：\n• 什么类型的花朵？（玫瑰、向日葵、郁金香等）\n• 希望是单朵还是花束？\n• 想要什么样的背景？\n• 偏好什么色彩搭配？\n• 有特定的艺术家风格参考吗？',
    userFollow: '向日葵，单朵，简单背景，温暖色调，梵高风格',
    optimizedPrompt: 'a single sunflower, oil painting style, Van Gogh inspired, warm color palette, yellow and orange tones, textured brushstrokes, impasto technique, simple background, classical art, painterly style, highly detailed, masterpiece, museum quality'
  }
];

async function runDemo() {
  console.log('请选择一个演示场景：');
  console.log('');
  
  demoScenarios.forEach((scenario, index) => {
    console.log(`${index + 1}. ${scenario.title}`);
  });
  
  console.log('0. 退出演示');
  console.log('');
  
  const choice = await askQuestion('请输入选择 (0-3): ');
  const scenarioIndex = parseInt(choice) - 1;
  
  if (choice === '0') {
    console.log('感谢使用演示！');
    rl.close();
    return;
  }
  
  if (scenarioIndex >= 0 && scenarioIndex < demoScenarios.length) {
    await playScenario(demoScenarios[scenarioIndex]);
  } else {
    console.log('无效选择，请重试。');
    await runDemo();
  }
}

async function playScenario(scenario) {
  console.log('\n' + '='.repeat(50));
  console.log(`📱 ${scenario.title}`);
  console.log('='.repeat(50));
  
  // 第一轮对话
  console.log('\n👤 用户:');
  console.log(scenario.userInput);
  
  await sleep(1000);
  
  console.log('\n🤖 AI助手:');
  console.log(scenario.aiResponse);
  
  await askQuestion('\n按回车继续...');
  
  // 第二轮对话
  console.log('\n👤 用户:');
  console.log(scenario.userFollow);
  
  await sleep(1000);
  
  console.log('\n🤖 AI助手:');
  console.log('很好！基于我们的对话，我已经为您优化了提示词：');
  
  await sleep(1000);
  
  // 显示优化结果
  console.log('\n' + '✨ 优化后的提示词 ✨'.padStart(30));
  console.log('-'.repeat(60));
  console.log(scenario.optimizedPrompt);
  console.log('-'.repeat(60));
  
  console.log('\n📊 优化改进:');
  console.log('• 添加了专业术语和技术参数');
  console.log('• 增强了风格描述的准确性');
  console.log('• 优化了光照和色彩描述');
  console.log('• 添加了质量增强关键词');
  
  console.log('\n💡 建议参数:');
  console.log('• 宽高比: 16:9');
  console.log('• 质量: 高');
  console.log('• 风格: 根据描述自动调整');
  
  await askQuestion('\n按回车返回主菜单...');
  await runDemo();
}

function askQuestion(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 启动演示
console.log('正在启动演示...\n');
runDemo().catch(console.error);
